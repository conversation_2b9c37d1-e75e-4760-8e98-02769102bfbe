# Mine Simulator

Un simulateur de gestion de mine médiévale fantasy avec des mécaniques de jeu complexes et une interface utilisateur interactive.

![Dashboard](Examples/Dashboard%20example.png)

## 🌟 Fonctionnalités

- **Gestion de ressources** : Nourriture, matériaux et trésor
- **Gestion de personnel** : Différents types de travailleurs (mineurs, fermiers, ingénieurs, etc.)
- **Système de moral** : Affecte la productivité des travailleurs
- **Cycles de jeu** : Progression temporelle avec saisons et années
- **Modificateurs dynamiques** : Effets qui influencent la production et la consommation
- **Interface utilisateur intuitive** : Tableaux de bord et onglets thématiques

## 📋 Prérequis

- Node.js (v14+)
- npm ou yarn

## 🚀 Installation

1. Clonez le dépôt :
```bash
git clone https://github.com/MichaelGhelfi/mine-simulator.git
cd mine-simulator
```

2. Installez les dépendances pour le client et le serveur :
```bash
# Installation des dépendances du serveur
cd server
npm install

# Installation des dépendances du client
cd ../client
npm install
```

## 🎮 Démarrage

1. Démarrez le serveur backend :
```bash
cd server
npm start
```

2. Dans un autre terminal, démarrez le client frontend :
```bash
cd client
npm start
```

3. Accédez à l'application dans votre navigateur à l'adresse `http://localhost:3000`

## 🧪 Tests

Le projet inclut des tests unitaires pour le frontend et le backend :

```bash
# Exécuter les tests du serveur
cd server
npm test

# Exécuter les tests du client
cd client
npm test
```

## 📚 Structure du projet

- `/client` - Application frontend React
- `/server` - API backend Node.js
- `/Examples` - Images et exemples de fonctionnalités

## 🛠️ Technologies utilisées

### Frontend
- React
- CSS
- Fetch API

### Backend
- Node.js
- Express
- SQLite

## 📝 Fonctionnalités à venir

- Système de bâtiments
- Événements aléatoires
- Recherche technologique
- Mode multijoueur en ligne

## 📜 Licence

Ce projet est sous licence "Tous droits réservés" - voir le fichier [LICENSE](LICENSE) pour plus de détails.

## 👥 Contribution

Les contributions sont les bienvenues ! N'hésitez pas à ouvrir une issue ou une pull request.
