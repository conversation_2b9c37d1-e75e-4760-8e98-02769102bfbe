# Mine Simulator - Enhanced Architecture

This document provides an overview of the enhanced architecture implemented in the Mine Simulator application.

## Table of Contents

1. [Service Layer](#service-layer)
2. [Calculation Functions](#calculation-functions)
3. [Redux Integration](#redux-integration)
4. [API Request Handling](#api-request-handling)
5. [Error Handling](#error-handling)
6. [WebSocket Integration](#websocket-integration)
7. [Hooks](#hooks)
8. [Best Practices](#best-practices)

## Service Layer

The service layer has been standardized to provide a consistent interface for all API interactions:

### BaseService

- Provides common methods for HTTP requests
- Implements request deduplication and caching
- Handles errors consistently

### ServiceRegistry

- Centralizes access to all services
- Ensures services are properly initialized
- Provides a single point of access for all services

### ApiEndpoint

- Provides a single point of access for all API endpoints
- Implements request deduplication, caching, and error handling
- Standardizes API request patterns across the application

### Domain-Specific Services

- GameService: Manages game state
- ModifiersService: Manages modifiers
- InhabitantsService: Manages inhabitants
- CalculationService: Centralizes calculations

## Calculation Functions

Calculation functions have been centralized and optimized:

### CalculationServiceEnhanced

- Wraps the original CalculationService with memoization
- Ensures consistent calculations between client and server
- Implements memoization for expensive calculations

### Memoization Utilities

- memoize: Basic memoization
- memoizeWithMaxSize: Memoization with a maximum cache size
- memoizeWithExpiration: Memoization with time-based expiration

## Redux Integration

Redux integration has been improved for better performance and maintainability:

### Optimized Selectors

- All selectors use createSelector for memoization
- Selectors are organized by domain
- Derived selectors are properly memoized

### Enhanced Redux Hooks

- useReduxEnhanced: Enhanced hook for Redux integration
- withReduxEnhanced: HOC for Redux integration

### ApiEndpoint Service

- Handles API requests with deduplication and caching
- Manages request lifecycle
- Provides consistent error handling

## API Request Handling

API request handling has been standardized:

### Request Deduplication

- Prevents duplicate requests
- Reuses in-flight requests
- Prioritizes critical requests

### Caching

- Implements a consistent caching strategy
- Provides cache invalidation patterns
- Optimizes performance

### Request Tracking

- Tracks API requests
- Provides debugging tools
- Monitors request patterns

## Error Handling

Error handling has been standardized:

### Error Types

- API_ERROR: API-related errors
- NETWORK_ERROR: Network-related errors
- VALIDATION_ERROR: Validation-related errors
- WEBSOCKET_ERROR: WebSocket-related errors
- UNKNOWN_ERROR: Unknown errors

### Error Severity

- INFO: Informational messages
- WARNING: Warning messages
- ERROR: Error messages
- CRITICAL: Critical error messages

### Error Boundary

- Catches JavaScript errors
- Provides fallback UI
- Logs errors

## WebSocket Integration

WebSocket integration has been improved:

### WebSocket Middleware

- Handles WebSocket connection
- Manages message sending/receiving
- Provides reconnection logic

### Enhanced WebSocket Hook

- useWebSocketEnhanced: Enhanced hook for WebSocket integration
- Provides consistent error handling
- Manages connection lifecycle

## Hooks

Enhanced hooks have been created for common patterns:

### API Hooks

- useApiEndpoint: Hook for using the enhanced API endpoint service

### Redux Hooks

- useReduxEnhanced: Enhanced hook for Redux integration
- useGameDataEnhanced: Enhanced hook for accessing game data

### Calculation Hook

- useCalculation: Hook for using the enhanced calculation service

### Service Hooks

- useService: Hook for using the enhanced service registry
- useGameService: Hook for using the game service
- useModifiersService: Hook for using the modifiers service
- useInhabitantsService: Hook for using the inhabitants service
- useCalculationService: Hook for using the calculation service
- useCacheService: Hook for using the cache service

### Error Handling Hook

- useErrorHandling: Hook for using the enhanced error handling

### WebSocket Hook

- useWebSocketEnhanced: Enhanced hook for WebSocket integration

## Best Practices

### Performance Optimization

- Use memoized selectors
- Implement request deduplication
- Use caching for expensive calculations

### Error Handling

- Use the error handling utilities
- Implement error boundaries
- Log errors consistently

### API Requests

- Use the API middleware
- Implement proper caching
- Handle errors consistently

### Redux Integration

- Use the enhanced Redux hooks
- Implement proper memoization
- Follow the Redux patterns

### WebSocket Integration

- Use the enhanced WebSocket hook
- Handle connection lifecycle
- Manage messages properly
