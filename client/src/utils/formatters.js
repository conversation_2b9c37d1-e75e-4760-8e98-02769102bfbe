/**
 * Format a number as a percentage
 * @param {number} value - The value to format
 * @param {number} decimals - Number of decimal places (default: 1)
 * @returns {string} - Formatted percentage string
 */
export const formatPercent = (value, decimals = 1) => {
  if (value === undefined || value === null) return '0.0%';
  
  // Convert to percentage and format with specified decimal places
  const percentage = value * 100;
  return `${percentage.toFixed(decimals)}%`;
};

/**
 * Format a number with specified decimal places
 * @param {number} value - The value to format
 * @param {number} decimals - Number of decimal places (default: 1)
 * @returns {string} - Formatted number string
 */
export const formatNumber = (value, decimals = 1) => {
  if (value === undefined || value === null) return '0.0';
  
  // Format with specified decimal places
  return value.toFixed(decimals);
};

/**
 * Format a date in fantasy style (e.g., "Year 1349")
 * @param {string} dateString - Date string in ISO format
 * @returns {string} - Formatted date string
 */
export const formatFantasyDate = (dateString) => {
  if (!dateString) return '';
  
  const date = new Date(dateString);
  const year = date.getFullYear();
  
  return `Year ${year}`;
};

/**
 * Format a currency value
 * @param {number} value - The value to format
 * @param {string} currency - Currency symbol (default: 'or')
 * @returns {string} - Formatted currency string
 */
export const formatCurrency = (value, currency = 'or') => {
  if (value === undefined || value === null) return `0.0 ${currency}`;
  
  // Format with 1 decimal place
  return `${value.toFixed(1)} ${currency}`;
};
