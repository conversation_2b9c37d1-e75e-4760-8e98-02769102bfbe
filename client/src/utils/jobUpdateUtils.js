/**
 * Utilitaires pour déterminer quels onglets doivent être mis à jour en fonction des changements de métier
 */

// Mapping des métiers aux onglets qu'ils affectent
const JOB_TAB_MAPPING = {
  // ID des métiers (basés sur la base de données)
  1: ['mining', 'materials'], // Miner -> Mining Tab et Materials Tab
  2: ['food'],   // Farmer -> Food Tab
  3: ['health'], // Healer -> Health Tab
  4: ['materials'], // Worker -> Materials Tab
  5: ['research'], // Scholar -> Research Tab
  6: ['trading'], // Craftsman -> Trading Tab
  7: ['justice'], // Protector -> Justice Tab
  8: ['defense'], // Soldier -> Defense Tab
  9: ['mining'], // Engineer -> Mining Tab
};

// Noms des métiers aux onglets qu'ils affectent (alternative à l'ID)
const JOB_NAME_TAB_MAPPING = {
  'Miner': ['mining', 'materials'],
  'Farmer': ['food'],
  'Healer': ['health'],
  'Worker': ['materials'],
  'Scholar': ['research'],
  'Craftsman': ['trading'],
  'Protector': ['justice'],
  'Soldier': ['defense'],
  'Engineer': ['mining'],
};

/**
 * Détermine quels onglets doivent être mis à jour en fonction du métier modifié
 * @param {number|string} jobId - ID ou nom du métier modifié
 * @returns {Array} - Liste des onglets à mettre à jour
 */
export const getTabsToUpdate = (jobId, jobName) => {
  // Si nous avons un ID de métier, utiliser le mapping par ID
  if (jobId && JOB_TAB_MAPPING[jobId]) {
    return JOB_TAB_MAPPING[jobId];
  }

  // Si nous avons un nom de métier, utiliser le mapping par nom
  if (jobName && JOB_NAME_TAB_MAPPING[jobName]) {
    return JOB_NAME_TAB_MAPPING[jobName];
  }

  // Si nous ne pouvons pas déterminer les onglets à mettre à jour, retourner une liste vide
  return [];
};

/**
 * Détermine si un changement de statut de maladie affecte un onglet spécifique
 * @param {Object} inhabitant - L'habitant modifié
 * @param {boolean} isSicknessChanged - Indique si le statut de maladie a changé
 * @returns {Array} - Liste des onglets à mettre à jour
 */
export const getTabsToUpdateForSickness = (inhabitant, isSicknessChanged) => {
  if (!isSicknessChanged) return [];

  // Si le statut de maladie a changé, déterminer les onglets à mettre à jour en fonction du métier
  return getTabsToUpdate(inhabitant.job_id, inhabitant.job_name);
};

/**
 * Détermine tous les onglets à mettre à jour en fonction des changements
 * @param {Object} inhabitant - L'habitant modifié
 * @param {boolean} isJobChanged - Indique si le métier a changé
 * @param {boolean} isSicknessChanged - Indique si le statut de maladie a changé
 * @param {number} oldJobId - Ancien ID de métier (si le métier a changé)
 * @param {string} oldJobName - Ancien nom de métier (si le métier a changé)
 * @returns {Array} - Liste des onglets à mettre à jour
 */
export const getAllTabsToUpdate = (inhabitant, isJobChanged, isSicknessChanged, oldJobId, oldJobName) => {
  const tabsToUpdate = new Set();

  // Si le métier a changé, ajouter les onglets affectés par l'ancien et le nouveau métier
  if (isJobChanged) {
    getTabsToUpdate(oldJobId, oldJobName).forEach(tab => tabsToUpdate.add(tab));
    getTabsToUpdate(inhabitant.job_id, inhabitant.job_name).forEach(tab => tabsToUpdate.add(tab));
  }

  // Si le statut de maladie a changé, ajouter les onglets affectés par le métier actuel
  if (isSicknessChanged) {
    getTabsToUpdate(inhabitant.job_id, inhabitant.job_name).forEach(tab => tabsToUpdate.add(tab));
  }

  // Toujours mettre à jour l'onglet population
  tabsToUpdate.add('population');

  return Array.from(tabsToUpdate);
};
