/**
 * Memoization utility
 * 
 * Provides a function to memoize expensive calculations.
 * Caches results based on input arguments.
 */

/**
 * Creates a memoized version of a function
 * @param {Function} fn - Function to memoize
 * @param {Function} resolver - Function to resolve cache key from arguments (optional)
 * @returns {Function} - Memoized function
 */
export function memoize(fn, resolver) {
  const cache = new Map();
  
  // Return memoized function
  return function memoized(...args) {
    // Get cache key
    const key = resolver ? resolver(...args) : JSON.stringify(args);
    
    // Check if result is cached
    if (cache.has(key)) {
      return cache.get(key);
    }
    
    // Calculate result
    const result = fn.apply(this, args);
    
    // Cache result
    cache.set(key, result);
    
    // Return result
    return result;
  };
}

/**
 * Creates a memoized version of a function with a maximum cache size
 * @param {Function} fn - Function to memoize
 * @param {number} maxSize - Maximum cache size
 * @param {Function} resolver - Function to resolve cache key from arguments (optional)
 * @returns {Function} - Memoized function
 */
export function memoizeWithMaxSize(fn, maxSize = 100, resolver) {
  const cache = new Map();
  const keys = [];
  
  // Return memoized function
  return function memoized(...args) {
    // Get cache key
    const key = resolver ? resolver(...args) : JSON.stringify(args);
    
    // Check if result is cached
    if (cache.has(key)) {
      return cache.get(key);
    }
    
    // Calculate result
    const result = fn.apply(this, args);
    
    // Cache result
    cache.set(key, result);
    keys.push(key);
    
    // Remove oldest entry if cache is too large
    if (keys.length > maxSize) {
      const oldestKey = keys.shift();
      cache.delete(oldestKey);
    }
    
    // Return result
    return result;
  };
}

/**
 * Creates a memoized version of a function with a time-based expiration
 * @param {Function} fn - Function to memoize
 * @param {number} maxAge - Maximum age of cache entries in milliseconds
 * @param {Function} resolver - Function to resolve cache key from arguments (optional)
 * @returns {Function} - Memoized function
 */
export function memoizeWithExpiration(fn, maxAge = 5000, resolver) {
  const cache = new Map();
  const timestamps = new Map();
  
  // Return memoized function
  return function memoized(...args) {
    // Get cache key
    const key = resolver ? resolver(...args) : JSON.stringify(args);
    
    // Check if result is cached and not expired
    const now = Date.now();
    if (cache.has(key) && now - timestamps.get(key) < maxAge) {
      return cache.get(key);
    }
    
    // Calculate result
    const result = fn.apply(this, args);
    
    // Cache result
    cache.set(key, result);
    timestamps.set(key, now);
    
    // Return result
    return result;
  };
}

export default memoize;
