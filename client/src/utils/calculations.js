/**
 * Calculate moral modifier from moral value
 * @param {Number} moralValue - Moral value (1.0 is neutral)
 * @returns {Number} - Moral modifier as a decimal (0.0 is neutral)
 */
export const calculateMoralModifier = (moralValue) => {
  // Convert from multiplier to modifier
  return moralValue - 1.0;
};

/**
 * Format number as percentage
 * @param {Number} value - Value to format
 * @returns {String} - Formatted percentage
 */
export const formatPercent = (value) => {
  if (value === undefined || value === null) return '0.0%';
  return `${(value * 100).toFixed(1)}%`;
};

/**
 * Format number with 1 decimal place
 * @param {Number} num - Number to format
 * @returns {String} - Formatted number
 */
export const formatNumber = (num) => {
  if (num === undefined || num === null) return '0.0';
  return parseFloat(num).toFixed(1);
};

/**
 * Get moral emoji based on moral title
 * @param {String} moralTitle - Moral title
 * @returns {String} - Emoji representing moral state
 */
export const getMoralEmoji = (moralTitle) => {
  if (!moralTitle) return '😐';

  if (moralTitle.includes('Rébellion')) return '🤬';
  if (moralTitle.includes('En colère')) return '😠';
  if (moralTitle.includes('Triste')) return '😢';
  if (moralTitle.includes('Mécontent')) return '😒';
  if (moralTitle.includes('Neutre')) return '😐';
  if (moralTitle.includes('Content')) return '😊';
  if (moralTitle.includes('Epanoui')) return '😄';
  if (moralTitle.includes('Heureux')) return '😁';
  if (moralTitle.includes('Ecstatique')) return '🥳';

  return '😐'; // Default value
};
