/**
 * Centralized Error Handling Utility
 *
 * Provides standardized error handling across the application.
 * Includes error logging, formatting, and notification.
 */
import React from 'react';

// Error types
export const ERROR_TYPES = {
  API: 'API_ERROR',
  NETWORK: 'NETWORK_ERROR',
  VALIDATION: 'VAL<PERSON>ATION_ERROR',
  AUTHENTICATION: 'AUTHENTICATION_ERROR',
  AUTHORIZATION: 'AUTHORIZATION_ERROR',
  WEBSOCKET: 'WEBSOCKET_ERROR',
  UNKNOWN: 'UNKNOWN_ERROR'
};

// Error severity levels
export const ERROR_SEVERITY = {
  INFO: 'info',
  WARNING: 'warning',
  ERROR: 'error',
  CRITICAL: 'critical'
};

/**
 * Format error for display
 * @param {Error|Object|string} error - Error object or message
 * @param {string} type - Error type
 * @param {string} severity - Error severity
 * @returns {Object} - Formatted error
 */
export const formatError = (error, type = ERROR_TYPES.UNKNOWN, severity = ERROR_SEVERITY.ERROR) => {
  // Handle different error types
  let formattedError = {
    message: 'An unknown error occurred',
    type,
    severity,
    timestamp: Date.now(),
    details: null,
    originalError: error
  };

  if (typeof error === 'string') {
    formattedError.message = error;
  } else if (error instanceof Error) {
    formattedError.message = error.message;
    formattedError.details = error.stack;
  } else if (error && typeof error === 'object') {
    if (error.message) {
      formattedError.message = error.message;
    }
    if (error.details) {
      formattedError.details = error.details;
    }
    if (error.type) {
      formattedError.type = error.type;
    }
    if (error.severity) {
      formattedError.severity = error.severity;
    }
  }

  return formattedError;
};

/**
 * Log error to console
 * @param {Object} error - Formatted error
 */
export const logError = (error) => {
  const { message, type, severity, details } = formatError(error);

  // Log based on severity
  switch (severity) {
    case ERROR_SEVERITY.INFO:
      console.info(`[${type}] ${message}`, details);
      break;
    case ERROR_SEVERITY.WARNING:
      console.warn(`[${type}] ${message}`, details);
      break;
    case ERROR_SEVERITY.CRITICAL:
      console.error(`[CRITICAL] [${type}] ${message}`, details);
      break;
    case ERROR_SEVERITY.ERROR:
    default:
      console.error(`[${type}] ${message}`, details);
      break;
  }
};

/**
 * Handle API error
 * @param {Error|Object|string} error - Error object or message
 * @param {Function} dispatch - Redux dispatch function
 * @param {Object} options - Additional options
 * @returns {Object} - Formatted error
 */
export const handleApiError = (error, dispatch = null, options = {}) => {
  // Format error
  const formattedError = formatError(error, ERROR_TYPES.API);

  // Log error
  logError(formattedError);

  // Dispatch error action if dispatch function is provided
  if (dispatch && options.action) {
    dispatch(options.action(formattedError));
  }

  return formattedError;
};

/**
 * Handle network error
 * @param {Error|Object|string} error - Error object or message
 * @param {Function} dispatch - Redux dispatch function
 * @param {Object} options - Additional options
 * @returns {Object} - Formatted error
 */
export const handleNetworkError = (error, dispatch = null, options = {}) => {
  // Format error
  const formattedError = formatError(error, ERROR_TYPES.NETWORK);

  // Log error
  logError(formattedError);

  // Dispatch error action if dispatch function is provided
  if (dispatch && options.action) {
    dispatch(options.action(formattedError));
  }

  return formattedError;
};

/**
 * Handle WebSocket error
 * @param {Error|Object|string} error - Error object or message
 * @param {Function} dispatch - Redux dispatch function
 * @param {Object} options - Additional options
 * @returns {Object} - Formatted error
 */
export const handleWebSocketError = (error, dispatch = null, options = {}) => {
  // Format error
  const formattedError = formatError(error, ERROR_TYPES.WEBSOCKET);

  // Log error
  logError(formattedError);

  // Dispatch error action if dispatch function is provided
  if (dispatch && options.action) {
    dispatch(options.action(formattedError));
  }

  return formattedError;
};

/**
 * Create error boundary component
 * @param {React.Component} Component - Component to wrap
 * @param {Function} fallback - Fallback component
 * @returns {React.Component} - Error boundary component
 */
export const withErrorBoundary = (Component, fallback) => {
  return class ErrorBoundary extends React.Component {
    constructor(props) {
      super(props);
      this.state = { hasError: false, error: null };
    }

    static getDerivedStateFromError(error) {
      return { hasError: true, error };
    }

    componentDidCatch(error, errorInfo) {
      logError({
        message: error.message,
        type: ERROR_TYPES.UNKNOWN,
        severity: ERROR_SEVERITY.ERROR,
        details: errorInfo.componentStack
      });
    }

    render() {
      if (this.state.hasError) {
        return fallback ? fallback(this.state.error) : (
          <div className="error-boundary">
            <h2>Something went wrong.</h2>
            <p>{this.state.error && this.state.error.message}</p>
            <button onClick={() => this.setState({ hasError: false, error: null })}>
              Try again
            </button>
          </div>
        );
      }

      return <Component {...this.props} />;
    }
  };
};

const errorHandler = {
  formatError,
  logError,
  handleApiError,
  handleNetworkError,
  handleWebSocketError,
  withErrorBoundary,
  ERROR_TYPES,
  ERROR_SEVERITY
};

export default errorHandler;
