/**
 * Translate race from English to French
 * @param {String} race - Race in English
 * @returns {String} - Race in French
 */
export const translateRace = (race) => {
  if (!race) return 'Inconnue';
  
  const translations = {
    'Human': '<PERSON><PERSON><PERSON>',
    'Elf': '<PERSON><PERSON>',
    'Dwarf': 'Nain',
    '<PERSON><PERSON>': '<PERSON><PERSON>',
    '<PERSON>no<PERSON>': '<PERSON>no<PERSON>',
    'Half-Elf': '<PERSON><PERSON>-<PERSON><PERSON>',
    'Half-Orc': 'Demi-<PERSON><PERSON>',
    '<PERSON>born': 'N<PERSON>-<PERSON>',
    'Tiefling': 'Tief<PERSON><PERSON>',
    'Orc': 'Or<PERSON>',
    'Goblin': 'Gobelin',
    'Hobgoblin': 'Hobgo<PERSON>in',
    '<PERSON><PERSON><PERSON>': '<PERSON><PERSON><PERSON>',
    'Lizardfolk': '<PERSON><PERSON>-<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>': '<PERSON><PERSON><PERSON>',
    'Tortle': 'Tortu<PERSON>',
    'Triton': 'Triton',
    '<PERSON>-Ti': '<PERSON>-Ti',
    '<PERSON><PERSON><PERSON>': '<PERSON><PERSON><PERSON>',
    'Firbolg': '<PERSON>rbol<PERSON>',
    'Golia<PERSON>': '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON>': '<PERSON><PERSON>',
    '<PERSON><PERSON><PERSON><PERSON><PERSON>': '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    '<PERSON><PERSON>': '<PERSON><PERSON>',
    'Bugbear': 'Bugbear',
    'Centaur': 'Ce<PERSON><PERSON>',
    'Changeling': 'Changelin',
    '<PERSON>lashtar': 'Kalashtar',
    'Minotaur': 'Minotaure',
    'Shifter': 'Métamorphe',
    'Warforged': 'Forgé-de-guerre',
    'Gith': 'Gith',
    'Satyr': 'Satyre',
    'Fairy': 'Fée',
    'Harengon': 'Harengon',
    'Owlin': 'Hiboulin',
    '<PERSON>in': 'Léonin',
    'Loxodon': 'Loxodon',
    'Simic Hybrid': 'Hybride Simic',
    'Vedalken': 'Vedalken',
    'Verdan': 'Verdan',
    'Locathah': 'Locathah',
    'Grung': 'Grung',
    'Kuo-Toa': 'Kuo-Toa',
    'Thri-Kreen': 'Thri-Kreen',
    'Autognome': 'Autognome',
    'Plasmoid': 'Plasmoïde',
    'Astral Elf': 'Elfe Astral',
    'Giff': 'Giff',
    'Hadozee': 'Hadozee',
    'Duergar': 'Duergar',
    'Drow': 'Drow',
    'Svirfneblin': 'Svirfneblin',
    'Shadar-Kai': 'Shadar-Kai',
    'Eladrin': 'Eladrin',
    'Sea Elf': 'Elfe des Mers',
    'Githyanki': 'Githyanki',
    'Githzerai': 'Githzerai',
    'Feral Tiefling': 'Tieffelin Sauvage',
    'Variant Human': 'Humain Variant',
    'Mountain Dwarf': 'Nain des Montagnes',
    'Hill Dwarf': 'Nain des Collines',
    'High Elf': 'Haut-Elfe',
    'Wood Elf': 'Elfe des Bois',
    'Lightfoot Halfling': 'Halfelin Pied-Léger',
    'Stout Halfling': 'Halfelin Robuste',
    'Forest Gnome': 'Gnome des Forêts',
    'Rock Gnome': 'Gnome des Roches',
    'Deep Gnome': 'Gnome des Profondeurs',
    'Air Genasi': 'Genasi de l\'Air',
    'Earth Genasi': 'Genasi de la Terre',
    'Fire Genasi': 'Genasi du Feu',
    'Water Genasi': 'Genasi de l\'Eau',
    'Wildhunt Shifter': 'Métamorphe Chasseur Sauvage',
    'Beasthide Shifter': 'Métamorphe Peau-Bête',
    'Longtooth Shifter': 'Métamorphe Longue-Dent',
    'Swiftstride Shifter': 'Métamorphe Pas-Rapide'
  };
  
  return translations[race] || race;
};

/**
 * Translate job from English to French
 * @param {String} job - Job in English
 * @returns {String} - Job in French
 */
export const translateJob = (job) => {
  if (!job) return 'Aucun';
  
  const translations = {
    'Worker': 'Ouvrier',
    'Farmer': 'Fermier',
    'Miner': 'Mineur',
    'Scholar': 'Érudit',
    'Healer': 'Guérisseur',
    'Trader': 'Commerçant',
    'Protector': 'Protecteur',
    'Soldier': 'Soldat',
    'Specialist': 'Spécialiste'
  };
  
  return translations[job] || job;
};

/**
 * Format date in fantasy style
 * @param {String} dateString - ISO date string
 * @returns {String} - Formatted date
 */
export const formatFantasyDate = (dateString) => {
  if (!dateString) return 'Inconnue';
  
  const date = new Date(dateString);
  
  // Fantasy months
  const months = [
    'Tanzanite', 'Rubis', 'Saphir', 'Émeraude', 
    'Diamant', 'Améthyste', 'Topaze', 'Sardoine', 
    'Perle', 'Opale', 'Aventurine', 'Grenat'
  ];
  
  const day = date.getDate();
  const month = months[date.getMonth()];
  const year = date.getFullYear();
  
  return `${day} ${month} ${year}`;
};
