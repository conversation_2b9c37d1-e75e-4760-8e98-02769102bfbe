import { fetchGameState } from '../store/slices/gameSlice';

// Actions types for WebSocket
export const WS_CONNECT = 'WS_CONNECT';
export const WS_CONNECTED = 'WS_CONNECTED';
export const WS_DISCONNECT = 'WS_DISCONNECT';
export const WS_DISCONNECTED = 'WS_DISCONNECTED';
export const WS_MESSAGE = 'WS_MESSAGE';
export const WS_SEND = 'WS_SEND';

// Action creators
export const wsConnect = (url) => ({ type: WS_CONNECT, payload: { url } });
export const wsDisconnect = () => ({ type: WS_DISCONNECT });
export const wsSend = (message) => ({ type: WS_SEND, payload: message });

// WebSocket middleware
export const websocketMiddleware = () => {
  let socket = null;
  let reconnectTimer = null;
  let url = null;
  let reconnectAttempts = 0;
  let isConnecting = false;
  let lastConnectionAttempt = 0;
  const MAX_RECONNECT_ATTEMPTS = 10; // Augmenté pour plus de résilience
  const RECONNECT_DELAY = 3000; // 3 secondes
  const CONNECTION_TIMEOUT = 10000; // 10 secondes
  const CONNECTION_COOLDOWN = 2000; // 2 secondes entre les tentatives

  const connect = (store, wsUrl) => {
    // Vérifier le cooldown entre les tentatives de connexion
    const now = Date.now();
    if (now - lastConnectionAttempt < CONNECTION_COOLDOWN) {
      console.log(`WebSocket: Tentative de connexion trop rapide, attente de ${CONNECTION_COOLDOWN}ms`);
      return;
    }

    // Mettre à jour le timestamp de la dernière tentative
    lastConnectionAttempt = now;

    // Éviter les connexions multiples
    if (isConnecting) {
      console.log('WebSocket: Connexion déjà en cours');
      return;
    }

    if (socket !== null && socket.readyState !== WebSocket.CLOSED) {
      console.log('WebSocket: Déjà connecté ou en cours de connexion');
      return;
    }

    // Nettoyer toute socket existante
    if (socket !== null) {
      try {
        socket.close();
      } catch (err) {
        console.error('WebSocket: Erreur lors de la fermeture de la socket', err);
      }
      socket = null;
    }

    // Nettoyer tout timer de reconnexion existant
    if (reconnectTimer) {
      clearTimeout(reconnectTimer);
      reconnectTimer = null;
    }

    url = wsUrl;
    console.log(`WebSocket: Connexion à ${url} (Tentative ${reconnectAttempts + 1}/${MAX_RECONNECT_ATTEMPTS})`);

    // Marquer comme en cours de connexion
    isConnecting = true;

    // Créer un timeout pour la connexion
    const connectionTimeoutId = setTimeout(() => {
      if (socket && socket.readyState !== WebSocket.OPEN) {
        console.error('WebSocket: Timeout de connexion');

        // Fermer la socket si elle existe
        try {
          if (socket) {
            socket.close();
          }
        } catch (err) {
          console.error('WebSocket: Erreur lors de la fermeture de la socket après timeout', err);
        }

        socket = null;
        isConnecting = false;

        // Dispatch une action d'erreur
        store.dispatch({
          type: 'WS_ERROR',
          payload: {
            message: 'Timeout de connexion WebSocket',
            code: 'TIMEOUT'
          }
        });

        // Tenter une reconnexion si possible
        handleReconnect(store);
      }
    }, CONNECTION_TIMEOUT);

    try {
      // Créer une nouvelle connexion WebSocket
      socket = new WebSocket(url);

      // Connexion ouverte
      socket.onopen = () => {
        console.log('WebSocket: Connecté avec succès');

        // Annuler le timeout de connexion
        clearTimeout(connectionTimeoutId);

        // Mettre à jour l'état
        isConnecting = false;
        reconnectAttempts = 0;

        // Dispatch une action de connexion réussie
        store.dispatch({ type: WS_CONNECTED });

        // Envoyer un ping pour vérifier que la connexion est bien établie
        try {
          socket.send(JSON.stringify({ type: 'PING', timestamp: Date.now() }));
        } catch (err) {
          console.error('WebSocket: Erreur lors de l\'envoi du ping initial', err);
        }
      };

      // Connexion fermée
      socket.onclose = (event) => {
        console.log(`WebSocket: Déconnecté avec le code ${event.code}`);

        // Annuler le timeout de connexion
        clearTimeout(connectionTimeoutId);

        // Mettre à jour l'état
        isConnecting = false;
        socket = null;

        // Dispatch une action de déconnexion
        store.dispatch({ type: WS_DISCONNECTED });

        // Tenter une reconnexion si possible
        handleReconnect(store);
      };

      // Erreur de connexion
      socket.onerror = (error) => {
        console.error('WebSocket: Erreur', error);

        // Annuler le timeout de connexion
        clearTimeout(connectionTimeoutId);

        // Mettre à jour l'état
        isConnecting = false;

        // Dispatch une action d'erreur
        store.dispatch({
          type: 'WS_ERROR',
          payload: {
            message: 'Échec de connexion au serveur WebSocket',
            error
          }
        });

        // Ne pas tenter de reconnexion immédiatement, laisser onclose s'en charger
      };

      // Écouter les messages
      socket.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);
          console.log('WebSocket: Message reçu', message);

          // Dispatch le message comme une action
          store.dispatch({
            type: WS_MESSAGE,
            payload: message
          });

          // Gérer les messages spécifiques du serveur
          if (message.type === 'SERVER_UPDATE') {
            // Mettre à jour l'état du jeu lorsque nous recevons des mises à jour du serveur
            store.dispatch(fetchGameState());
          } else if (message.type === 'PONG') {
            // Réponse à un ping, la connexion est active
            console.log('WebSocket: Pong reçu, connexion active');
          }
        } catch (error) {
          console.error('WebSocket: Erreur lors du parsing du message', error);
        }
      };
    } catch (error) {
      console.error('WebSocket: Erreur lors de la création de la connexion', error);

      // Annuler le timeout de connexion
      clearTimeout(connectionTimeoutId);

      // Mettre à jour l'état
      isConnecting = false;

      // Dispatch une action de déconnexion
      store.dispatch({
        type: WS_DISCONNECTED,
        payload: {
          error: error.message
        }
      });

      // Tenter une reconnexion si possible
      handleReconnect(store);
    }
  };

  // Fonction pour gérer les tentatives de reconnexion
  const handleReconnect = (store) => {
    // Tenter une reconnexion uniquement si nous n'avons pas dépassé le nombre maximum de tentatives
    if (reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
      reconnectAttempts++;

      // Backoff exponentiel pour la reconnexion
      const delay = RECONNECT_DELAY * Math.pow(1.5, reconnectAttempts - 1);
      console.log(`WebSocket: Tentative de reconnexion dans ${delay/1000} secondes (Tentative ${reconnectAttempts}/${MAX_RECONNECT_ATTEMPTS})`);

      // Créer un timer pour la reconnexion
      reconnectTimer = setTimeout(() => {
        console.log('WebSocket: Tentative de reconnexion...');
        connect(store, url);
      }, delay);
    } else {
      console.error('WebSocket: Nombre maximum de tentatives de reconnexion atteint. Abandon.');

      // Dispatch une action d'erreur finale
      store.dispatch({
        type: 'WS_ERROR',
        payload: {
          message: 'Nombre maximum de tentatives de reconnexion atteint',
          code: 'MAX_RECONNECT_ATTEMPTS'
        }
      });
    }
  };

  return store => next => action => {
    switch (action.type) {
      case WS_CONNECT:
        connect(store, action.payload.url);
        break;

      case WS_DISCONNECT:
        if (socket !== null) {
          socket.close();
        }
        break;

      case WS_SEND:
        if (socket !== null && socket.readyState === WebSocket.OPEN) {
          // Add a unique ID and timestamp to the message
          const message = {
            ...action.payload,
            id: Date.now().toString(36) + Math.random().toString(36).substr(2, 5),
            timestamp: Date.now()
          };

          socket.send(JSON.stringify(message));
        } else {
          console.warn('WebSocket: Cannot send message, socket is not connected');
        }
        break;

      default:
        // Check if this action should be sent to the server
        if (action.meta && action.meta.sendToServer && socket && socket.readyState === WebSocket.OPEN) {
          // Create a copy of the action without the meta.sendToServer flag
          // to avoid infinite loops
          const serverAction = {
            ...action,
            meta: {
              ...action.meta,
              sendToServer: undefined,
              id: Date.now().toString(36) + Math.random().toString(36).substr(2, 5),
              timestamp: Date.now()
            }
          };

          // Send the action to the server
          socket.send(JSON.stringify(serverAction));
        }
        break;
    }

    return next(action);
  };
};

export default websocketMiddleware;
