/**
 * Actions pour mettre à jour les onglets spécifiques
 */
import { fetchFoodModifiers, updateFoodStats } from '../slices/foodSlice';
import { fetchMiningModifiers, updateMiningStats } from '../slices/miningSlice';
import { fetchHealthModifiers, updateHealthStats } from '../slices/healthSlice';
import { fetchMaterialsModifiers, updateMaterialsStats } from '../slices/materialsSlice';
import { fetchPopulationStats } from '../slices/populationSlice';
import { fetchGameState } from '../slices/gameSlice';

/**
 * Met à jour les onglets spécifiés
 * @param {Array} tabs - Liste des onglets à mettre à jour
 * @param {Object} gameState - État du jeu actuel
 * @param {Object} moralStats - Statistiques de moral actuelles
 * @returns {Function} - Thunk action
 */
export const updateSpecificTabs = (tabs, gameState, moralStats) => async (dispatch, getState) => {
  console.log('Mise à jour des onglets spécifiques:', tabs);

  // Récupérer le gameState le plus récent si nécessaire
  let updatedGameState = gameState;
  let updatedMoralStats = moralStats;

  // Si nous mettons à jour des onglets qui dépendent des jobs, récupérer le gameState le plus récent
  if (tabs.some(tab => ['food', 'mining', 'materials', 'health'].includes(tab))) {
    try {
      // Récupérer le gameState le plus récent
      const result = await dispatch(fetchGameState()).unwrap();
      updatedGameState = result;

      // Récupérer les statistiques de moral les plus récentes
      updatedMoralStats = getState().moral?.moralStats || moralStats;

      console.log('GameState mis à jour pour les onglets spécifiques:', {
        updatedGameState,
        updatedMoralStats
      });
    } catch (error) {
      console.error('Erreur lors de la récupération du gameState:', error);
      // Continuer avec le gameState fourni en paramètre
    }
  }

  const updatePromises = [];

  // Mettre à jour les onglets spécifiés
  if (tabs.includes('food')) {
    console.log('Mise à jour de l\'onglet Food');
    updatePromises.push(
      dispatch(fetchFoodModifiers()).then(() => {
        if (updatedGameState && updatedMoralStats) {
          return new Promise(resolve => {
            setTimeout(() => {
              dispatch(updateFoodStats({
                gameState: updatedGameState,
                moralModifier: updatedMoralStats.modifier,
                moralTitle: updatedMoralStats.title
              }));
              resolve();
            }, 50);
          });
        }
      })
    );
  }

  if (tabs.includes('mining') || tabs.includes('engineering')) {
    console.log('Mise à jour de l\'onglet Mining/Engineering');
    updatePromises.push(
      dispatch(fetchMiningModifiers()).then(() => {
        if (updatedGameState && updatedMoralStats) {
          return new Promise(resolve => {
            setTimeout(() => {
              dispatch(updateMiningStats({
                gameState: updatedGameState,
                moralModifier: updatedMoralStats.modifier,
                moralTitle: updatedMoralStats.title
              }));
              resolve();
            }, 50);
          });
        }
      })
    );
  }

  if (tabs.includes('health')) {
    console.log('Mise à jour de l\'onglet Health');
    updatePromises.push(
      dispatch(fetchHealthModifiers()).then(() => {
        if (updatedGameState && updatedMoralStats) {
          return new Promise(resolve => {
            setTimeout(() => {
              dispatch(updateHealthStats({
                gameState: updatedGameState
              }));
              resolve();
            }, 50);
          });
        }
      })
    );
  }

  if (tabs.includes('population')) {
    console.log('Mise à jour de l\'onglet Population');
    updatePromises.push(dispatch(fetchPopulationStats()));
  }

  // Gérer l'onglet Materials
  if (tabs.includes('materials')) {
    console.log('Mise à jour de l\'onglet Materials');
    updatePromises.push(
      dispatch(fetchMaterialsModifiers()).then(() => {
        if (updatedGameState && updatedMoralStats) {
          return new Promise(resolve => {
            setTimeout(() => {
              dispatch(updateMaterialsStats({
                gameState: updatedGameState,
                moralModifier: updatedMoralStats.modifier,
                moralTitle: updatedMoralStats.title
              }));
              resolve();
            }, 50);
          });
        }
      })
    );
  }

  // Si aucun onglet spécifique n'est spécifié ou si la liste contient des onglets non gérés,
  // mettre à jour l'état du jeu complet
  if (tabs.length === 0 ||
      tabs.some(tab => !['food', 'mining', 'materials', 'health', 'population'].includes(tab))) {
    console.log('Mise à jour complète du gameState');
    // Nous avons déjà récupéré le gameState le plus récent si nécessaire
    if (!updatedGameState || updatedGameState === gameState) {
      updatePromises.push(dispatch(fetchGameState()));
    }
  }

  // Attendre que toutes les mises à jour soient terminées
  await Promise.all(updatePromises);

  console.log('Mises à jour spécifiques terminées');
};
