import { createAsyncThunk } from '@reduxjs/toolkit';
import { fetchGameState } from '../slices/gameSlice';
import { gameApi } from '../../services/api';

/**
 * Async thunk for changing job numbers
 */
export const handleJobChange = createAsyncThunk(
  'jobs/handleJobChange',
  async ({ jobId, change, isShiftPressed }, { dispatch, getState }) => {
    try {
      // Apply multiplier if Shift is pressed
      const actualChange = isShiftPressed ? change * 5 : change;

      const state = getState();
      const jobs = state.jobs.jobs;

      // Find the job being changed
      const job = jobs.find(j => j.id === jobId);

      // Special handling for Worker job
      if (job && job.name === 'Worker') {
        return dispatch(handleWorkerChange({ jobId, change: actualChange }));
      }

      // Check if we're trying to decrease the job count below the number of free workers
      if (actualChange < 0 && job.number + actualChange < job.free) {
        // Translate job name for the alert
        let translatedName = job.name;
        switch(job.name) {
          case 'Craftsman': translatedName = 'Artisans'; break;
          case 'Engineer': translatedName = 'Ingénieurs'; break;
          case 'Scholar': translatedName = 'Érudits'; break;
          case 'Farmer': translatedName = 'Fermiers'; break;
          case 'Healer': translatedName = 'Guérisseurs'; break;
          case 'Miner': translatedName = 'Mineurs'; break;
          case 'Protector': translatedName = 'Protecteurs'; break;
          case 'Soldier': translatedName = 'Soldats'; break;
          case 'Worker': translatedName = 'Ouvriers'; break;
          default: translatedName = job.name;
        }
        alert(`Impossible de réduire le nombre de ${translatedName} en dessous du nombre d'emplois gratuits (${job.free}).`);
        return;
      }

      // Check if we have enough workers to increase by 5 when Shift is pressed
      if (isShiftPressed && actualChange > 0 && job.name !== 'Worker') {
        const workers = jobs.find(j => j.name === 'Worker');
        if (workers && workers.number < Math.abs(actualChange)) {
          alert(`Pas assez d'ouvriers disponibles pour ajouter ${Math.abs(actualChange)} ${job.name === 'Craftsman' ? 'Artisans' :
                 job.name === 'Engineer' ? 'Ingénieurs' :
                 job.name === 'Scholar' ? 'Érudits' :
                 job.name === 'Farmer' ? 'Fermiers' :
                 job.name === 'Healer' ? 'Guérisseurs' :
                 job.name === 'Miner' ? 'Mineurs' :
                 job.name === 'Protector' ? 'Protecteurs' :
                 job.name === 'Soldier' ? 'Soldats' : job.name}.`);
          return;
        }
      }

      // Send request to server using API service
      const data = await gameApi.changeJobNumber(jobId, actualChange);

      if (data.success) {
        // Refresh game state to get updated jobs
        dispatch(fetchGameState());
        return data;
      } else {
        throw new Error(data.error || 'Failed to change job');
      }
    } catch (error) {
      console.error('Error changing job:', error);
      // Refresh game state on error
      dispatch(fetchGameState());
      throw error;
    }
  }
);

/**
 * Async thunk for changing worker numbers
 */
export const handleWorkerChange = createAsyncThunk(
  'jobs/handleWorkerChange',
  async ({ jobId, change }, { dispatch }) => {
    try {
      // Send request to server using API service
      const data = await gameApi.changeWorkerNumber(change);

      if (data.success) {
        // Refresh game state to get updated jobs
        dispatch(fetchGameState());
        return data;
      } else {
        throw new Error(data.error || 'Failed to change workers');
      }
    } catch (error) {
      console.error('Error changing workers:', error);
      // Refresh game state on error
      dispatch(fetchGameState());
      throw error;
    }
  }
);

/**
 * Async thunk for changing free workers
 */
export const handleFreeChange = createAsyncThunk(
  'jobs/handleFreeChange',
  async ({ jobId, change, isShiftPressed }, { dispatch }) => {
    try {
      // Apply multiplier if Shift is pressed
      const actualChange = isShiftPressed ? change * 5 : change;

      // Send request to server using API service
      const data = await gameApi.changeFreeWorkers(jobId, actualChange);

      if (data.success) {
        // Refresh game state to get updated jobs
        dispatch(fetchGameState());
        return data;
      } else {
        throw new Error(data.error || 'Failed to change free workers');
      }
    } catch (error) {
      console.error('Error changing free workers:', error);
      // Refresh game state on error
      dispatch(fetchGameState());
      throw error;
    }
  }
);
