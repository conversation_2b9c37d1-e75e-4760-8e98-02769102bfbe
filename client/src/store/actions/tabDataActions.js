import { fetchGameState } from '../slices/gameSlice';
import { fetchInhabitants, fetchPopulationStats } from '../slices/populationSlice';
import { fetchEvents } from '../slices/eventsSlice';
import { fetchBuildings } from '../slices/buildingsSlice';
import { fetchFoodModifiers } from '../slices/foodSlice';
import { fetchMaterialsModifiers } from '../slices/materialsSlice';
import { fetchMiningModifiers } from '../slices/miningSlice';
import { fetchMoralModifiers } from '../slices/moralSlice';
import { fetchResearchModifiers } from '../slices/researchSlice';
import { fetchTradingModifiers } from '../slices/tradingSlice';
import { fetchJusticeDefenseModifiers } from '../slices/justiceDefenseSlice';
import { fetchChargesModifiers } from '../slices/chargesSlice';
import { fetchRenownModifiers } from '../slices/renownSlice';
import { fetchHealthModifiers } from '../slices/healthSlice';

/**
 * Action pour charger les données spécifiques à un onglet
 * @param {string} tabName - Nom de l'onglet à charger
 * @returns {Function} - Thunk action
 */
export const loadTabData = (tabName) => (dispatch, getState) => {
  // Vérifier si les données sont déjà en cours de chargement
  const state = getState();
  if (state.ui.loadingTabs && state.ui.loadingTabs[tabName]) {
    return;
  }

  // Vérifier si l'onglet a été chargé récemment (dans les 10 secondes)
  const lastLoaded = state.ui.tabLastLoaded && state.ui.tabLastLoaded[tabName];
  if (lastLoaded && Date.now() - lastLoaded < 10000) {
    return;
  }

  // Marquer l'onglet comme en cours de chargement
  dispatch({ type: 'ui/setTabLoading', payload: { tabName, loading: true } });

  // Charger les données spécifiques à l'onglet
  try {
    switch (tabName) {
      case 'dashboard':
        dispatch(fetchGameState({ force: false }));
        break;
      case 'population':
        dispatch(fetchInhabitants());
        dispatch(fetchPopulationStats());
        break;
      case 'events':
        dispatch(fetchEvents());
        break;
      case 'buildings':
        dispatch(fetchBuildings());
        break;
      case 'food':
        dispatch(fetchFoodModifiers());
        break;
      case 'materials':
        dispatch(fetchMaterialsModifiers());
        break;
      case 'mining':
        dispatch(fetchMiningModifiers());
        break;
      case 'moral':
        dispatch(fetchMoralModifiers());
        break;
      case 'research':
        dispatch(fetchResearchModifiers());
        break;
      case 'trading':
        dispatch(fetchTradingModifiers());
        break;
      case 'justice':
        dispatch(fetchJusticeDefenseModifiers());
        break;
      case 'charges':
        dispatch(fetchChargesModifiers());
        break;
      case 'renown':
        dispatch(fetchRenownModifiers());
        break;
      case 'health':
        dispatch(fetchHealthModifiers());
        break;
      case 'calendar':
        // Le calendrier utilise principalement des données statiques
        dispatch(fetchGameState({ force: false }));
        break;
      default:
        // Aucune action spécifique pour cet onglet
        break;
    }
  } catch (error) {
    console.error(`Erreur lors du chargement des données pour l'onglet ${tabName}:`, error);
  } finally {
    // Marquer l'onglet comme chargé
    dispatch({ type: 'ui/setTabLoading', payload: { tabName, loading: false } });
    dispatch({ type: 'ui/setTabLastLoaded', payload: { tabName, timestamp: Date.now() } });
  }
};

/**
 * Action pour rafraîchir les données de tous les onglets
 * @returns {Function} - Thunk action
 */
export const refreshAllTabs = () => (dispatch, getState) => {
  console.log('Rafraîchissement de toutes les données');

  // Charger les données de base du jeu
  dispatch(fetchGameState({ force: true }));

  // Charger les données spécifiques à chaque onglet
  const state = getState();
  const activeTab = state.ui.activeTab;

  // Charger les données de l'onglet actif en priorité
  dispatch(loadTabData(activeTab));
};
