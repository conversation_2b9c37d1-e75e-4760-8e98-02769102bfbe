import { createSlice, createAsyncThunk, createSelector } from '@reduxjs/toolkit';
import { modifiersApi } from '../../services/api';

// Constants
const HEALTH_MODIFIERS_TABLE_ID = 13; // Health modifiers table ID
const BASE_SICKNESS_CHANCE = 0.05; // 5% base chance of getting sick

// Async thunk for fetching health modifiers
export const fetchHealthModifiers = createAsyncThunk(
  'health/fetchModifiers',
  async (_, { rejectWithValue }) => {
    try {
      // Fetch health data from API
      const healthModifiers = await modifiersApi.getModifiersByTable(HEALTH_MODIFIERS_TABLE_ID);

      console.log('Health modifiers fetched:', healthModifiers);

      return healthModifiers;
    } catch (error) {
      console.error('Error loading health modifiers:', error);
      return rejectWithValue('Failed to load health modifiers. Please try again.');
    }
  }
);

// Initial state
const initialState = {
  healthModifiers: [],
  healthStats: {
    totalPopulation: 0,
    totalSick: 0,
    sickPercentage: 0,
    sickProbability: BASE_SICKNESS_CHANCE,
    activeHealers: 0
  },
  loading: false,
  error: null
};

/**
 * Calculate health stats based on game state
 * @param {Object} gameState - Game state object
 * @param {Array} healthModifiers - Health modifiers array
 * @returns {Object} - Health stats object
 */
export const calculateHealthStats = (gameState, healthModifiers = []) => {
  if (!gameState || !gameState.jobs) {
    return {
      totalPopulation: 0,
      totalSick: 0,
      sickPercentage: 0,
      sickProbability: BASE_SICKNESS_CHANCE,
      activeHealers: 0
    };
  }

  // Calculate total population and sick people
  const totalPopulation = gameState.jobs.reduce((sum, job) => sum + job.number, 0);
  const totalSick = gameState.jobs.reduce((sum, job) => sum + (job.sick || 0), 0);
  const sickPercentage = totalPopulation > 0 ? (totalSick / totalPopulation) * 100 : 0;

  // Get healers count
  const healers = gameState.jobs.find(job => job.name === 'Healer') || { number: 0, sick: 0 };
  const activeHealers = healers.number - healers.sick;

  // Calculate total health modifier
  const totalHealthModifier = Array.isArray(healthModifiers)
    ? healthModifiers.reduce((sum, mod) => {
        const effect = typeof mod.effect === 'number' ? mod.effect : parseFloat(mod.effect) || 0;
        return sum + effect;
      }, 0)
    : 0;

  console.log('Health calculation:', {
    totalPopulation,
    activeHealers,
    totalHealthModifier
  });

  // Calculate sick probability using the formula:
  // MAX(0; 0.25/(1+EXP(-0.1*(TotalInhabitants-18*HealerNumber*(1+TotalHealthModifier)))))
  const exponent = -0.1 * (totalPopulation - 18 * activeHealers * (1 + totalHealthModifier));
  const sickProbability = Math.max(0, 0.25 / (1 + Math.exp(exponent)));

  return {
    totalPopulation,
    totalSick,
    sickPercentage,
    sickProbability,
    activeHealers
  };
};

// Create the health slice
const healthSlice = createSlice({
  name: 'health',
  initialState,
  reducers: {
    // Update health stats based on game state
    updateHealthStats: (state, action) => {
      const { gameState } = action.payload;

      if (!gameState) return;

      // Calculate health stats using the current modifiers
      const healthStats = calculateHealthStats(gameState, state.healthModifiers);

      console.log('Updating health stats:', healthStats);

      // Update health stats
      state.healthStats = healthStats;
    }
  },
  extraReducers: (builder) => {
    builder
      // Handle fetchHealthModifiers
      .addCase(fetchHealthModifiers.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchHealthModifiers.fulfilled, (state, action) => {
        state.loading = false;
        state.healthModifiers = action.payload;
      })
      .addCase(fetchHealthModifiers.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  }
});

// Export actions
export const { updateHealthStats } = healthSlice.actions;

// Sélecteurs de base
const selectHealthState = (state) => state.health;

// Export selectors
export const selectHealthModifiers = createSelector(
  [selectHealthState],
  (healthState) => healthState.healthModifiers
);

export const selectHealthStats = createSelector(
  [selectHealthState],
  (healthState) => healthState.healthStats
);

export const selectHealthLoading = createSelector(
  [selectHealthState],
  (healthState) => healthState.loading
);

export const selectHealthError = createSelector(
  [selectHealthState],
  (healthState) => healthState.error
);

// Export reducer
export default healthSlice.reducer;
