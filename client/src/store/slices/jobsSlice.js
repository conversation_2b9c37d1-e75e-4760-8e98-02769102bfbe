import { createSlice, createAsyncThunk, createSelector } from '@reduxjs/toolkit';

// Async thunk for changing job numbers
export const changeJobNumber = createAsyncThunk(
  'jobs/changeJobNumber',
  async ({ jobId, change }, { getState, rejectWithValue }) => {
    try {
      const response = await fetch('http://localhost:3001/api/game/jobs/change', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ jobId, change })
      });

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || 'Failed to change job number');
      }

      return { jobId, change };
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

// Async thunk for changing free workers
export const changeFreeWorkers = createAsyncThunk(
  'jobs/changeFreeWorkers',
  async ({ jobId, change }, { getState, rejectWithValue }) => {
    try {
      const response = await fetch('http://localhost:3001/api/game/jobs/free', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ jobId, change })
      });

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || 'Failed to change free workers');
      }

      return { jobId, change };
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

// Initial state
const initialState = {
  jobs: [],
  loading: false,
  error: null,
  isShiftPressed: false,
};

// Create the jobs slice
const jobsSlice = createSlice({
  name: 'jobs',
  initialState,
  reducers: {
    setJobs: (state, action) => {
      state.jobs = action.payload;
    },
    setShiftPressed: (state, action) => {
      state.isShiftPressed = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      // Handle job number change
      .addCase(changeJobNumber.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(changeJobNumber.fulfilled, (state, action) => {
        state.loading = false;
        // Optimistic update already handled in the pending case
      })
      .addCase(changeJobNumber.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // Handle free workers change
      .addCase(changeFreeWorkers.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(changeFreeWorkers.fulfilled, (state, action) => {
        state.loading = false;
        // Optimistic update already handled in the pending case
      })
      .addCase(changeFreeWorkers.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

// Export actions
export const { setJobs, setShiftPressed } = jobsSlice.actions;

// Sélecteurs de base
const selectJobsRaw = (state) => state.jobs.jobs;
export const selectJobsLoading = (state) => state.jobs.loading;
export const selectJobsError = (state) => state.jobs.error;
export const selectIsShiftPressed = (state) => state.jobs.isShiftPressed;

// Export selectors - pas besoin de mémoisation pour les sélecteurs simples
export const selectJobs = selectJobsRaw;

export const selectJobById = (id) => createSelector(
  [selectJobsRaw],
  (jobs) => jobs.find(job => job.id === id)
);

export const selectWorkerJob = createSelector(
  [selectJobsRaw],
  (jobs) => jobs.find(job => job.name === 'Worker')
);

// Export reducer
export default jobsSlice.reducer;
