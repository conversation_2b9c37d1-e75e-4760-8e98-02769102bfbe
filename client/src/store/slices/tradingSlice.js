import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { modifiersApi } from '../../services/api';

// Constants
const TRADING_MODIFIERS_TABLE_ID = 18; // Trading effects table ID
const TRADING_VALUE = 25;              // Base trading value per craftsman (from server constants)

// Async thunk for fetching trading modifiers
export const fetchTradingModifiers = createAsyncThunk(
  'trading/fetchModifiers',
  async (_, { rejectWithValue }) => {
    try {
      // Fetch trading modifiers
      const modifiers = await modifiersApi.getModifiersByTable(TRADING_MODIFIERS_TABLE_ID);

      console.log('tradingSlice: Fetched trading modifiers:', modifiers);

      return modifiers;
    } catch (error) {
      console.error('Error loading trading modifiers:', error);
      return rejectWithValue('Failed to load trading modifiers. Please try again.');
    }
  }
);

// Initial state
const initialState = {
  tradingModifiers: [],
  tradingStats: {
    craftsmen: 0,
    craftsmenSick: 0,
    activeCraftsmen: 0,
    tradingRevenue: 0,
    tradingModifier: 0,
    moralModifier: 0,
    tradingValue: TRADING_VALUE,
    revenuePerCraftsman: 0
  },
  loading: false,
  error: null
};

/**
 * Calculate trading revenue based on craftsmen and modifiers
 * @param {Number} activeCraftsmen - Number of active craftsmen
 * @param {Number} tradingModifier - Trading modifier value
 * @returns {Number} - Trading revenue value
 */
export const calculateTradingRevenue = (
  activeCraftsmen,
  tradingModifier
) => {
  // Calculate trading revenue using the formula:
  // tradingRevenue = activeCraftsmen * MAX(0, 1 + tradingModifier) * TRADING_VALUE
  const revenue = activeCraftsmen *
                 Math.max(0, 1 + tradingModifier) *
                 TRADING_VALUE;

  // Round to 2 decimal places
  return Math.round(revenue * 100) / 100;
};

/**
 * Calculate revenue per craftsman
 * @param {Number} activeCraftsmen - Number of active craftsmen
 * @param {Number} tradingRevenue - Total trading revenue
 * @returns {Number} - Revenue per craftsman
 */
export const calculateRevenuePerCraftsman = (activeCraftsmen, tradingRevenue) => {
  if (activeCraftsmen <= 0) return 0;
  return Math.round((tradingRevenue / activeCraftsmen) * 100) / 100;
};

// Create the trading slice
const tradingSlice = createSlice({
  name: 'trading',
  initialState,
  reducers: {
    // Update trading stats based on game state
    updateTradingStats: (state, action) => {
      const { gameState } = action.payload;

      if (!gameState) return;

      // Get moral modifier value
      const moralValue = gameState?.gameState?.moral_value || 1.0;
      const moralModifier = moralValue - 1.0; // Convert from multiplier to modifier

      // Calculate trading modifier from modifiers
      const tradingModifier = Array.isArray(state.tradingModifiers)
        ? state.tradingModifiers.reduce((sum, mod) => sum + mod.effect, 0)
        : 0;

      console.log('tradingSlice: Trading modifier used:', tradingModifier);

      // Extraire les jobs correctement, qu'ils soient dans gameState.jobs ou gameState.gameState.jobs
      const jobs = Array.isArray(gameState.jobs) ? gameState.jobs :
                  (gameState.gameState && Array.isArray(gameState.gameState.jobs)) ? gameState.gameState.jobs : [];

      // Get craftsmen count
      const craftsmen = jobs.find(j => j.name === 'Craftsman') || { number: 0, sick: 0 };
      const craftsmenSick = craftsmen.sick || 0;
      const activeCraftsmen = craftsmen.number - craftsmenSick;

      // Calculate trading revenue
      const tradingRevenue = calculateTradingRevenue(
        activeCraftsmen,
        tradingModifier
      );

      // Calculate revenue per craftsman
      const revenuePerCraftsman = calculateRevenuePerCraftsman(
        activeCraftsmen,
        tradingRevenue
      );

      // Update trading stats
      state.tradingStats = {
        craftsmen: craftsmen.number,
        craftsmenSick,
        activeCraftsmen,
        tradingRevenue,
        tradingModifier,
        moralModifier,
        tradingValue: TRADING_VALUE,
        revenuePerCraftsman
      };
    }
  },
  extraReducers: (builder) => {
    builder
      // Handle fetchTradingModifiers
      .addCase(fetchTradingModifiers.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchTradingModifiers.fulfilled, (state, action) => {
        state.loading = false;
        state.tradingModifiers = action.payload;
        console.log('tradingSlice: Updated state with modifiers:', action.payload);
      })
      .addCase(fetchTradingModifiers.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  }
});

// Export actions
export const { updateTradingStats } = tradingSlice.actions;

// Sélecteurs directs pour les propriétés

// Export selectors - utiliser des sélecteurs simples pour les propriétés directes
export const selectTradingModifiers = (state) => state.trading.tradingModifiers;
export const selectTradingStats = (state) => state.trading.tradingStats;
export const selectTradingLoading = (state) => state.trading.loading;
export const selectTradingError = (state) => state.trading.error;

// Export reducer
export default tradingSlice.reducer;
