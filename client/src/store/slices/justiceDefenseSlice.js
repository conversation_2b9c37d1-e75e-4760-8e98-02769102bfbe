import { createSlice, createAsyncThunk, createSelector } from '@reduxjs/toolkit';
import { modifiersApi } from '../../services/api';

// Constants
const JUSTICE_TABLE_ID = 19;  // Justice modifiers table ID (from original)
const ARMY_TABLE_ID = 20;     // Army modifiers table ID (from original)

// Async thunk for fetching justice and defense modifiers and defense means
export const fetchJusticeDefenseModifiers = createAsyncThunk(
  'justiceDefense/fetchModifiers',
  async (_, { rejectWithValue }) => {
    try {
      // Fetch justice and army modifiers in parallel
      const [justiceModifiers, armyModifiers] = await Promise.all([
        modifiersApi.getModifiersByTable(JUSTICE_TABLE_ID),
        modifiersApi.getModifiersByTable(ARMY_TABLE_ID)
      ]);

      // Fetch defense means
      let defenseMeans = [];
      try {
        defenseMeans = await modifiersApi.getDefenseMeans();
        // Vérifier que defenseMeans est un tableau
        if (!Array.isArray(defenseMeans)) {
          console.warn('Defense means is not an array:', defenseMeans);
          defenseMeans = [];
        }
      } catch (error) {
        console.error('Error fetching defense means:', error);
        // Continuer avec un tableau vide en cas d'erreur
      }

      return {
        justiceModifiers,
        armyModifiers,
        defenseMeans
      };
    } catch (error) {
      console.error('Error loading justice and defense data:', error);
      return rejectWithValue('Failed to load justice and defense data. Please try again.');
    }
  }
);

// Async thunk for adding a defense mean
export const addDefenseMean = createAsyncThunk(
  'justiceDefense/addDefenseMean',
  async (defenseMean, { rejectWithValue }) => {
    try {
      const result = await modifiersApi.addDefenseMean(defenseMean);
      return result;
    } catch (error) {
      console.error('Error adding defense mean:', error);
      return rejectWithValue('Failed to add defense mean. Please try again.');
    }
  }
);

// Async thunk for deleting a defense mean
export const deleteDefenseMean = createAsyncThunk(
  'justiceDefense/deleteDefenseMean',
  async (id, { rejectWithValue }) => {
    try {
      const result = await modifiersApi.deleteDefenseMean(id);
      return { id, result };
    } catch (error) {
      console.error('Error deleting defense mean:', error);
      return rejectWithValue('Failed to delete defense mean. Please try again.');
    }
  }
);

// Initial state
const initialState = {
  justiceModifiers: [],
  armyModifiers: [],
  defenseMeans: [],
  stats: {
    crimeProbability: 0,
    armyStrength: 0,
    protectors: 0,
    protectors_sick: 0,
    soldiers: 0,
    soldiers_sick: 0,
    justiceModifier: 0,
    armyModifier: 0,
    defenseValues: {
      citizenDefense: 0.125,
      soldierDefense: 0.5,
      protectorDefense: 0.25
    }
  },
  loading: false,
  error: null
};

/**
 * Calculate crime probability based on original formula
 * @param {Number} totalInhabitants - Total number of inhabitants
 * @param {Number} activeProtectors - Number of active protectors
 * @param {Number} activeSoldiers - Number of active soldiers
 * @param {Number} justiceModifier - Justice modifier value
 * @returns {Number} - Crime probability
 */
export const calculateCrimeProbability = (
  totalInhabitants,
  activeProtectors,
  activeSoldiers,
  justiceModifier
) => {
  // Original formula: MAX(0, 0.15 / (1 + EXP(-0.1 * (TotalInhabitants - (15 * activeProtectors * (1 + justiceModifier) - (2.5 * activeSoldiers * (1 + justiceModifier)))))))
  const protectorEffect = 15 * activeProtectors * (1 + justiceModifier);
  const soldierEffect = 2.5 * activeSoldiers * (1 + justiceModifier);
  const exponentValue = -0.1 * (totalInhabitants - (protectorEffect - soldierEffect));
  const probability = 0.15 / (1 + Math.exp(exponentValue));

  // Ensure probability is not negative
  return Math.max(0, probability);
};

/**
 * Calculate army strength based on original formula
 * @param {Number} activeSoldiers - Number of active soldiers
 * @param {Number} armyModifier - Army modifier value
 * @param {Number} inhabitantsToPay - Number of inhabitants that pay taxes
 * @param {Number} allSick - Number of all sick inhabitants
 * @param {Number} soldiersNumber - Total number of soldiers
 * @returns {Number} - Army strength
 */
export const calculateArmyStrength = (
  activeSoldiers,
  armyModifier,
  inhabitantsToPay,
  allSick,
  soldiersNumber
) => {
  // Original formula: (activeSoldiers * 0.5 * (1 + armyModifier)) + ((inhabitantsToPay - allSick - soldiers.number) * 0.125)
  const soldierStrength = activeSoldiers * 0.5 * (1 + armyModifier);
  const citizenStrength = (inhabitantsToPay - allSick - soldiersNumber) * 0.125;

  return soldierStrength + citizenStrength;
};

// Create the justice and defense slice
const justiceDefenseSlice = createSlice({
  name: 'justiceDefense',
  initialState,
  reducers: {
    // Update justice and defense stats based on game state
    updateJusticeDefenseStats: (state, action) => {
      const { gameState } = action.payload;

      if (!gameState) return;

      // Calculate justice modifier total
      const justiceModifier = Array.isArray(state.justiceModifiers)
        ? state.justiceModifiers.reduce((sum, mod) => sum + mod.effect, 0)
        : 0;

      // Calculate army modifier total
      const armyModifier = Array.isArray(state.armyModifiers)
        ? state.armyModifiers.reduce((sum, mod) => sum + mod.effect, 0)
        : 0;

      // Extraire les jobs correctement, qu'ils soient dans gameState.jobs ou gameState.gameState.jobs
      const jobs = Array.isArray(gameState.jobs) ? gameState.jobs :
                  (gameState.gameState && Array.isArray(gameState.gameState.jobs)) ? gameState.gameState.jobs : [];

      // Get protectors count
      const protectors = jobs.find(j => j.name === 'Protector') || { number: 0, sick: 0 };
      const protectorsSick = protectors.sick || 0;
      const activeProtectors = protectors.number - protectorsSick;

      // Get soldiers count
      const soldiers = jobs.find(j => j.name === 'Soldier') || { number: 0, sick: 0 };
      const soldiersSick = soldiers.sick || 0;
      const activeSoldiers = soldiers.number - soldiersSick;

      // Get total inhabitants and sick count
      const totalInhabitants = jobs.reduce((sum, job) => sum + job.number, 0);
      const allSick = jobs.reduce((sum, job) => sum + (job.sick || 0), 0);
      const inhabitantsToPay = totalInhabitants; // Assuming all inhabitants pay taxes

      // Calculate crime probability
      const crimeProbability = calculateCrimeProbability(
        totalInhabitants,
        activeProtectors,
        activeSoldiers,
        justiceModifier
      );

      // Calculate army strength
      const armyStrength = calculateArmyStrength(
        activeSoldiers,
        armyModifier,
        inhabitantsToPay,
        allSick,
        soldiers.number
      );

      // Update stats
      state.stats = {
        crimeProbability,
        armyStrength,
        protectors: protectors.number,
        protectors_sick: protectorsSick,
        soldiers: soldiers.number,
        soldiers_sick: soldiersSick,
        justiceModifier,
        armyModifier,
        defenseValues: {
          citizenDefense: 0.125,
          soldierDefense: 0.5,
          protectorDefense: 0.25
        }
      };
    }
  },
  extraReducers: (builder) => {
    builder
      // Handle fetchJusticeDefenseModifiers
      .addCase(fetchJusticeDefenseModifiers.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchJusticeDefenseModifiers.fulfilled, (state, action) => {
        state.loading = false;
        state.justiceModifiers = action.payload.justiceModifiers;
        state.armyModifiers = action.payload.armyModifiers;
        // S'assurer que defenseMeans est toujours un tableau
        state.defenseMeans = Array.isArray(action.payload.defenseMeans) ? action.payload.defenseMeans : [];

        // Calculer les totaux des modificateurs
        state.stats.justiceModifier = Array.isArray(state.justiceModifiers)
          ? state.justiceModifiers.reduce((sum, mod) => sum + mod.effect, 0)
          : 0;

        state.stats.armyModifier = Array.isArray(state.armyModifiers)
          ? state.armyModifiers.reduce((sum, mod) => sum + mod.effect, 0)
          : 0;

        console.log('justiceDefenseSlice: Modificateurs mis à jour:', {
          justiceModifiers: state.justiceModifiers,
          armyModifiers: state.armyModifiers,
          justiceModifier: state.stats.justiceModifier,
          armyModifier: state.stats.armyModifier
        });
      })
      .addCase(fetchJusticeDefenseModifiers.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Handle addDefenseMean
      .addCase(addDefenseMean.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(addDefenseMean.fulfilled, (state, action) => {
        state.loading = false;
        // Refresh defense means by fetching all modifiers again
      })
      .addCase(addDefenseMean.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Handle deleteDefenseMean
      .addCase(deleteDefenseMean.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteDefenseMean.fulfilled, (state, action) => {
        state.loading = false;
        // Remove the deleted defense mean from the state
        state.defenseMeans = state.defenseMeans.filter(dm => dm.id !== action.payload.id);
      })
      .addCase(deleteDefenseMean.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  }
});

// Export actions
export const { updateJusticeDefenseStats } = justiceDefenseSlice.actions;

// Sélecteurs de base
const selectJusticeDefenseState = (state) => state.justiceDefense;

// Export selectors
export const selectJusticeModifiers = createSelector(
  [selectJusticeDefenseState],
  (justiceDefenseState) => justiceDefenseState.justiceModifiers
);

export const selectArmyModifiers = createSelector(
  [selectJusticeDefenseState],
  (justiceDefenseState) => justiceDefenseState.armyModifiers
);

export const selectDefenseMeans = createSelector(
  [selectJusticeDefenseState],
  (justiceDefenseState) => justiceDefenseState.defenseMeans
);

export const selectJusticeDefenseStats = createSelector(
  [selectJusticeDefenseState],
  (justiceDefenseState) => justiceDefenseState.stats
);

export const selectJusticeDefenseLoading = createSelector(
  [selectJusticeDefenseState],
  (justiceDefenseState) => justiceDefenseState.loading
);

export const selectJusticeDefenseError = createSelector(
  [selectJusticeDefenseState],
  (justiceDefenseState) => justiceDefenseState.error
);

// Export reducer
export default justiceDefenseSlice.reducer;
