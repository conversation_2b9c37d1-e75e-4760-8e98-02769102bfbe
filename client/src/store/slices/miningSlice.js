import { createSlice, createAsyncThunk, createSelector } from '@reduxjs/toolkit';
import { modifiersApi } from '../../services/api';
import {
  MINING_VALUE,
  calculateMiningProduction,
  calculateRevenuePerMiner,
  calculateEngineeringMultiplier,
  calculateTotalBonus
} from '../../services/calculationService';

// Table IDs
const MINING_GENERAL_TABLE_ID = 4; // General mining effects
const ENGINEERING_TABLE_ID = 5;    // Engineering effects
const MINING_TECH_TABLE_ID = 6;    // Technology effects on mining

// Async thunk for fetching mining modifiers
export const fetchMiningModifiers = createAsyncThunk(
  'mining/fetchModifiers',
  async (_, { rejectWithValue }) => {
    try {
      // Fetch all three types of modifiers in parallel
      const [generalMods, engineeringMods, techMods] = await Promise.all([
        modifiersApi.getModifiersByTable(MINING_GENERAL_TABLE_ID),
        modifiersApi.getModifiersByTable(ENGINEERING_TABLE_ID),
        modifiersApi.getModifiersByTable(MINING_TECH_TABLE_ID)
      ]);

      return {
        miningGeneralModifiers: generalMods,
        engineeringModifiers: engineeringMods,
        miningTechModifiers: techMods
      };
    } catch (error) {
      console.error('Error loading modifiers:', error);
      return rejectWithValue('Failed to load mining modifiers. Please try again.');
    }
  }
);

// Initial state
const initialState = {
  miningGeneralModifiers: [],
  engineeringModifiers: [],
  miningTechModifiers: [],
  miningStats: {
    miners: 0,
    production: 0,
    baseProduction: 0,
    revenuePerMiner: 0,
    totalBonus: 0
  },
  totals: {
    generalEffectsTotal: 0,
    engineeringEffectsTotal: 0,
    techEffectsTotal: 0,
    totalProductionBonus: 0,
    engineeringMultiplier: 0
  },
  loading: false,
  error: null
};

// Create the mining slice
const miningSlice = createSlice({
  name: 'mining',
  initialState,
  reducers: {
    // Update mining stats based on game state
    updateMiningStats: (state, action) => {
      const { gameState, moralModifier, moralTitle } = action.payload;

      if (!gameState) return;

      console.log('miningSlice: updateMiningStats with moral:', { moralModifier, moralTitle });

      // Calculate totals - vérifier que les tableaux existent avant d'appeler reduce
      // et que les effets sont des nombres
      const generalEffectsTotal = Array.isArray(state.miningGeneralModifiers)
        ? state.miningGeneralModifiers.reduce((sum, mod) => {
            const effect = typeof mod.effect === 'number' ? mod.effect : parseFloat(mod.effect) || 0;
            return sum + effect;
          }, 0)
        : 0;
      const engineeringEffectsTotal = Array.isArray(state.engineeringModifiers)
        ? state.engineeringModifiers.reduce((sum, mod) => {
            const effect = typeof mod.effect === 'number' ? mod.effect : parseFloat(mod.effect) || 0;
            return sum + effect;
          }, 0)
        : 0;
      const techEffectsTotal = Array.isArray(state.miningTechModifiers)
        ? state.miningTechModifiers.reduce((sum, mod) => {
            const effect = typeof mod.effect === 'number' ? mod.effect : parseFloat(mod.effect) || 0;
            return sum + effect;
          }, 0)
        : 0;

      console.log('miningSlice: Effects totals:', {
        generalEffectsTotal,
        engineeringEffectsTotal,
        techEffectsTotal
      });
      const totalProductionBonus = generalEffectsTotal + techEffectsTotal;

      // Extraire les jobs correctement, qu'ils soient dans gameState.jobs ou gameState.gameState.jobs
      const jobs = Array.isArray(gameState.jobs) ? gameState.jobs :
                  (gameState.gameState && Array.isArray(gameState.gameState.jobs)) ? gameState.gameState.jobs : [];

      console.log('miningSlice: Jobs extracted:', jobs);

      // Get engineers count
      const engineers = jobs.find(j => j.name === 'Engineer') || { number: 0, sick: 0 };
      console.log('miningSlice: Found engineers:', engineers);
      const activeEngineers = (engineers.number || 0) - (engineers.sick || 0);

      // Calculate engineering multiplier using the centralized calculation service
      const engineeringMultiplier = calculateEngineeringMultiplier(activeEngineers, engineeringEffectsTotal);

      // Update totals
      state.totals = {
        generalEffectsTotal,
        engineeringEffectsTotal,
        techEffectsTotal,
        totalProductionBonus,
        engineeringMultiplier
      };

      // Get values from gameState
      const miners = jobs.find(j => j.name === 'Miner') || { number: 0, sick: 0 };
      console.log('miningSlice: Found miners:', miners);
      const activeMiner = (miners.number || 0) - (miners.sick || 0);

      // Calculate mining production using the centralized calculation service
      const miningProduction = calculateMiningProduction(
        activeMiner,
        generalEffectsTotal,
        techEffectsTotal,
        moralModifier,
        engineeringMultiplier
      );

      // Calculate base production
      const baseProduction = activeMiner * MINING_VALUE;

      // Calculate revenue per miner using the centralized calculation service
      const revenuePerMiner = calculateRevenuePerMiner(
        activeMiner,
        miningProduction,
        generalEffectsTotal,
        techEffectsTotal,
        moralModifier,
        engineeringMultiplier
      );

      // Calculate total bonus using the centralized calculation service
      const totalBonus = calculateTotalBonus(
        generalEffectsTotal,
        techEffectsTotal,
        moralModifier,
        engineeringMultiplier
      );

      // Update mining stats
      state.miningStats = {
        miners: activeMiner,
        production: miningProduction,
        baseProduction: baseProduction,
        revenuePerMiner: revenuePerMiner,
        totalBonus: totalBonus,
        moralModifier,
        moralTitle,
        // Ajouter des informations supplémentaires pour le débogage
        _debug: {
          timestamp: Date.now(),
          activeEngineers,
          activeMiner,
          generalEffectsTotal,
          techEffectsTotal,
          engineeringMultiplier
        }
      };

      console.log('miningSlice: Updated mining stats:', state.miningStats);
    }
  },
  extraReducers: (builder) => {
    builder
      // Handle fetchMiningModifiers
      .addCase(fetchMiningModifiers.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchMiningModifiers.fulfilled, (state, action) => {
        state.loading = false;
        state.miningGeneralModifiers = action.payload.miningGeneralModifiers;
        state.engineeringModifiers = action.payload.engineeringModifiers;
        state.miningTechModifiers = action.payload.miningTechModifiers;
      })
      .addCase(fetchMiningModifiers.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  }
});

// Export actions
export const { updateMiningStats } = miningSlice.actions;

// Sélecteurs de base
const selectMiningState = (state) => state.mining;

// Export selectors - utiliser des sélecteurs simples pour les propriétés directes
export const selectMiningGeneralModifiers = (state) => state.mining.miningGeneralModifiers;
export const selectEngineeringModifiers = (state) => state.mining.engineeringModifiers;
export const selectMiningTechModifiers = (state) => state.mining.miningTechModifiers;
export const selectMiningStats = (state) => state.mining.miningStats;

export const selectMiningTotals = createSelector(
  [selectMiningState],
  (miningState) => miningState.totals
);

export const selectMiningLoading = createSelector(
  [selectMiningState],
  (miningState) => miningState.loading
);

export const selectMiningError = createSelector(
  [selectMiningState],
  (miningState) => miningState.error
);

// Sélecteurs dérivés pour les calculs complexes
export const selectTotalMiningModifiers = createSelector(
  [selectMiningGeneralModifiers, selectMiningTechModifiers],
  (generalModifiers, techModifiers) => {
    const generalTotal = Array.isArray(generalModifiers)
      ? generalModifiers.reduce((sum, mod) => {
          const effect = typeof mod.effect === 'number' ? mod.effect : parseFloat(mod.effect) || 0;
          return sum + effect;
        }, 0)
      : 0;
    const techTotal = Array.isArray(techModifiers)
      ? techModifiers.reduce((sum, mod) => {
          const effect = typeof mod.effect === 'number' ? mod.effect : parseFloat(mod.effect) || 0;
          return sum + effect;
        }, 0)
      : 0;
    return generalTotal + techTotal;
  }
);

export const selectFormattedMiningStats = createSelector(
  [selectMiningStats],
  (stats) => {
    if (!stats) return {};

    // Ensure all values are numbers
    const production = typeof stats.production === 'number' ? stats.production : parseFloat(stats.production) || 0;
    const revenuePerMiner = typeof stats.revenuePerMiner === 'number' ? stats.revenuePerMiner : parseFloat(stats.revenuePerMiner) || 0;
    const totalBonus = typeof stats.totalBonus === 'number' ? stats.totalBonus : parseFloat(stats.totalBonus) || 0;

    return {
      ...stats,
      formattedProduction: production.toFixed(1),
      formattedRevenuePerMiner: revenuePerMiner.toFixed(1),
      formattedTotalBonus: `${(totalBonus * 100).toFixed(1)}%`
    };
  }
);

// Export reducer
export default miningSlice.reducer;
