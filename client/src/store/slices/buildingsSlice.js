import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { createSelector } from 'reselect';
import apiEndpoint from '../../services/ApiEndpoint';

// Async thunk pour récupérer les bâtiments
export const fetchBuildings = createAsyncThunk(
  'buildings/fetchBuildings',
  async (_, { rejectWithValue }) => {
    try {
      return await apiEndpoint.get('buildings');
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

// Async thunk pour récupérer les plans de bâtiments
export const fetchBuildingPlans = createAsyncThunk(
  'buildings/fetchBuildingPlans',
  async (_, { rejectWithValue }) => {
    try {
      return await apiEndpoint.get('buildings/plans');
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

// Async thunk pour démarrer un projet de construction
export const startConstructionProject = createAsyncThunk(
  'buildings/startConstructionProject',
  async ({ planId, name }, { rejectWithValue }) => {
    try {
      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:3001'}/api/buildings/construction`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ plan_id: planId, name }),
      });

      if (!response.ok) {
        throw new Error('Failed to start construction project');
      }

      return await response.json();
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

const initialState = {
  buildings: [],
  buildingPlans: [],
  constructionProjects: [],
  loading: false,
  error: null
};

const buildingsSlice = createSlice({
  name: 'buildings',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    }
  },
  extraReducers: (builder) => {
    builder
      // Gérer fetchBuildings
      .addCase(fetchBuildings.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchBuildings.fulfilled, (state, action) => {
        state.loading = false;
        state.buildings = action.payload;
      })
      .addCase(fetchBuildings.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Gérer fetchBuildingPlans
      .addCase(fetchBuildingPlans.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchBuildingPlans.fulfilled, (state, action) => {
        state.loading = false;
        state.buildingPlans = action.payload;
      })
      .addCase(fetchBuildingPlans.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Gérer startConstructionProject
      .addCase(startConstructionProject.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(startConstructionProject.fulfilled, (state, action) => {
        state.loading = false;
        state.constructionProjects.push(action.payload);
      })
      .addCase(startConstructionProject.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  }
});

// Export actions
export const { clearError } = buildingsSlice.actions;

// Sélecteurs de base
const selectBuildingsState = (state) => state.buildings;

// Sélecteurs optimisés avec createSelector pour la mémoisation
export const selectBuildings = createSelector(
  [selectBuildingsState],
  (buildingsState) => buildingsState.buildings
);

export const selectBuildingPlans = createSelector(
  [selectBuildingsState],
  (buildingsState) => buildingsState.buildingPlans
);

export const selectConstructionProjects = createSelector(
  [selectBuildingsState],
  (buildingsState) => buildingsState.constructionProjects
);

export const selectBuildingsLoading = createSelector(
  [selectBuildingsState],
  (buildingsState) => buildingsState.loading
);

export const selectBuildingsError = createSelector(
  [selectBuildingsState],
  (buildingsState) => buildingsState.error
);

// Sélecteurs dérivés
export const selectBuildingById = (id) => createSelector(
  [selectBuildings],
  (buildings) => buildings.find(building => building.id === id)
);

export const selectBuildingPlanById = (id) => createSelector(
  [selectBuildingPlans],
  (plans) => plans.find(plan => plan.id === id)
);

export const selectBuildingsByType = (type) => createSelector(
  [selectBuildings],
  (buildings) => buildings.filter(building => building.type === type)
);

export default buildingsSlice.reducer;
