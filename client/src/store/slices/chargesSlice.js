import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { modifiersApi } from '../../services/api';

// Table IDs
const CHARGES_GENERAL_TABLE_ID = 7;  // Coûts généraux
const CHARGES_GLOBAL_TABLE_ID = 8;   // Modificateurs généraux des charges

// Async thunk for fetching charges modifiers
export const fetchChargesModifiers = createAsyncThunk(
  'charges/fetchModifiers',
  async (_, { rejectWithValue }) => {
    try {
      // Fetch both types of modifiers in parallel
      const [generalMods, globalMods] = await Promise.all([
        modifiersApi.getModifiersByTable(CHARGES_GENERAL_TABLE_ID),
        modifiersApi.getModifiersByTable(CHARGES_GLOBAL_TABLE_ID)
      ]);

      return {
        chargesGeneralModifiers: generalMods,
        chargesGlobalModifiers: globalMods
      };
    } catch (error) {
      console.error('Error loading charges modifiers:', error);
      return rejectWithValue('Failed to load charges modifiers. Please try again.');
    }
  }
);

// Async thunk for adding a charges general modifier
export const addChargesGeneralModifier = createAsyncThunk(
  'charges/addGeneralModifier',
  async (modifierData, { rejectWithValue }) => {
    try {
      // Add the modifier
      const result = await modifiersApi.addModifier(CHARGES_GENERAL_TABLE_ID, modifierData);

      console.log('Charges general modifier added:', result);

      return { modifier: result.modifier, tableId: CHARGES_GENERAL_TABLE_ID };
    } catch (error) {
      console.error('Error adding charges general modifier:', error);
      return rejectWithValue('Failed to add charges general modifier. Please try again.');
    }
  }
);

// Async thunk for adding a charges global modifier
export const addChargesGlobalModifier = createAsyncThunk(
  'charges/addGlobalModifier',
  async (modifierData, { rejectWithValue }) => {
    try {
      // Add the modifier
      const result = await modifiersApi.addModifier(CHARGES_GLOBAL_TABLE_ID, modifierData);

      console.log('Charges global modifier added:', result);

      return { modifier: result.modifier, tableId: CHARGES_GLOBAL_TABLE_ID };
    } catch (error) {
      console.error('Error adding charges global modifier:', error);
      return rejectWithValue('Failed to add charges global modifier. Please try again.');
    }
  }
);

// Async thunk for deleting a charges general modifier
export const deleteChargesGeneralModifier = createAsyncThunk(
  'charges/deleteGeneralModifier',
  async (modifierId, { rejectWithValue }) => {
    try {
      // Delete the modifier
      const result = await modifiersApi.deleteModifier(modifierId);

      console.log('Charges general modifier deleted:', result);

      return { modifierId, result };
    } catch (error) {
      // If the modifier is not found, consider it a success
      if (error.message && error.message.includes('Modifier not found')) {
        console.log(`Modifier ${modifierId} already deleted`);
        return { modifierId, result: { success: true } };
      }

      console.error('Error deleting charges general modifier:', error);
      return rejectWithValue('Failed to delete charges general modifier. Please try again.');
    }
  }
);

// Async thunk for deleting a charges global modifier
export const deleteChargesGlobalModifier = createAsyncThunk(
  'charges/deleteGlobalModifier',
  async (modifierId, { rejectWithValue }) => {
    try {
      // Delete the modifier
      const result = await modifiersApi.deleteModifier(modifierId);

      console.log('Charges general modifier deleted:', result);

      return { modifierId, result };
    } catch (error) {
      // If the modifier is not found, consider it a success
      if (error.message && error.message.includes('Modifier not found')) {
        console.log(`Modifier ${modifierId} already deleted`);
        return { modifierId, result: { success: true } };
      }

      console.error('Error deleting charges general modifier:', error);
      return rejectWithValue('Failed to delete charges general modifier. Please try again.');
    }
  }
);



// Initial state
const initialState = {
  chargesGeneralModifiers: [],
  chargesGlobalModifiers: [],
  chargesStats: {
    salaries: 0,
    nonSalaryCharges: 0,
    totalCharges: 0,
    craftsmanEffect: 0,
    baseCharges: 0
  },
  totals: {
    chargesPerCycle: 0,
    chargesGlobalModifier: 0
  },
  jobs: [], // Stocker les jobs pour pouvoir recalculer les statistiques
  loading: false,
  error: null
};

/**
 * Calculate charges based on game state and modifiers
 * @param {Array} jobs - Array of jobs
 * @param {Number} chargesPerCycle - Sum of general charges
 * @param {Number} chargesGlobalModifier - Global charges modifier
 * @returns {Object} - Charges stats
 */
export const calculateChargesStats = (
  jobs,
  chargesPerCycle,
  chargesGlobalModifier
) => {
  if (!jobs || !Array.isArray(jobs)) return {
    salaries: 0,
    nonSalaryCharges: 0,
    totalCharges: 0,
    craftsmanEffect: 0,
    baseCharges: 0
  };

  // Calculate craftsman effect - 3% par artisan, effet max de 2 (divise les coûts par 2)
  const craftsman = jobs.find(j => j.name === 'Craftsman') || { number: 0, sick: 0, free: 0 };
  const activeCraftsmen = Math.max(0, (craftsman.number || 0) - (craftsman.sick || 0));
  const craftsmanEffect = Math.min(1, (0.03 * activeCraftsmen)); // Max 1 (ce qui donne un diviseur de 2 dans la formule)

  // Calculate salaries
  const salaries = jobs.reduce((sum, job) => {
    const jobNumber = job.number || 0;
    const jobFree = job.free || 0;
    const jobSalary = job.salary || 0;
    const jobTotal = (jobNumber - jobFree) * jobSalary;
    console.log(`Job ${job.name}: ${jobNumber} - ${jobFree} = ${jobNumber - jobFree} × ${jobSalary} = ${jobTotal}`);
    return sum + jobTotal;
  }, 0);

  console.log('Total salaries calculated in chargesSlice:', salaries);

  // Calculate non-salary charges
  // Nouvelle formule pour les modificateurs généraux: FixedCharges / (1 + (ChargesGlobalModifier * -1)) / (1 + CraftsmanEffect)
  const fixedCharges = chargesPerCycle || 0;

  // Ensure we don't divide by zero
  const globalModifierDivisor = 1 + ((chargesGlobalModifier || 0) * -1);
  const craftsmanDivisor = 1 + (craftsmanEffect || 0);

  const nonSalaryCharges = fixedCharges /
    (globalModifierDivisor === 0 ? 1 : globalModifierDivisor) /
    (craftsmanDivisor === 0 ? 1 : craftsmanDivisor);

  // Total charges
  const totalCharges = salaries + nonSalaryCharges;

  return {
    salaries,
    nonSalaryCharges,
    totalCharges,
    craftsmanEffect,
    baseCharges: fixedCharges
  };
};

// Create the charges slice
const chargesSlice = createSlice({
  name: 'charges',
  initialState,
  reducers: {
    // Update charges stats based on game state
    updateChargesStats: (state, action) => {
      const { gameState } = action.payload;

      if (!gameState) return;

      // Calculate totals - vérifier que les tableaux existent avant d'appeler reduce
      const chargesPerCycle = Array.isArray(state.chargesGeneralModifiers)
        ? state.chargesGeneralModifiers.reduce((sum, mod) => sum + (mod.effect || 0), 0)
        : 0;
      const chargesGlobalModifier = Array.isArray(state.chargesGlobalModifiers)
        ? state.chargesGlobalModifiers.reduce((sum, mod) => sum + (mod.effect || 0), 0)
        : 0;

      // Update totals
      state.totals = {
        chargesPerCycle,
        chargesGlobalModifier
      };

      // Utiliser les jobs directement depuis gameState (structure standardisée)
      const jobs = Array.isArray(gameState.jobs) ? gameState.jobs : [];

      // Stocker les jobs pour pouvoir les réutiliser lors des recalculs
      state.jobs = jobs;

      // S'assurer que les jobs sont à jour
      console.log('Calculating charges stats with jobs:', jobs.length, 'jobs');
      console.log('Using chargesPerCycle:', chargesPerCycle, 'and chargesGlobalModifier:', chargesGlobalModifier);

      // Calculate charges stats
      const chargesStats = calculateChargesStats(
        jobs,
        chargesPerCycle,
        chargesGlobalModifier
      );

      // Update charges stats
      state.chargesStats = chargesStats;

      // Log pour le débogage
      console.log('Updated charges stats:', chargesStats);
    }
  },
  extraReducers: (builder) => {
    builder
      // Handle fetchChargesModifiers
      .addCase(fetchChargesModifiers.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchChargesModifiers.fulfilled, (state, action) => {
        state.loading = false;
        state.chargesGeneralModifiers = action.payload.chargesGeneralModifiers || [];
        state.chargesGlobalModifiers = action.payload.chargesGlobalModifiers || [];

        // Recalculer les totaux immédiatement après avoir mis à jour les modificateurs
        const chargesPerCycle = Array.isArray(action.payload.chargesGeneralModifiers)
          ? action.payload.chargesGeneralModifiers.reduce((sum, mod) => sum + (mod.effect || 0), 0)
          : 0;
        const chargesGlobalModifier = Array.isArray(action.payload.chargesGlobalModifiers)
          ? action.payload.chargesGlobalModifiers.reduce((sum, mod) => sum + (mod.effect || 0), 0)
          : 0;

        // Mettre à jour les totaux
        state.totals = {
          chargesPerCycle,
          chargesGlobalModifier
        };

        console.log('Updated charges totals after fetching modifiers:', state.totals);

        // Utiliser les jobs stockés pour recalculer les statistiques
        if (state.jobs && state.jobs.length > 0) {
          // Recalculer les statistiques des charges avec les nouveaux totaux
          const updatedChargesStats = calculateChargesStats(
            state.jobs,
            chargesPerCycle,
            chargesGlobalModifier
          );

          // Mettre à jour les statistiques des charges
          state.chargesStats = updatedChargesStats;

          console.log('Recalculated charges stats after modifier update:', updatedChargesStats);
        } else {
          console.log('No jobs available to recalculate charges stats');
        }
      })
      .addCase(fetchChargesModifiers.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Handle deleteChargesGeneralModifier
      .addCase(deleteChargesGeneralModifier.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteChargesGeneralModifier.fulfilled, (state, action) => {
        state.loading = false;
        // Remove the deleted modifier from the state
        if (action.payload && action.payload.modifierId) {
          state.chargesGeneralModifiers = state.chargesGeneralModifiers.filter(
            modifier => modifier.id !== action.payload.modifierId
          );
          console.log('Charges general modifier removed from state:', action.payload.modifierId);

          // Recalculate the total effect
          const chargesPerCycle = state.chargesGeneralModifiers.reduce(
            (sum, mod) => sum + (mod.effect || 0), 0
          );

          // Update totals
          state.totals = {
            ...state.totals,
            chargesPerCycle
          };

          console.log('Charges per cycle recalculated:', chargesPerCycle);
        }
      })
      .addCase(deleteChargesGeneralModifier.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Handle deleteChargesGlobalModifier
      .addCase(deleteChargesGlobalModifier.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteChargesGlobalModifier.fulfilled, (state, action) => {
        state.loading = false;
        // Remove the deleted modifier from the state
        if (action.payload && action.payload.modifierId) {
          state.chargesGlobalModifiers = state.chargesGlobalModifiers.filter(
            modifier => modifier.id !== action.payload.modifierId
          );
          console.log('Charges global modifier removed from state:', action.payload.modifierId);

          // Recalculate the total effect
          const chargesGlobalModifier = state.chargesGlobalModifiers.reduce(
            (sum, mod) => sum + (mod.effect || 0), 0
          );

          // Update totals
          state.totals = {
            ...state.totals,
            chargesGlobalModifier
          };

          console.log('Charges global modifier recalculated:', chargesGlobalModifier);
        }
      })
      .addCase(deleteChargesGlobalModifier.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Handle addChargesGeneralModifier
      .addCase(addChargesGeneralModifier.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(addChargesGeneralModifier.fulfilled, (state, action) => {
        state.loading = false;
        // Add the new modifier to the state
        if (action.payload && action.payload.modifier) {
          state.chargesGeneralModifiers = [...state.chargesGeneralModifiers, action.payload.modifier];
          console.log('Charges general modifier added to state:', action.payload.modifier);

          // Recalculate the total effect
          const chargesPerCycle = state.chargesGeneralModifiers.reduce(
            (sum, mod) => sum + (mod.effect || 0), 0
          );

          // Update totals
          state.totals = {
            ...state.totals,
            chargesPerCycle
          };

          console.log('Charges per cycle recalculated:', chargesPerCycle);
        }
      })
      .addCase(addChargesGeneralModifier.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Handle addChargesGlobalModifier
      .addCase(addChargesGlobalModifier.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(addChargesGlobalModifier.fulfilled, (state, action) => {
        state.loading = false;
        // Add the new modifier to the state
        if (action.payload && action.payload.modifier) {
          state.chargesGlobalModifiers = [...state.chargesGlobalModifiers, action.payload.modifier];
          console.log('Charges global modifier added to state:', action.payload.modifier);

          // Recalculate the total effect
          const chargesGlobalModifier = state.chargesGlobalModifiers.reduce(
            (sum, mod) => sum + (mod.effect || 0), 0
          );

          // Update totals
          state.totals = {
            ...state.totals,
            chargesGlobalModifier
          };

          console.log('Charges global modifier recalculated:', chargesGlobalModifier);
        }
      })
      .addCase(addChargesGlobalModifier.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  }
});

// Export actions
export const { updateChargesStats } = chargesSlice.actions;

// Sélecteurs simplifiés pour garantir la détection des changements
export const selectChargesGeneralModifiers = (state) => state.charges.chargesGeneralModifiers;
export const selectChargesGlobalModifiers = (state) => state.charges.chargesGlobalModifiers;
export const selectChargesStats = (state) => state.charges.chargesStats;
export const selectChargesTotals = (state) => state.charges.totals;
export const selectChargesLoading = (state) => state.charges.loading;
export const selectChargesError = (state) => state.charges.error;

// Sélecteurs dérivés pour les calculs complexes
export const selectFormattedChargesStats = (state) => {
  const stats = selectChargesStats(state);
  const totals = selectChargesTotals(state);

  if (!stats || !totals) return {};

  return {
    ...stats,
    formattedSalaries: stats.salaries ? parseFloat(stats.salaries).toFixed(1) : '0.0',
    formattedNonSalaryCharges: stats.nonSalaryCharges ? parseFloat(stats.nonSalaryCharges).toFixed(1) : '0.0',
    formattedTotalCharges: stats.totalCharges ? parseFloat(stats.totalCharges).toFixed(1) : '0.0',
    formattedCraftsmanEffect: `${((1/(1+stats.craftsmanEffect))-1) * 100}%`,
    formattedGlobalModifierEffect: `${((1/(1+(totals.chargesGlobalModifier * -1)))-1) * 100}%`
  };
};

// Export reducer
export default chargesSlice.reducer;
