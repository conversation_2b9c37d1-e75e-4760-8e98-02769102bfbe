import { createSlice, createAsyncThunk, createSelector } from '@reduxjs/toolkit';
import { modifiersApi } from '../../services/api';

// Constants
export const MATERIALS_PER_WORKER = 4;
export const MATERIALS_PER_MINER = 2;

// Table IDs
const MATERIALS_GENERAL_TABLE_ID = 11; // General materials effects
const MATERIALS_TECH_TABLE_ID = 12;    // Technology effects on materials

// Async thunk for fetching materials modifiers
export const fetchMaterialsModifiers = createAsyncThunk(
  'materials/fetchModifiers',
  async (_, { rejectWithValue }) => {
    try {
      console.log('materialsSlice: fetchMaterialsModifiers appelé');

      // Fetch both types of modifiers in parallel
      const [generalMods, techMods] = await Promise.all([
        modifiersApi.getModifiersByTable(MATERIALS_GENERAL_TABLE_ID),
        modifiersApi.getModifiersByTable(MATERIALS_TECH_TABLE_ID)
      ]);

      console.log('materialsSlice: Modificateurs récupérés', {
        generalMods,
        techMods
      });

      return {
        materialsGeneralModifiers: generalMods,
        materialsTechModifiers: techMods
      };
    } catch (error) {
      console.error('Error loading materials modifiers:', error);
      return rejectWithValue('Failed to load materials modifiers. Please try again.');
    }
  }
);

// Initial state
const initialState = {
  materialsGeneralModifiers: [],
  materialsTechModifiers: [],
  materialsStats: {
    workers: 0,
    miners: 0,
    production: 0,
    baseProduction: 0,
    totalBonus: 0
  },
  totals: {
    generalEffectsTotal: 0,
    techEffectsTotal: 0,
    totalProductionBonus: 0
  },
  loading: false,
  error: null
};

/**
 * Calculate materials production based on workers, miners, and modifiers
 * @param {Number} activeWorkers - Number of active workers
 * @param {Number} activeMiners - Number of active miners
 * @param {Number} generalEffectsTotal - Total of general effects
 * @param {Number} techEffectsTotal - Total of tech effects
 * @param {Number} moralModifier - Moral modifier value
 * @returns {Number} - Materials production value
 */
export const calculateMaterialsProduction = (
  activeWorkers,
  activeMiners,
  generalEffectsTotal,
  techEffectsTotal,
  moralModifier
) => {
  // Calculate base production
  const baseProduction = (activeWorkers * MATERIALS_PER_WORKER) + (activeMiners * MATERIALS_PER_MINER);

  // Calculate materials production
  const materialsProduction = baseProduction * (1 + generalEffectsTotal + techEffectsTotal + moralModifier);

  return materialsProduction;
};

/**
 * Calculate total bonus for materials production
 * @param {Number} generalEffectsTotal - Total of general effects
 * @param {Number} techEffectsTotal - Total of tech effects
 * @param {Number} moralModifier - Moral modifier value
 * @returns {Number} - Total bonus as a decimal (e.g., 0.25 for 25%)
 */
export const calculateTotalBonus = (
  generalEffectsTotal,
  techEffectsTotal,
  moralModifier
) => {
  return (1 + generalEffectsTotal + techEffectsTotal + moralModifier) - 1;
};

// Create the materials slice
const materialsSlice = createSlice({
  name: 'materials',
  initialState,
  reducers: {
    // Update materials stats based on game state
    updateMaterialsStats: (state, action) => {
      const { gameState, moralModifier, moralTitle } = action.payload;

      if (!gameState) return;

      // Use moral modifier and title passed from the component
      console.log('materialsSlice: updateMaterialsStats with moral:', { moralModifier, moralTitle });

      // Calculate totals - vérifier que les tableaux existent avant d'appeler reduce
      // et que les effets sont des nombres
      const generalEffectsTotal = Array.isArray(state.materialsGeneralModifiers)
        ? state.materialsGeneralModifiers.reduce((sum, mod) => {
            const effect = typeof mod.effect === 'number' ? mod.effect : parseFloat(mod.effect) || 0;
            return sum + effect;
          }, 0)
        : 0;
      const techEffectsTotal = Array.isArray(state.materialsTechModifiers)
        ? state.materialsTechModifiers.reduce((sum, mod) => {
            const effect = typeof mod.effect === 'number' ? mod.effect : parseFloat(mod.effect) || 0;
            return sum + effect;
          }, 0)
        : 0;

      console.log('materialsSlice: Effects totals:', {
        generalEffectsTotal,
        techEffectsTotal
      });
      const totalProductionBonus = generalEffectsTotal + techEffectsTotal;

      // Utiliser les jobs directement depuis gameState (structure standardisée)
      const jobs = Array.isArray(gameState.jobs) ? gameState.jobs : [];

      // Get workers count
      const workers = jobs.find(j => j.name === 'Worker') || { number: 0, sick: 0 };
      const activeWorkers = workers.number - workers.sick;

      // Get miners count
      const miners = jobs.find(j => j.name === 'Miner') || { number: 0, sick: 0 };
      const activeMiners = miners.number - miners.sick;

      // Update totals
      state.totals = {
        generalEffectsTotal,
        techEffectsTotal,
        totalProductionBonus
      };

      // Calculate base production
      const baseProduction = (activeWorkers * MATERIALS_PER_WORKER) + (activeMiners * MATERIALS_PER_MINER);

      // Calculate materials production using the centralized calculation service
      const materialsProduction = calculateMaterialsProduction(
        activeWorkers,
        activeMiners,
        generalEffectsTotal,
        techEffectsTotal,
        moralModifier
      );

      // Calculate total bonus using the centralized calculation service
      const totalBonus = calculateTotalBonus(
        generalEffectsTotal,
        techEffectsTotal,
        moralModifier
      );

      // Update materials stats
      state.materialsStats = {
        workers: activeWorkers,
        miners: activeMiners,
        production: materialsProduction,
        baseProduction: baseProduction,
        totalBonus: totalBonus,
        moralModifier,
        moralTitle
      };
    }
  },
  extraReducers: (builder) => {
    builder
      // Handle fetchMaterialsModifiers
      .addCase(fetchMaterialsModifiers.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchMaterialsModifiers.fulfilled, (state, action) => {
        state.loading = false;
        state.materialsGeneralModifiers = action.payload.materialsGeneralModifiers;
        state.materialsTechModifiers = action.payload.materialsTechModifiers;
      })
      .addCase(fetchMaterialsModifiers.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  }
});

// Export actions
export const { updateMaterialsStats } = materialsSlice.actions;

// Sélecteurs de base
const selectMaterialsState = (state) => state.materials;

// Export selectors - utiliser des sélecteurs simples pour les propriétés directes
export const selectMaterialsGeneralModifiers = (state) => state.materials.materialsGeneralModifiers;
export const selectMaterialsTechModifiers = (state) => state.materials.materialsTechModifiers;
export const selectMaterialsStats = (state) => state.materials.materialsStats;

export const selectMaterialsTotals = createSelector(
  [selectMaterialsState],
  (materialsState) => materialsState.totals
);

export const selectMaterialsLoading = createSelector(
  [selectMaterialsState],
  (materialsState) => materialsState.loading
);

export const selectMaterialsError = createSelector(
  [selectMaterialsState],
  (materialsState) => materialsState.error
);

// Sélecteurs dérivés pour les calculs complexes
export const selectTotalMaterialsModifiers = createSelector(
  [selectMaterialsGeneralModifiers, selectMaterialsTechModifiers],
  (generalModifiers, techModifiers) => {
    const generalTotal = Array.isArray(generalModifiers)
      ? generalModifiers.reduce((sum, mod) => {
          const effect = typeof mod.effect === 'number' ? mod.effect : parseFloat(mod.effect) || 0;
          return sum + effect;
        }, 0)
      : 0;
    const techTotal = Array.isArray(techModifiers)
      ? techModifiers.reduce((sum, mod) => {
          const effect = typeof mod.effect === 'number' ? mod.effect : parseFloat(mod.effect) || 0;
          return sum + effect;
        }, 0)
      : 0;
    return generalTotal + techTotal;
  }
);

export const selectFormattedMaterialsStats = createSelector(
  [selectMaterialsStats],
  (stats) => {
    if (!stats) return {};

    // Ensure all values are numbers
    const production = typeof stats.production === 'number' ? stats.production : parseFloat(stats.production) || 0;
    const baseProduction = typeof stats.baseProduction === 'number' ? stats.baseProduction : parseFloat(stats.baseProduction) || 0;
    const totalBonus = typeof stats.totalBonus === 'number' ? stats.totalBonus : parseFloat(stats.totalBonus) || 0;

    return {
      ...stats,
      formattedProduction: production.toFixed(1),
      formattedBaseProduction: baseProduction.toFixed(1),
      formattedTotalBonus: `${(totalBonus * 100).toFixed(1)}%`
    };
  }
);

// Export reducer
export default materialsSlice.reducer;
