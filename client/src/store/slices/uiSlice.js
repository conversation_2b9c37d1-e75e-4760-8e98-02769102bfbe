import { createSlice } from '@reduxjs/toolkit';
import { createSelector } from 'reselect';
import { processNextCycle } from './gameSlice';

// Initial state
const initialState = {
  activeTab: 'dashboard',
  showCycleModal: false,
  moralUpdated: 0,
  healthUpdated: 0,
  researchUpdated: 0,
  tradingUpdated: 0,
  justiceDefenseUpdated: 0,
  isShiftPressed: false,
  // État pour suivre le chargement par onglet
  loadingTabs: {},
  // Timestamp de la dernière mise à jour par onglet
  lastTabUpdate: {},
  // Timestamp du dernier chargement par onglet
  tabLastLoaded: {},
};

// Create the UI slice
const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    setActiveTab: (state, action) => {
      state.activeTab = action.payload;
    },
    setShowCycleModal: (state, action) => {
      state.showCycleModal = action.payload;
    },
    incrementMoralUpdated: (state) => {
      state.moralUpdated += 1;
    },
    incrementHealthUpdated: (state) => {
      state.healthUpdated += 1;
    },
    incrementResearchUpdated: (state) => {
      state.researchUpdated += 1;
    },
    incrementTradingUpdated: (state) => {
      state.tradingUpdated += 1;
    },
    incrementJusticeDefenseUpdated: (state) => {
      state.justiceDefenseUpdated += 1;
    },
    setShiftPressed: (state, action) => {
      state.isShiftPressed = action.payload;
    },
    resetMoralUpdated: (state) => {
      state.moralUpdated = 0;
    },
    resetHealthUpdated: (state) => {
      state.healthUpdated = 0;
    },
    resetResearchUpdated: (state) => {
      state.researchUpdated = 0;
    },
    resetTradingUpdated: (state) => {
      state.tradingUpdated = 0;
    },
    resetJusticeDefenseUpdated: (state) => {
      state.justiceDefenseUpdated = 0;
    },
    // Reducers pour gérer l'état de chargement par onglet
    setTabLoading: (state, action) => {
      const { tabName, loading } = action.payload;
      state.loadingTabs = {
        ...state.loadingTabs,
        [tabName]: loading
      };

      // Si le chargement est terminé, mettre à jour le timestamp
      if (!loading) {
        state.lastTabUpdate = {
          ...state.lastTabUpdate,
          [tabName]: Date.now()
        };
      }
    },
    // Marquer le timestamp du dernier chargement d'un onglet
    setTabLastLoaded: (state, action) => {
      const { tabName, timestamp } = action.payload;
      state.tabLastLoaded = {
        ...state.tabLastLoaded,
        [tabName]: timestamp
      };
    },
    // Marquer tous les onglets comme non chargés
    resetAllTabsLoading: (state) => {
      state.loadingTabs = {};
    },
  },
  extraReducers: (builder) => {
    builder
      // Show cycle modal when next cycle is processed
      .addCase(processNextCycle.fulfilled, (state) => {
        state.showCycleModal = true;
      });
  },
});

// Export actions
export const {
  setActiveTab,
  setShowCycleModal,
  incrementMoralUpdated,
  incrementHealthUpdated,
  incrementResearchUpdated,
  incrementTradingUpdated,
  incrementJusticeDefenseUpdated,
  setShiftPressed,
  resetMoralUpdated,
  resetHealthUpdated,
  resetResearchUpdated,
  resetTradingUpdated,
  resetJusticeDefenseUpdated,
  // Actions de gestion des onglets
  setTabLoading,
  setTabLastLoaded,
  resetAllTabsLoading,
} = uiSlice.actions;

// Sélecteurs de base - non mémorisés, utilisés comme dépendances pour les sélecteurs mémorisés
const selectLoadingTabs = (state) => state.ui.loadingTabs || {};
const selectLastTabUpdateState = (state) => state.ui.lastTabUpdate || {};
const selectTabLastLoadedState = (state) => state.ui.tabLastLoaded || {};

// Sélecteurs simples pour les propriétés directes
export const selectActiveTab = (state) => state.ui.activeTab;
export const selectShowCycleModal = (state) => state.ui.showCycleModal;
export const selectMoralUpdated = (state) => state.ui.moralUpdated;
export const selectHealthUpdated = (state) => state.ui.healthUpdated;
export const selectResearchUpdated = (state) => state.ui.researchUpdated;
export const selectTradingUpdated = (state) => state.ui.tradingUpdated;
export const selectJusticeDefenseUpdated = (state) => state.ui.justiceDefenseUpdated;
export const selectIsShiftPressed = (state) => state.ui.isShiftPressed;

// Sélecteurs mémorisés pour les onglets
export const selectTabLoading = (tabName) => createSelector(
  [selectLoadingTabs],
  (loadingTabs) => loadingTabs[tabName] || false
);

export const selectLastTabUpdate = (tabName) => createSelector(
  [selectLastTabUpdateState],
  (lastTabUpdate) => lastTabUpdate[tabName] || null
);

export const selectTabLastLoaded = (tabName) => createSelector(
  [selectTabLastLoadedState],
  (tabLastLoaded) => tabLastLoaded[tabName] || null
);

// Sélecteur dérivé pour obtenir toutes les informations d'un onglet
export const selectTabInfo = (tabName) => createSelector(
  [
    selectTabLoading(tabName),
    selectLastTabUpdate(tabName),
    selectTabLastLoaded(tabName)
  ],
  (loading, lastUpdate, lastLoaded) => ({
    loading,
    lastUpdate,
    lastLoaded,
    needsRefresh: lastLoaded && lastUpdate ? lastUpdate > lastLoaded : true
  })
);

// Export reducer
export default uiSlice.reducer;
