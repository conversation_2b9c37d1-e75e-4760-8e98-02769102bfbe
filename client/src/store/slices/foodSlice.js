import { createSlice, createAsyncThunk, createSelector } from '@reduxjs/toolkit';
import { modifiersApi } from '../../services/api';

// Constants
const FOOD_PER_FARMER = 4;
const FOOD_GENERAL_TABLE_ID = 1; // General food effects
const FOOD_TECH_TABLE_ID = 2;    // Technology effects on food
const PERISHABLE_TABLE_ID = 3;   // Perishable rate

// Async thunk for fetching food modifiers
export const fetchFoodModifiers = createAsyncThunk(
  'food/fetchModifiers',
  async (_, { rejectWithValue }) => {
    try {
      console.log('foodSlice: Fetching food modifiers...');

      // Fetch all three types of modifiers in parallel using the modifiersApi service
      const [generalMods, techMods, perishableMods] = await Promise.all([
        modifiersApi.getModifiersByTable(FOOD_GENERAL_TABLE_ID),
        modifiersApi.getModifiersByTable(FOOD_TECH_TABLE_ID),
        modifiersApi.getModifiersByTable(PERISHABLE_TABLE_ID)
      ]);

      // Ensure the modifiers are arrays
      const foodGeneralModifiers = Array.isArray(generalMods) ? generalMods : [];
      const foodTechModifiers = Array.isArray(techMods) ? techMods : [];
      const perishableModifiers = Array.isArray(perishableMods) ? perishableMods : [];

      console.log('foodSlice: Food modifiers fetched successfully:', {
        foodGeneralModifiers,
        foodTechModifiers,
        perishableModifiers
      });

      return {
        foodGeneralModifiers,
        foodTechModifiers,
        perishableModifiers
      };
    } catch (error) {
      console.error('Error loading food modifiers:', error);
      return rejectWithValue('Failed to load food modifiers. Please try again.');
    }
  }
);

// Initial state
const initialState = {
  foodGeneralModifiers: [],
  foodTechModifiers: [],
  perishableModifiers: [],
  foodStats: {
    farmers: 0,
    farmersSick: 0,
    production: 0,
    consumption: 0,
    net: 0,
    currentReserves: 0,
    perishableLoss: 0,
    netAfterPerishable: 0,
    seasonFactor: 0
  },
  totals: {
    generalEffectsTotal: 0,
    techEffectsTotal: 0,
    totalProductionBonus: 0,
    perishableFactor: 0
  },
  loading: false,
  error: null
};

/**
 * Calculate food production based on farmers and modifiers
 * @param {Number} effectiveFarmers - Number of active farmers
 * @param {Number} seasonFactor - Season factor (0.5 to 1.5)
 * @param {Number} generalEffectsTotal - Total of general effects
 * @param {Number} techEffectsTotal - Total of tech effects
 * @param {Number} moralModifier - Moral modifier value
 * @returns {Number} - Food production value
 */
export const calculateFoodProduction = (
  effectiveFarmers,
  seasonFactor,
  generalEffectsTotal,
  techEffectsTotal,
  moralModifier
) => {
  // Ensure all values are numbers
  const farmers = typeof effectiveFarmers === 'number' ? effectiveFarmers : parseFloat(effectiveFarmers) || 0;
  const season = typeof seasonFactor === 'number' ? seasonFactor : parseFloat(seasonFactor) || 1.0;
  const generalEffects = typeof generalEffectsTotal === 'number' ? generalEffectsTotal : parseFloat(generalEffectsTotal) || 0;
  const techEffects = typeof techEffectsTotal === 'number' ? techEffectsTotal : parseFloat(techEffectsTotal) || 0;
  const moral = typeof moralModifier === 'number' ? moralModifier : parseFloat(moralModifier) || 0;

  console.log('calculateFoodProduction: Input values:', {
    farmers,
    season,
    generalEffects,
    techEffects,
    moral
  });

  // Calculate base production
  const baseProduction = farmers * FOOD_PER_FARMER;

  // Apply season factor
  const seasonalProduction = baseProduction * season;

  // Apply modifiers
  const modifierMultiplier = 1 + generalEffects + techEffects + moral;

  // Calculate final production
  const foodProduction = seasonalProduction * modifierMultiplier;

  console.log('calculateFoodProduction: Calculated values:', {
    baseProduction,
    seasonalProduction,
    modifierMultiplier,
    foodProduction
  });

  // Round to 1 decimal place
  return Math.round(foodProduction * 10) / 10;
};

/**
 * Calculate food stats based on game state and modifiers
 * @param {Object} jobs - Jobs array from game state
 * @param {Number} seasonFactor - Season factor from game state
 * @param {Number} currentReserves - Current food reserves
 * @param {Number} generalEffectsTotal - Total of general effects
 * @param {Number} techEffectsTotal - Total of tech effects
 * @param {Number} perishableFactor - Perishable factor
 * @param {Number} moralModifier - Moral modifier value
 * @returns {Object} - Food stats object
 */
export const calculateFoodStats = (
  jobs,
  seasonFactor,
  currentReserves,
  generalEffectsTotal,
  techEffectsTotal,
  perishableFactor,
  moralModifier,
  serverFoodData = null // New parameter to accept server-calculated values
) => {
  // If we have server-calculated values, use them directly
  if (serverFoodData) {
    console.log('calculateFoodStats: Using server-calculated values:', serverFoodData);

    // Get farmers count for display purposes
    let farmers = jobs.find(j => j.name === 'Farmer');
    if (!farmers) {
      farmers = jobs.find(j => j.id === 7);
    }
    if (!farmers) {
      farmers = { number: 0, sick: 0 };
    }

    const farmersSick = parseInt(farmers.sick || 0, 10);
    const farmersNumber = parseInt(farmers.number || 0, 10);
    const effectiveFarmers = farmersNumber - farmersSick;

    // Check if we have next season factor information
    const nextSeasonFactor = serverFoodData.nextSeasonFactor;
    console.log('calculateFoodStats: Next season factor from server:', nextSeasonFactor);

    // If we have next season factor, use it for prediction
    if (nextSeasonFactor !== undefined) {
      // Calculate production with next season factor
      const nextProduction = calculateFoodProduction(
        effectiveFarmers,
        nextSeasonFactor,
        generalEffectsTotal,
        techEffectsTotal,
        moralModifier
      );

      // Calculate net with next season factor
      const nextNet = Math.round((nextProduction - serverFoodData.consumption) * 10) / 10;

      // Calculate perishable loss (this doesn't change with season)
      const perishableLoss = serverFoodData.perishableLoss;

      // Calculate net after perishable with next season factor
      const nextNetAfterPerishable = Math.round((nextNet - perishableLoss) * 10) / 10;

      console.log('calculateFoodStats: Prediction with next season factor:', {
        currentProduction: serverFoodData.production,
        nextProduction,
        currentNet: serverFoodData.net,
        nextNet,
        currentNetAfterPerishable: serverFoodData.netAfterPerishable,
        nextNetAfterPerishable
      });

      // Use values with next season factor for prediction
      return {
        farmers: effectiveFarmers,
        farmersSick,
        production: nextProduction,
        consumption: serverFoodData.consumption,
        net: nextNet,
        currentReserves: currentReserves,
        perishableLoss: perishableLoss,
        netAfterPerishable: nextNetAfterPerishable,
        seasonFactor: nextSeasonFactor, // Use next season factor
        isNextSeasonPrediction: true
      };
    }

    // Use server values as is if no next season factor
    return {
      farmers: effectiveFarmers,
      farmersSick,
      production: serverFoodData.production,
      consumption: serverFoodData.consumption,
      net: serverFoodData.net,
      currentReserves: currentReserves,
      perishableLoss: serverFoodData.perishableLoss,
      netAfterPerishable: serverFoodData.netAfterPerishable,
      seasonFactor
    };
  }
  // Get farmers count - make sure to handle all possible data structures
  let farmers = jobs.find(j => j.name === 'Farmer');

  // If no farmers found, try to find by ID 7 (which is the Farmer ID in the database)
  if (!farmers) {
    farmers = jobs.find(j => j.id === 7);
  }

  // Default values if still not found
  if (!farmers) {
    farmers = { number: 0, sick: 0 };
  }

  // Ensure we have valid numbers
  const farmersSick = parseInt(farmers.sick || 0, 10);
  const farmersNumber = parseInt(farmers.number || 0, 10);
  const effectiveFarmers = farmersNumber - farmersSick;

  // Log detailed information about farmers for debugging
  console.log('calculateFoodStats: Detailed farmers data:', {
    farmersObject: farmers,
    farmersNumber,
    farmersSick,
    effectiveFarmers,
    farmersType: typeof farmers.number,
    jobsLength: jobs.length
  });

  console.log('Farmers data:', {
    farmers,
    farmersNumber,
    farmersSick,
    effectiveFarmers,
    jobsLength: jobs.length,
    jobNames: jobs.map(j => j.name)
  });

  // Get total inhabitants for consumption
  const totalInhabitants = jobs.reduce((sum, job) => sum + job.number, 0) || 0;

  // Ensure all values are numbers
  const season = typeof seasonFactor === 'number' ? seasonFactor : parseFloat(seasonFactor) || 1.0;
  const reserves = typeof currentReserves === 'number' ? currentReserves : parseFloat(currentReserves) || 0;
  const generalEffects = typeof generalEffectsTotal === 'number' ? generalEffectsTotal : parseFloat(generalEffectsTotal) || 0;
  const techEffects = typeof techEffectsTotal === 'number' ? techEffectsTotal : parseFloat(techEffectsTotal) || 0;
  const perishable = typeof perishableFactor === 'number' ? perishableFactor : parseFloat(perishableFactor) || 0;
  const moral = typeof moralModifier === 'number' ? moralModifier : parseFloat(moralModifier) || 0;

  console.log('calculateFoodStats: Normalized values:', {
    effectiveFarmers,
    season,
    reserves,
    generalEffects,
    techEffects,
    perishable,
    moral
  });

  // Calculate production
  const production = calculateFoodProduction(
    effectiveFarmers,
    season,
    generalEffects,
    techEffects,
    moral
  );

  // Calculate consumption (1 unit per inhabitant)
  const consumption = Math.round(totalInhabitants * 10) / 10;

  // Calculate net food change
  const net = Math.round((production - consumption) * 10) / 10;

  // Calculate perishable loss
  // Use the perishable factor as is, without forcing a default value
  console.log('calculateFoodStats: Perishable factor before:', {
    perishable,
    isZero: perishable === 0,
    type: typeof perishable
  });

  const effectivePerishableFactor = perishable; // Use the value as is

  console.log('calculateFoodStats: Perishable factor after:', {
    effectivePerishableFactor,
    isZero: effectivePerishableFactor === 0,
    type: typeof effectivePerishableFactor
  });

  const perishableLoss = Math.round((reserves * effectivePerishableFactor) * 10) / 10;

  // Calculate net after perishable
  const netAfterPerishable = Math.round((net - perishableLoss) * 10) / 10;

  // Calculate expected new reserves
  const expectedNewReserves = Math.max(0, reserves + netAfterPerishable);

  console.log('calculateFoodStats: Calculated values:', {
    production,
    consumption,
    net,
    perishableLoss,
    netAfterPerishable,
    currentReserves: reserves,
    expectedNewReserves,
    expectedChange: netAfterPerishable,
    expectedChangeWithoutPerishable: net
  });

  return {
    farmers: effectiveFarmers,
    farmersSick,
    production,
    consumption,
    net,
    currentReserves,
    perishableLoss,
    netAfterPerishable,
    seasonFactor
  };
};

// Create the food slice
const foodSlice = createSlice({
  name: 'food',
  initialState,
  reducers: {
    // Update food stats based on game state
    updateFoodStats: (state, action) => {
      const { gameState, moralModifier, moralTitle } = action.payload;

      if (!gameState) return;

      console.log('foodSlice: Updating food stats with gameState and moral:', {
        gameState,
        moralModifier,
        moralTitle
      });

      console.log('foodSlice: Modifiers in state:', {
        foodGeneralModifiers: state.foodGeneralModifiers,
        foodTechModifiers: state.foodTechModifiers,
        perishableModifiers: state.perishableModifiers
      });

      // Calculate totals
      const generalEffectsTotal = Array.isArray(state.foodGeneralModifiers)
        ? state.foodGeneralModifiers.reduce((sum, mod) => sum + (mod.effect || 0), 0)
        : 0;
      const techEffectsTotal = Array.isArray(state.foodTechModifiers)
        ? state.foodTechModifiers.reduce((sum, mod) => sum + (mod.effect || 0), 0)
        : 0;
      // Calculate perishable factor with detailed logging
      let perishableFactor = 0;
      if (Array.isArray(state.perishableModifiers)) {
        console.log('foodSlice: Calculating perishable factor from modifiers:', state.perishableModifiers);

        // Calculate sum with detailed logging of each modifier
        perishableFactor = state.perishableModifiers.reduce((sum, mod) => {
          const effect = mod.effect || 0;
          console.log(`foodSlice: Modifier ${mod.id} (${mod.title}) effect: ${effect}, running sum: ${sum + effect}`);
          return sum + effect;
        }, 0);

        console.log('foodSlice: Final perishable factor:', perishableFactor);
      } else {
        console.log('foodSlice: No perishable modifiers found, using default 0');
      }
      const totalProductionBonus = generalEffectsTotal + techEffectsTotal;

      console.log('foodSlice: Calculated totals:', {
        generalEffectsTotal,
        techEffectsTotal,
        perishableFactor,
        totalProductionBonus
      });

      // Update totals
      state.totals = {
        generalEffectsTotal,
        techEffectsTotal,
        totalProductionBonus,
        perishableFactor
      };

      // Utiliser les jobs directement depuis gameState (structure standardisée)
      const jobs = Array.isArray(gameState.jobs) ? gameState.jobs : [];

      console.log('Jobs from gameState:', {
        jobsLength: jobs.length,
        firstJob: jobs.length > 0 ? jobs[0] : null
      });

      // Get season factor
      const seasonFactor = gameState?.season_factor || 1.0;

      // Get current reserves
      const currentReserves = gameState?.food_reserves || 0;

      // Get server-calculated food data
      const serverFoodData = gameState?.foodData;

      // Calculate food stats, passing server data if available
      const foodStats = calculateFoodStats(
        jobs,
        seasonFactor,
        currentReserves,
        generalEffectsTotal,
        techEffectsTotal,
        perishableFactor,
        moralModifier,
        serverFoodData
      );

      // Update food stats
      state.foodStats = foodStats;
    }
  },
  extraReducers: (builder) => {
    builder
      // Handle fetchFoodModifiers
      .addCase(fetchFoodModifiers.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchFoodModifiers.fulfilled, (state, action) => {
        state.loading = false;
        state.foodGeneralModifiers = action.payload.foodGeneralModifiers;
        state.foodTechModifiers = action.payload.foodTechModifiers;
        state.perishableModifiers = action.payload.perishableModifiers;

        console.log('foodSlice: Modifiers fetched successfully:', {
          foodGeneralModifiers: action.payload.foodGeneralModifiers,
          foodTechModifiers: action.payload.foodTechModifiers,
          perishableModifiers: action.payload.perishableModifiers
        });

        // Recalculate totals immediately
        const generalEffectsTotal = Array.isArray(action.payload.foodGeneralModifiers)
          ? action.payload.foodGeneralModifiers.reduce((sum, mod) => sum + (mod.effect || 0), 0)
          : 0;
        const techEffectsTotal = Array.isArray(action.payload.foodTechModifiers)
          ? action.payload.foodTechModifiers.reduce((sum, mod) => sum + (mod.effect || 0), 0)
          : 0;
        const perishableFactor = Array.isArray(action.payload.perishableModifiers)
          ? action.payload.perishableModifiers.reduce((sum, mod) => sum + (mod.effect || 0), 0)
          : 0;
        const totalProductionBonus = generalEffectsTotal + techEffectsTotal;

        console.log('foodSlice: Recalculated totals after fetch:', {
          generalEffectsTotal,
          techEffectsTotal,
          perishableFactor,
          totalProductionBonus
        });

        // Update totals
        state.totals = {
          generalEffectsTotal,
          techEffectsTotal,
          totalProductionBonus,
          perishableFactor
        };
      })
      .addCase(fetchFoodModifiers.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  }
});

// Export actions
export const { updateFoodStats } = foodSlice.actions;

// Sélecteurs de base
const selectFoodState = (state) => state.food;

// Export selectors - utiliser des sélecteurs simples comme dans miningSlice
export const selectFoodGeneralModifiers = (state) => state.food.foodGeneralModifiers;
export const selectFoodTechModifiers = (state) => state.food.foodTechModifiers;
export const selectPerishableModifiers = (state) => state.food.perishableModifiers;
export const selectFoodStats = (state) => state.food.foodStats;

export const selectFoodTotals = createSelector(
  [selectFoodState],
  (foodState) => foodState.totals
);

export const selectFoodLoading = createSelector(
  [selectFoodState],
  (foodState) => foodState.loading
);

export const selectFoodError = createSelector(
  [selectFoodState],
  (foodState) => foodState.error
);

// Export reducer
export default foodSlice.reducer;
