import { createSlice } from '@reduxjs/toolkit';
import { createSelector } from 'reselect';
import { fetchGameState } from './gameSlice';
import { fetchInhabitants } from './populationSlice';
import { fetchFoodModifiers } from './foodSlice';
import { fetchMiningModifiers } from './miningSlice';
import { fetchMaterialsModifiers } from './materialsSlice';
import { fetchChargesModifiers } from './chargesSlice';
import { fetchMoralModifiers } from './moralSlice';
import { fetchHealthModifiers } from './healthSlice';
import { fetchResearchModifiers } from './researchSlice';
import { fetchBuildings } from './buildingsSlice';

const initialState = {
  inhabitants: {
    byId: {},
    allIds: []
  },
  jobs: {
    byId: {},
    allIds: []
  },
  modifiers: {
    byId: {},
    byTable: {}
  },
  buildings: {
    byId: {},
    allIds: []
  }
};

const entitiesSlice = createSlice({
  name: 'entities',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      // Mettre à jour les habitants quand fetchInhabitants est réussi
      .addCase(fetchInhabitants.fulfilled, (state, action) => {
        // Extraire les habitants de la réponse API
        const response = action.payload || {};
        const inhabitants = response.inhabitants || [];

        // Réinitialiser l'état
        state.inhabitants.byId = {};
        state.inhabitants.allIds = [];

        // Vérifier que inhabitants est bien un tableau
        if (Array.isArray(inhabitants)) {
          // Normaliser les habitants
          inhabitants.forEach(inhabitant => {
            if (inhabitant && inhabitant.id) {
              state.inhabitants.byId[inhabitant.id] = inhabitant;
              if (!state.inhabitants.allIds.includes(inhabitant.id)) {
                state.inhabitants.allIds.push(inhabitant.id);
              }
            }
          });
        } else {
          console.error('fetchInhabitants.fulfilled: inhabitants is not an array', response);
        }
      })

      // Mettre à jour les emplois quand fetchGameState est réussi
      .addCase(fetchGameState.fulfilled, (state, action) => {
        const jobs = action.payload?.jobs || [];

        // Réinitialiser l'état
        state.jobs.byId = {};
        state.jobs.allIds = [];

        // Vérifier que jobs est bien un tableau
        if (Array.isArray(jobs)) {
          // Normaliser les emplois
          jobs.forEach(job => {
            if (job && job.id) {
              state.jobs.byId[job.id] = job;
              if (!state.jobs.allIds.includes(job.id)) {
                state.jobs.allIds.push(job.id);
              }
            }
          });
        } else {
          console.error('fetchGameState.fulfilled: jobs is not an array', jobs);
        }
      })

      // Mettre à jour les modificateurs quand fetchFoodModifiers est réussi
      .addCase(fetchFoodModifiers.fulfilled, (state, action) => {
        const { foodGeneralModifiers = [], foodTechModifiers = [], perishableModifiers = [] } = action.payload || {};

        // Mettre à jour les modificateurs par table
        state.modifiers.byTable[1] = []; // Food general
        state.modifiers.byTable[2] = []; // Food tech
        state.modifiers.byTable[3] = []; // Perishable

        // Vérifier que les modificateurs sont bien des tableaux
        const foodGeneralArray = Array.isArray(foodGeneralModifiers) ? foodGeneralModifiers : [];
        const foodTechArray = Array.isArray(foodTechModifiers) ? foodTechModifiers : [];
        const perishableArray = Array.isArray(perishableModifiers) ? perishableModifiers : [];

        // Normaliser les modificateurs
        [...foodGeneralArray, ...foodTechArray, ...perishableArray].forEach(modifier => {
          if (modifier && modifier.id) {
            state.modifiers.byId[modifier.id] = modifier;

            // Ajouter l'ID à la table correspondante
            if (modifier.table_id === 1) {
              state.modifiers.byTable[1].push(modifier.id);
            } else if (modifier.table_id === 2) {
              state.modifiers.byTable[2].push(modifier.id);
            } else if (modifier.table_id === 3) {
              state.modifiers.byTable[3].push(modifier.id);
            }
          }
        });
      })

      // Mettre à jour les modificateurs quand fetchMiningModifiers est réussi
      .addCase(fetchMiningModifiers.fulfilled, (state, action) => {
        const { miningGeneralModifiers = [], engineeringModifiers = [], miningTechModifiers = [] } = action.payload || {};

        // Mettre à jour les modificateurs par table
        state.modifiers.byTable[4] = []; // Mining general
        state.modifiers.byTable[5] = []; // Engineering
        state.modifiers.byTable[6] = []; // Mining tech

        // Vérifier que les modificateurs sont bien des tableaux
        const miningGeneralArray = Array.isArray(miningGeneralModifiers) ? miningGeneralModifiers : [];
        const engineeringArray = Array.isArray(engineeringModifiers) ? engineeringModifiers : [];
        const miningTechArray = Array.isArray(miningTechModifiers) ? miningTechModifiers : [];

        // Normaliser les modificateurs
        [...miningGeneralArray, ...engineeringArray, ...miningTechArray].forEach(modifier => {
          if (modifier && modifier.id) {
            state.modifiers.byId[modifier.id] = modifier;

            // Ajouter l'ID à la table correspondante
            if (modifier.table_id === 4) {
              state.modifiers.byTable[4].push(modifier.id);
            } else if (modifier.table_id === 5) {
              state.modifiers.byTable[5].push(modifier.id);
            } else if (modifier.table_id === 6) {
              state.modifiers.byTable[6].push(modifier.id);
            }
          }
        });
      })

      // Mettre à jour les modificateurs quand fetchMaterialsModifiers est réussi
      .addCase(fetchMaterialsModifiers.fulfilled, (state, action) => {
        const { materialsGeneralModifiers = [], materialsTechModifiers = [] } = action.payload || {};

        // Mettre à jour les modificateurs par table
        state.modifiers.byTable[9] = []; // Materials general
        state.modifiers.byTable[10] = []; // Materials tech

        // Vérifier que les modificateurs sont bien des tableaux
        const materialsGeneralArray = Array.isArray(materialsGeneralModifiers) ? materialsGeneralModifiers : [];
        const materialsTechArray = Array.isArray(materialsTechModifiers) ? materialsTechModifiers : [];

        // Normaliser les modificateurs
        [...materialsGeneralArray, ...materialsTechArray].forEach(modifier => {
          if (modifier && modifier.id) {
            state.modifiers.byId[modifier.id] = modifier;

            // Ajouter l'ID à la table correspondante
            if (modifier.table_id === 9) {
              state.modifiers.byTable[9].push(modifier.id);
            } else if (modifier.table_id === 10) {
              state.modifiers.byTable[10].push(modifier.id);
            }
          }
        });
      })

      // Mettre à jour les modificateurs quand fetchChargesModifiers est réussi
      .addCase(fetchChargesModifiers.fulfilled, (state, action) => {
        const { chargesGeneralModifiers = [], chargesGlobalModifiers = [] } = action.payload || {};

        // Mettre à jour les modificateurs par table
        state.modifiers.byTable[7] = []; // Charges general
        state.modifiers.byTable[8] = []; // Charges global

        // Vérifier que les modificateurs sont bien des tableaux
        const chargesGeneralArray = Array.isArray(chargesGeneralModifiers) ? chargesGeneralModifiers : [];
        const chargesGlobalArray = Array.isArray(chargesGlobalModifiers) ? chargesGlobalModifiers : [];

        // Normaliser les modificateurs
        [...chargesGeneralArray, ...chargesGlobalArray].forEach(modifier => {
          if (modifier && modifier.id) {
            state.modifiers.byId[modifier.id] = modifier;

            // Ajouter l'ID à la table correspondante
            if (modifier.table_id === 7) {
              state.modifiers.byTable[7].push(modifier.id);
            } else if (modifier.table_id === 8) {
              state.modifiers.byTable[8].push(modifier.id);
            }
          }
        });
      })

      // Mettre à jour les bâtiments quand fetchBuildings est réussi
      .addCase(fetchBuildings.fulfilled, (state, action) => {
        const buildings = action.payload || [];

        // Réinitialiser l'état
        state.buildings.byId = {};
        state.buildings.allIds = [];

        // Vérifier que buildings est bien un tableau
        if (Array.isArray(buildings)) {
          // Normaliser les bâtiments
          buildings.forEach(building => {
            if (building && building.id) {
              state.buildings.byId[building.id] = building;
              if (!state.buildings.allIds.includes(building.id)) {
                state.buildings.allIds.push(building.id);
              }
            }
          });
        } else {
          console.error('fetchBuildings.fulfilled: buildings is not an array', buildings);
        }
      });
  }
});

// Sélecteurs de base
export const selectInhabitantById = (id) => (state) => state.entities.inhabitants.byId[id];
export const selectAllInhabitantIds = (state) => state.entities.inhabitants.allIds;
export const selectModifierById = (id) => (state) => state.entities.modifiers.byId[id];
export const selectBuildingById = (id) => (state) => state.entities.buildings.byId[id];
export const selectAllBuildingIds = (state) => state.entities.buildings.allIds;
export const selectJobById = (id) => (state) => state.entities.jobs.byId[id];
export const selectAllJobIds = (state) => state.entities.jobs.allIds;

// Sélecteurs mémorisés pour les tableaux
export const selectAllInhabitants = createSelector(
  [(state) => state.entities.inhabitants.allIds, (state) => state.entities.inhabitants.byId],
  (allIds, byId) => allIds.map(id => byId[id])
);

export const selectAllJobs = createSelector(
  [(state) => state.entities.jobs.allIds, (state) => state.entities.jobs.byId],
  (allIds, byId) => allIds.map(id => byId[id])
);

export const selectModifiersByTable = (tableId) => createSelector(
  [(state) => state.entities.modifiers.byTable[tableId] || [], (state) => state.entities.modifiers.byId],
  (modifierIds, modifiersById) => modifierIds.map(id => modifiersById[id])
);

export const selectAllBuildings = createSelector(
  [(state) => state.entities.buildings.allIds, (state) => state.entities.buildings.byId],
  (allIds, byId) => allIds.map(id => byId[id])
);

export default entitiesSlice.reducer;
