import { createSlice, createSelector } from '@reduxjs/toolkit';
import { WS_CONNECT, WS_CONNECTED, WS_DISCONNECT, WS_DISCONNECTED, WS_MESSAGE } from '../../middleware/websocketMiddleware';

// Define additional action types
const WS_ERROR = 'WS_ERROR';

const initialState = {
  connected: false,
  connecting: false,
  messages: [],
  lastMessage: null,
  error: null,
  connectionAttempts: 0,
  lastConnectionAttempt: null,
  lastSuccessfulConnection: null,
  disconnectReason: null,
  status: 'disconnected' // 'disconnected', 'connecting', 'connected', 'error'
};

const websocketSlice = createSlice({
  name: 'websocket',
  initialState,
  reducers: {
    clearMessages: (state) => {
      state.messages = [];
      state.lastMessage = null;
    },
    clearError: (state) => {
      state.error = null;
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(WS_CONNECT, (state) => {
        state.connecting = true;
        state.connected = false;
        state.connectionAttempts++;
        state.lastConnectionAttempt = Date.now();
        state.status = 'connecting';

        // Ne pas effacer l'erreur précédente pour permettre à l'UI de l'afficher
        // jusqu'à ce que la connexion réussisse
      })
      .addCase(WS_CONNECTED, (state) => {
        state.connected = true;
        state.connecting = false;
        state.error = null;
        state.disconnectReason = null;
        state.lastSuccessfulConnection = Date.now();
        state.status = 'connected';

        // Réinitialiser le compteur de tentatives lors d'une connexion réussie
        state.connectionAttempts = 0;
      })
      .addCase(WS_DISCONNECT, (state) => {
        state.connecting = false;
        state.status = 'disconnected';
        state.disconnectReason = 'user_initiated';
      })
      .addCase(WS_DISCONNECTED, (state, action) => {
        state.connected = false;
        state.connecting = false;
        state.status = 'disconnected';

        // Stocker la raison de la déconnexion si elle est fournie
        if (action.payload && action.payload.error) {
          state.disconnectReason = action.payload.error;
        } else {
          state.disconnectReason = 'connection_lost';
        }
      })
      .addCase(WS_MESSAGE, (state, action) => {
        // Limiter le nombre de messages stockés pour éviter les fuites de mémoire
        if (state.messages.length >= 100) {
          state.messages = state.messages.slice(-99);
        }

        state.messages.push({
          ...action.payload,
          receivedAt: Date.now()
        });

        state.lastMessage = {
          ...action.payload,
          receivedAt: Date.now()
        };

        // Si c'est un message d'erreur, le stocker dans le champ error
        if (action.payload.type === 'ERROR') {
          state.error = action.payload.payload;
          state.status = 'error';
        }
      })
      .addCase(WS_ERROR, (state, action) => {
        state.error = action.payload;
        state.connected = false;
        state.connecting = false;
        state.status = 'error';

        // Stocker la raison de l'erreur si elle est fournie
        if (action.payload && action.payload.code) {
          state.disconnectReason = action.payload.code;
        } else {
          state.disconnectReason = 'error';
        }
      });
  }
});

// Export actions
export const { clearMessages, clearError } = websocketSlice.actions;

// Sélecteur de base pour l'état WebSocket
const selectWebSocketState = (state) => state.websocket;

// Export selectors mémorisés
export const selectWebSocketConnected = createSelector(
  [selectWebSocketState],
  (websocketState) => websocketState.connected
);

export const selectWebSocketConnecting = createSelector(
  [selectWebSocketState],
  (websocketState) => websocketState.connecting
);

export const selectWebSocketMessages = createSelector(
  [selectWebSocketState],
  (websocketState) => websocketState.messages
);

export const selectWebSocketLastMessage = createSelector(
  [selectWebSocketState],
  (websocketState) => websocketState.lastMessage
);

export const selectWebSocketError = createSelector(
  [selectWebSocketState],
  (websocketState) => websocketState.error
);

export const selectWebSocketStatus = createSelector(
  [selectWebSocketState],
  (websocketState) => websocketState.status
);

export const selectWebSocketConnectionAttempts = createSelector(
  [selectWebSocketState],
  (websocketState) => websocketState.connectionAttempts
);

export const selectWebSocketDisconnectReason = createSelector(
  [selectWebSocketState],
  (websocketState) => websocketState.disconnectReason
);

export const selectWebSocketLastConnectionAttempt = createSelector(
  [selectWebSocketState],
  (websocketState) => websocketState.lastConnectionAttempt
);

export const selectWebSocketLastSuccessfulConnection = createSelector(
  [selectWebSocketState],
  (websocketState) => websocketState.lastSuccessfulConnection
);

// Utiliser les sélecteurs mémorisés déjà définis pour le sélecteur dérivé

// Sélecteur dérivé mémorisé pour obtenir des informations détaillées sur l'état de la connexion
export const selectWebSocketConnectionInfo = createSelector(
  [
    state => state.websocket.connected,
    state => state.websocket.connecting,
    state => state.websocket.status,
    state => state.websocket.error,
    state => state.websocket.disconnectReason,
    state => state.websocket.connectionAttempts,
    state => state.websocket.lastConnectionAttempt,
    state => state.websocket.lastSuccessfulConnection,
    state => state.websocket.lastMessage
  ],
  (
    connected,
    connecting,
    status,
    error,
    disconnectReason,
    connectionAttempts,
    lastConnectionAttempt,
    lastSuccessfulConnection,
    lastMessage
  ) => ({
    connected,
    connecting,
    status,
    error,
    disconnectReason,
    connectionAttempts,
    lastConnectionAttempt: lastConnectionAttempt ? new Date(lastConnectionAttempt).toISOString() : null,
    lastSuccessfulConnection: lastSuccessfulConnection ? new Date(lastSuccessfulConnection).toISOString() : null,
    lastMessage: lastMessage ? {
      ...lastMessage,
      receivedAt: lastMessage.receivedAt ? new Date(lastMessage.receivedAt).toISOString() : null
    } : null
  })
);

export default websocketSlice.reducer;
