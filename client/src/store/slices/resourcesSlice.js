import { createSlice, createSelector } from '@reduxjs/toolkit';
import { fetchGameState, processNextCycle } from './gameSlice';

// Initial state
const initialState = {
  food: {
    production: 0,
    consumption: 0,
    net: 0,
    modifiers: { perishableRate: 0 }
  },
  mining: {
    production: 0,
    miners: 0,
    modifiers: {}
  },
  materials: {
    production: 0,
    workers: 0,
    miners: 0,
    modifiers: {}
  },
  treasury: {
    revenue: 0,
    expenses: 0,
    net: 0
  },
  charges: {
    salaries: 0,
    nonSalaryCharges: 0,
    totalCharges: 0,
    modifiers: {}
  },
  renown: {
    totalRenown: 0
  }
};

// Create the resources slice
const resourcesSlice = createSlice({
  name: 'resources',
  initialState,
  reducers: {
    setFoodData: (state, action) => {
      state.food = action.payload;
    },
    setMiningData: (state, action) => {
      state.mining = action.payload;
    },
    setMaterialsData: (state, action) => {
      state.materials = action.payload;
    },
    setTreasuryData: (state, action) => {
      state.treasury = action.payload;
    },
    setChargesData: (state, action) => {
      state.charges = action.payload;
    },
    setRenownData: (state, action) => {
      state.renown = action.payload;
    }
  },
  extraReducers: (builder) => {
    builder
      // Update resources when game state is fetched
      .addCase(fetchGameState.fulfilled, (state, action) => {
        // Update food data if available
        if (action.payload.foodData) {
          state.food = action.payload.foodData;
        }
      })
      // Update resources when next cycle is processed
      .addCase(processNextCycle.fulfilled, (state, action) => {
        if (action.payload.calculations) {
          const { calculations } = action.payload;

          // Update food data
          if (calculations.food) {
            state.food = {
              ...state.food,
              production: calculations.food.production || 0,
              consumption: calculations.food.consumption || 0,
              net: (calculations.food.production || 0) - (calculations.food.consumption || 0),
              modifiers: {
                ...state.food.modifiers,
                perishableRate: calculations.food.perishable || 0
              }
            };
          }

          // Update mining data
          if (calculations.mining) {
            state.mining = {
              ...state.mining,
              production: calculations.mining.miningProduction || 0,
              totalProduction: calculations.mining.totalProduction || 0,
              mineDepth: calculations.mining.mineDepth || 0
            };
          }

          // Update materials data
          if (calculations.materials) {
            state.materials = {
              ...state.materials,
              production: calculations.materials.materialsProduction || 0,
              workers: action.payload.jobs?.find(j => j.name === 'Worker')?.number || 0,
              miners: action.payload.jobs?.find(j => j.name === 'Miner')?.number || 0
            };
          }

          // Update trading data
          if (calculations.trading) {
            state.treasury = {
              ...state.treasury,
              revenue: calculations.trading.revenue || 0
            };
          }
        }
      });
  }
});

// Export actions
export const {
  setFoodData,
  setMiningData,
  setMaterialsData,
  setTreasuryData,
  setChargesData,
  setRenownData
} = resourcesSlice.actions;

// Sélecteurs de base
const selectFoodDataRaw = (state) => state.resources.food;
const selectMiningDataRaw = (state) => state.resources.mining;
const selectMaterialsDataRaw = (state) => state.resources.materials;
const selectTreasuryDataRaw = (state) => state.resources.treasury;
const selectChargesDataRaw = (state) => state.resources.charges;
const selectRenownDataRaw = (state) => state.resources.renown;

// Export selectors mémorisés
export const selectFoodData = createSelector(
  [selectFoodDataRaw],
  (foodData) => foodData
);

export const selectMiningData = createSelector(
  [selectMiningDataRaw],
  (miningData) => miningData
);

export const selectMaterialsData = createSelector(
  [selectMaterialsDataRaw],
  (materialsData) => materialsData
);

export const selectTreasuryData = createSelector(
  [selectTreasuryDataRaw],
  (treasuryData) => treasuryData
);

export const selectChargesData = createSelector(
  [selectChargesDataRaw],
  (chargesData) => chargesData
);

export const selectRenownData = createSelector(
  [selectRenownDataRaw],
  (renownData) => renownData
);

// Export reducer
export default resourcesSlice.reducer;
