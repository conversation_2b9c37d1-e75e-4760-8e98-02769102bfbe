import { configureStore } from '@reduxjs/toolkit';
import chargesReducer, {
  updateChargesStats,
  calculateChargesStats
} from '../chargesSlice';

// Mock API
jest.mock('../../../services/api', () => ({
  modifiersApi: {
    getModifiersByTable: jest.fn()
  }
}));

describe('chargesSlice', () => {
  let store;

  beforeEach(() => {
    store = configureStore({
      reducer: {
        charges: chargesReducer
      }
    });
  });

  describe('reducer', () => {
    it('should handle initial state', () => {
      expect(store.getState().charges).toEqual({
        chargesGeneralModifiers: [],
        chargesGlobalModifiers: [],
        chargesStats: {
          salaries: 0,
          nonSalaryCharges: 0,
          totalCharges: 0,
          craftsmanEffect: 0,
          baseCharges: 0
        },
        totals: {
          chargesPerCycle: 0,
          chargesGlobalModifier: 0
        },
        loading: false,
        error: null
      });
    });

    it('should handle updateChargesStats', () => {
      // Setup initial state with modifiers
      store = configureStore({
        reducer: {
          charges: chargesReducer
        },
        preloadedState: {
          charges: {
            chargesGeneralModifiers: [
              { id: 1, name: 'Test Charge 1', effect: 50 },
              { id: 2, name: 'Test Charge 2', effect: 30 }
            ],
            chargesGlobalModifiers: [
              { id: 3, name: 'Test Global Modifier', effect: -0.1 }
            ],
            chargesStats: {
              salaries: 0,
              nonSalaryCharges: 0,
              totalCharges: 0,
              craftsmanEffect: 0,
              baseCharges: 0
            },
            totals: {
              chargesPerCycle: 0,
              chargesGlobalModifier: 0
            },
            loading: false,
            error: null
          }
        }
      });

      // Mock game state
      const gameState = {
        jobs: [
          { name: 'Worker', number: 10, sick: 2, free: 1, salary: 2 },
          { name: 'Miner', number: 5, sick: 1, free: 0, salary: 3 },
          { name: 'Craftsman', number: 3, sick: 0, free: 0, salary: 4 }
        ]
      };

      // Dispatch action
      store.dispatch(updateChargesStats({ gameState }));

      // Get updated state
      const state = store.getState().charges;

      // Check if totals were calculated correctly
      expect(state.totals.chargesPerCycle).toBe(80); // 50 + 30
      expect(state.totals.chargesGlobalModifier).toBe(-0.1);

      // Check if stats were updated correctly
      // Salaries: (10-1)*2 + 5*3 + 3*4 = 18 + 15 + 12 = 45
      expect(state.chargesStats.salaries).toBe(45);
      
      // Craftsman effect: 3 * 0.03 = 0.09
      expect(state.chargesStats.craftsmanEffect).toBeCloseTo(0.09);
      
      // Non-salary charges: 80 / (1 + 0.1) / (1 + 0.09) ≈ 80 / 1.1 / 1.09 ≈ 66.7
      expect(state.chargesStats.nonSalaryCharges).toBeCloseTo(66.7, 0);
      
      // Total charges: 45 + 66.7 ≈ 111.7
      expect(state.chargesStats.totalCharges).toBeCloseTo(111.7, 0);
    });
  });

  describe('calculation functions', () => {
    it('should calculate charges stats correctly', () => {
      const jobs = [
        { name: 'Worker', number: 10, sick: 0, free: 2, salary: 2 },
        { name: 'Miner', number: 5, sick: 0, free: 0, salary: 3 },
        { name: 'Craftsman', number: 3, sick: 0, free: 0, salary: 4 }
      ];
      const chargesPerCycle = 100;
      const chargesGlobalModifier = -0.2;

      const result = calculateChargesStats(
        jobs,
        chargesPerCycle,
        chargesGlobalModifier
      );

      // Salaries: (10-2)*2 + 5*3 + 3*4 = 16 + 15 + 12 = 43
      expect(result.salaries).toBe(43);
      
      // Craftsman effect: 3 * 0.03 = 0.09
      expect(result.craftsmanEffect).toBeCloseTo(0.09);
      
      // Non-salary charges: 100 / (1 + 0.2) / (1 + 0.09) ≈ 100 / 1.2 / 1.09 ≈ 76.3
      expect(result.nonSalaryCharges).toBeCloseTo(76.3, 0);
      
      // Total charges: 43 + 76.3 ≈ 119.3
      expect(result.totalCharges).toBeCloseTo(119.3, 0);
    });
  });
});
