import { configureStore } from '@reduxjs/toolkit';
import materialsReducer, {
  updateMaterialsStats,
  calculateMaterialsProduction,
  calculateTotalBonus
} from '../materialsSlice';

// Mock API
jest.mock('../../../services/api', () => ({
  modifiersApi: {
    getModifiersByTable: jest.fn()
  }
}));

describe('materialsSlice', () => {
  let store;

  beforeEach(() => {
    store = configureStore({
      reducer: {
        materials: materialsReducer
      }
    });
  });

  describe('reducer', () => {
    it('should handle initial state', () => {
      expect(store.getState().materials).toEqual({
        materialsGeneralModifiers: [],
        materialsTechModifiers: [],
        materialsStats: {
          workers: 0,
          miners: 0,
          production: 0,
          baseProduction: 0,
          totalBonus: 0
        },
        totals: {
          generalEffectsTotal: 0,
          techEffectsTotal: 0,
          totalProductionBonus: 0
        },
        loading: false,
        error: null
      });
    });

    it('should handle updateMaterialsStats', () => {
      // Setup initial state with modifiers
      store = configureStore({
        reducer: {
          materials: materialsReducer
        },
        preloadedState: {
          materials: {
            materialsGeneralModifiers: [
              { id: 1, name: 'Test Modifier 1', effect: 0.1 },
              { id: 2, name: 'Test Modifier 2', effect: 0.2 }
            ],
            materialsTechModifiers: [
              { id: 3, name: 'Test Tech 1', effect: 0.05 }
            ],
            materialsStats: {
              workers: 0,
              miners: 0,
              production: 0,
              baseProduction: 0,
              totalBonus: 0
            },
            totals: {
              generalEffectsTotal: 0,
              techEffectsTotal: 0,
              totalProductionBonus: 0
            },
            loading: false,
            error: null
          }
        }
      });

      // Mock game state
      const gameState = {
        gameState: {
          moral_value: 1000,
          moral_title: 'Neutre'
        },
        jobs: [
          { name: 'Worker', number: 10, sick: 2 },
          { name: 'Miner', number: 5, sick: 1 }
        ]
      };

      // Dispatch action
      store.dispatch(updateMaterialsStats({ gameState }));

      // Get updated state
      const state = store.getState().materials;

      // Check if totals were calculated correctly
      expect(state.totals.generalEffectsTotal).toBeCloseTo(0.3);
      expect(state.totals.techEffectsTotal).toBeCloseTo(0.05);
      expect(state.totals.totalProductionBonus).toBeCloseTo(0.35);

      // Check if stats were updated correctly
      expect(state.materialsStats.workers).toBe(8); // 10 - 2 sick
      expect(state.materialsStats.miners).toBe(4);  // 5 - 1 sick
      expect(state.materialsStats.baseProduction).toBeCloseTo(8 * 4 + 4 * 2); // workers * 4 + miners * 2
      expect(state.materialsStats.moralModifier).toBeCloseTo(0);
      expect(state.materialsStats.moralTitle).toBe('Neutre');
    });
  });

  describe('calculation functions', () => {
    it('should calculate materials production correctly', () => {
      const activeWorkers = 10;
      const activeMiners = 5;
      const generalEffectsTotal = 0.2;
      const techEffectsTotal = 0.1;
      const moralModifier = 0.05;

      const expectedBaseProduction = 10 * 4 + 5 * 2; // workers * 4 + miners * 2
      const expectedTotalProduction = expectedBaseProduction * (1 + generalEffectsTotal + techEffectsTotal + moralModifier);

      const result = calculateMaterialsProduction(
        activeWorkers,
        activeMiners,
        generalEffectsTotal,
        techEffectsTotal,
        moralModifier
      );

      expect(result).toBeCloseTo(expectedTotalProduction);
    });

    it('should calculate total bonus correctly', () => {
      const generalEffectsTotal = 0.2;
      const techEffectsTotal = 0.1;
      const moralModifier = 0.05;

      const expectedTotalBonus = 0.35; // 0.2 + 0.1 + 0.05

      const result = calculateTotalBonus(
        generalEffectsTotal,
        techEffectsTotal,
        moralModifier
      );

      expect(result).toBeCloseTo(expectedTotalBonus);
    });
  });
});
