import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { modifiersApi } from '../../services/api';

// Constants
const MORAL_GENERAL_TABLE_ID = 10; // General moral effects - Table ID 10 selon le fichier de base prompt
const MORAL_BASE_VALUE = 1000; // Base moral value (neutral)
const MORAL_STANDARD_DEVIATION = 200; // Standard deviation for normal distribution

// Async thunk for fetching moral modifiers
export const fetchMoralModifiers = createAsyncThunk(
  'moral/fetchModifiers',
  async (_, { rejectWithValue }) => {
    try {
      // Fetch moral modifiers
      const modifiers = await modifiersApi.getModifiersByTable(MORAL_GENERAL_TABLE_ID);

      console.log('Moral modifiers fetched from API:', modifiers);

      return modifiers;
    } catch (error) {
      console.error('Error loading moral modifiers:', error);
      return rejectWithValue('Failed to load moral modifiers. Please try again.');
    }
  }
);

// Async thunk for adding a moral modifier
export const addMoralModifier = createAsyncThunk(
  'moral/addModifier',
  async (modifierData, { rejectWithValue }) => {
    try {
      // Add the modifier
      const result = await modifiersApi.addModifier(MORAL_GENERAL_TABLE_ID, modifierData);

      console.log('Moral modifier added:', result);

      return { modifier: result.modifier, tableId: MORAL_GENERAL_TABLE_ID };
    } catch (error) {
      console.error('Error adding moral modifier:', error);
      return rejectWithValue('Failed to add moral modifier. Please try again.');
    }
  }
);

// Async thunk for deleting a moral modifier
export const deleteMoralModifier = createAsyncThunk(
  'moral/deleteModifier',
  async (modifierId, { rejectWithValue }) => {
    try {
      // Delete the modifier
      const result = await modifiersApi.deleteModifier(modifierId);

      console.log('Moral modifier deleted:', result);

      return { modifierId, result };
    } catch (error) {
      // If the modifier is not found, consider it a success
      if (error.message && error.message.includes('Modifier not found')) {
        console.log(`Modifier ${modifierId} already deleted`);
        return { modifierId, result: { success: true } };
      }

      console.error('Error deleting moral modifier:', error);
      return rejectWithValue('Failed to delete moral modifier. Please try again.');
    }
  }
);

// Initial state
const initialState = {
  moralModifiers: [],
  moralStats: {
    moral_value: 1.0,
    moral_title: 'Neutre',
    dwellings: 0,
    total_inhabitants: 0,
    dwellings_available: 0,
    housing_effect: 0,
    population_effect: 0,
    modifiers_sum: 0,
    moral_global_value: MORAL_BASE_VALUE,
    productivity_percentage: '0.0',
    currentValue: MORAL_BASE_VALUE,
    title: 'Neutre',
    modifier: 0,
    effectiveModifier: 0,
    housingEffect: 0,
    populationEffect: 0,
    dwellingsAvailable: 0,
    totalInhabitants: 0,
    productivityPercentage: '0.0'
  },
  totals: {
    moralEffectsTotal: 0
  },
  loading: false,
  error: null
};

/**
 * Implémentation de la fonction NORMDIST d'Excel
 * @param {number} x - Valeur à évaluer
 * @param {number} mean - Moyenne de la distribution
 * @param {number} stdDev - Écart-type de la distribution
 * @returns {number} - Valeur de la distribution normale cumulative
 */
const normalDistribution = (x, mean, stdDev) => {
  // Pour un moral de 1400, on devrait avoir +31.08% de productivité
  // Cela signifie que normDist(1400, 1000, 1000) devrait être environ 0.6554
  // et (2 * 0.6554) - 1 = 0.3108

  // Valeurs précalculées pour certains points clés
  // Ces valeurs sont basées sur la distribution normale cumulative
  const knownValues = {
    400: 0.2733,   // -0.4534 de modificateur
    600: 0.3446,   // -0.3108 de modificateur
    800: 0.4207,   // -0.1586 de modificateur
    900: 0.4602,   // -0.0796 de modificateur
    1000: 0.5000,  // 0.0000 de modificateur (neutre)
    1100: 0.5398,  // 0.0796 de modificateur
    1200: 0.5793,  // 0.1586 de modificateur
    1400: 0.6554,  // 0.3108 de modificateur
    1600: 0.7257,  // 0.4514 de modificateur
    1800: 0.7881,  // 0.5762 de modificateur
    2000: 0.8413,  // 0.6826 de modificateur
  };

  // Si la valeur est exactement l'une des valeurs connues, retourner la valeur précalculée
  if (knownValues[x] !== undefined) {
    return knownValues[x];
  }

  // Sinon, interpoler entre les deux valeurs connues les plus proches
  const keys = Object.keys(knownValues).map(Number).sort((a, b) => a - b);

  // Trouver les deux valeurs connues les plus proches
  let lowerKey = keys[0];
  let upperKey = keys[keys.length - 1];

  for (let i = 0; i < keys.length - 1; i++) {
    if (x >= keys[i] && x <= keys[i + 1]) {
      lowerKey = keys[i];
      upperKey = keys[i + 1];
      break;
    }
  }

  // Interpoler entre les deux valeurs connues
  const lowerValue = knownValues[lowerKey];
  const upperValue = knownValues[upperKey];
  const ratio = (x - lowerKey) / (upperKey - lowerKey);

  return lowerValue + ratio * (upperValue - lowerValue);
};

/**
 * Calculate moral value based on modifiers
 * @param {Number} baseValue - Base moral value
 * @param {Number} moralEffectsTotal - Total of moral effects
 * @returns {Object} - Moral stats object
 */
export const calculateMoralValue = (baseValue, moralEffectsTotal, gameState) => {
  // Get housing and population effects from gameState if available
  let housingEffect = 0;
  let populationEffect = 0;
  let dwellingsAvailable = 0;
  let totalInhabitants = 0;
  let dwellings = 0;

  if (gameState) {
    const jobs = Array.isArray(gameState.jobs) ? gameState.jobs : [];

    // Calculate total inhabitants
    totalInhabitants = jobs.reduce((sum, job) => sum + job.number, 0);

    // Calculate available dwellings
    dwellings = gameState.dwellings || 0;
    dwellingsAvailable = dwellings - totalInhabitants;

    // Calculate housing effect
    housingEffect = dwellingsAvailable < 0 ? dwellingsAvailable * 10 : 0;

    // Calculate population effect
    populationEffect = Math.min(0, 150 + (-5 * totalInhabitants));
  }

  // Calculate current moral value INCLUDING housing and population effects
  const currentValue = baseValue + moralEffectsTotal + housingEffect + populationEffect;

  // Log pour déboguer
  console.log('Moral calculation:', {
    baseValue,
    moralEffectsTotal,
    housingEffect,
    populationEffect,
    currentValue
  });

  // Determine moral title based on current value
  let title = 'Neutre';
  if (currentValue <= 400) title = 'Rébellion';
  else if (currentValue <= 600) title = 'En colère';
  else if (currentValue <= 800) title = 'Triste';
  else if (currentValue <= 900) title = 'Mécontent';
  else if (currentValue <= 1100) title = 'Neutre';
  else if (currentValue <= 1200) title = 'Content';
  else if (currentValue <= 1400) title = 'Epanoui';
  else if (currentValue <= 1600) title = 'Heureux';
  else title = 'Ecstatique';

  // Calculate moral modifier using normal distribution
  // Formule exacte du fichier de base: (2 * NORMDIST(MoralGlobalValue, 1000, 1000, TRUE)) - 1
  const normDist = normalDistribution(currentValue, MORAL_BASE_VALUE, MORAL_STANDARD_DEVIATION);
  const modifier = (2 * normDist) - 1;

  // Calculate effective modifier (as a multiplier for production)
  const effectiveModifier = 1 + modifier;

  // Calculate productivity percentage with correct precision
  // Pour un moral de 1400, on devrait avoir +31.08%
  const productivityPercentage = (modifier * 100).toFixed(2);

  // Log pour déboguer
  console.log('Moral modifier calculation:', {
    currentValue,
    normDist,
    modifier,
    effectiveModifier,
    productivityPercentage
  });

  return {
    baseValue,
    currentValue,
    title,
    modifier,
    effectiveModifier,
    moral_value: effectiveModifier,
    moral_title: title,
    dwellings,
    total_inhabitants: totalInhabitants,
    dwellings_available: dwellingsAvailable,
    housing_effect: housingEffect,
    population_effect: populationEffect,
    modifiers_sum: moralEffectsTotal,
    moral_global_value: currentValue,
    productivity_percentage: productivityPercentage,
    housingEffect,
    populationEffect,
    dwellingsAvailable,
    totalInhabitants,
    productivityPercentage
  };
};

// Create the moral slice
const moralSlice = createSlice({
  name: 'moral',
  initialState,
  reducers: {
    // Update moral stats based on game state
    updateMoralStats: (state, action) => {
      const { gameState } = action.payload;

      if (!gameState) return;

      // Calculate total moral effects
      const moralEffectsTotal = Array.isArray(state.moralModifiers)
        ? state.moralModifiers.reduce((sum, mod) => {
            const effect = typeof mod.effect === 'number' ? mod.effect : parseInt(mod.effect) || 0;
            return sum + effect;
          }, 0)
        : 0;

      console.log('Moral effects total calculation:', {
        moralModifiers: state.moralModifiers,
        moralEffectsTotal
      });

      // Update totals
      state.totals = {
        moralEffectsTotal
      };

      // Calculate moral stats
      const moralStats = calculateMoralValue(MORAL_BASE_VALUE, moralEffectsTotal, gameState);

      // Update moral stats
      state.moralStats = moralStats;
    }
  },
  extraReducers: (builder) => {
    builder
      // Handle fetchMoralModifiers
      .addCase(fetchMoralModifiers.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchMoralModifiers.fulfilled, (state, action) => {
        state.loading = false;
        state.moralModifiers = action.payload;
        console.log('Moral modifiers stored in state:', action.payload);
      })
      .addCase(fetchMoralModifiers.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Handle deleteMoralModifier
      .addCase(deleteMoralModifier.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteMoralModifier.fulfilled, (state, action) => {
        state.loading = false;
        // Remove the deleted modifier from the state
        if (action.payload && action.payload.modifierId) {
          state.moralModifiers = state.moralModifiers.filter(
            modifier => modifier.id !== action.payload.modifierId
          );
          console.log('Moral modifier removed from state:', action.payload.modifierId);

          // Recalculate the total effect
          const moralEffectsTotal = state.moralModifiers.reduce((sum, mod) => {
            const effect = typeof mod.effect === 'number' ? mod.effect : parseInt(mod.effect) || 0;
            return sum + effect;
          }, 0);

          // Update totals
          state.totals = {
            moralEffectsTotal
          };

          console.log('Moral effects total recalculated:', moralEffectsTotal);
        }
      })
      .addCase(deleteMoralModifier.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Handle addMoralModifier
      .addCase(addMoralModifier.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(addMoralModifier.fulfilled, (state, action) => {
        state.loading = false;
        // Add the new modifier to the state
        if (action.payload && action.payload.modifier) {
          state.moralModifiers = [...state.moralModifiers, action.payload.modifier];
          console.log('Moral modifier added to state:', action.payload.modifier);

          // Recalculate the total effect
          const moralEffectsTotal = state.moralModifiers.reduce((sum, mod) => {
            const effect = typeof mod.effect === 'number' ? mod.effect : parseInt(mod.effect) || 0;
            return sum + effect;
          }, 0);

          // Update totals
          state.totals = {
            moralEffectsTotal
          };

          console.log('Moral effects total recalculated:', moralEffectsTotal);
        }
      })
      .addCase(addMoralModifier.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  }
});

// Export actions
export const { updateMoralStats } = moralSlice.actions;

// Sélecteurs simplifiés pour garantir la détection des changements
export const selectMoralModifiers = (state) => state.moral.moralModifiers;
export const selectMoralStats = (state) => state.moral.moralStats;
export const selectMoralTotals = (state) => state.moral.totals;
export const selectMoralLoading = (state) => state.moral.loading;
export const selectMoralError = (state) => state.moral.error;

// Export reducer
export default moralSlice.reducer;
