import { configureStore } from '@reduxjs/toolkit';
import miningReducer, {
  fetchMiningModifiers,
  updateMiningStats,
  selectMiningGeneralModifiers,
  selectEngineeringModifiers,
  selectMiningTechModifiers,
  selectMiningStats,
  selectMiningTotals,
  selectMiningLoading
} from './miningSlice';

// Mock API calls
jest.mock('../../services/api', () => ({
  modifiersApi: {
    getModifiersByTable: jest.fn()
  }
}));

// Import mocked API
import { modifiersApi } from '../../services/api';

describe('Mining Slice', () => {
  let store;

  beforeEach(() => {
    store = configureStore({
      reducer: {
        mining: miningReducer
      }
    });
  });

  test('should handle initial state', () => {
    const state = store.getState().mining;

    expect(state.miningGeneralModifiers).toEqual([]);
    expect(state.engineeringModifiers).toEqual([]);
    expect(state.miningTechModifiers).toEqual([]);
    expect(state.loading).toBeFalsy();
    expect(state.error).toBeNull();
  });

  test('should handle fetchMiningModifiers.pending', () => {
    store.dispatch({ type: fetchMiningModifiers.pending.type });
    const state = store.getState().mining;

    expect(state.loading).toBeTruthy();
    expect(state.error).toBeNull();
  });

  test('should handle fetchMiningModifiers.fulfilled', () => {
    const mockModifiers = {
      miningGeneralModifiers: [{ id: 1, name: 'Test', effect: 0.1 }],
      engineeringModifiers: [{ id: 2, name: 'Test', effect: 0.2 }],
      miningTechModifiers: [{ id: 3, name: 'Test', effect: 0.3 }]
    };

    store.dispatch({
      type: fetchMiningModifiers.fulfilled.type,
      payload: mockModifiers
    });

    const state = store.getState().mining;

    expect(state.loading).toBeFalsy();
    expect(state.miningGeneralModifiers).toEqual(mockModifiers.miningGeneralModifiers);
    expect(state.engineeringModifiers).toEqual(mockModifiers.engineeringModifiers);
    expect(state.miningTechModifiers).toEqual(mockModifiers.miningTechModifiers);
  });

  test('should handle fetchMiningModifiers.rejected', () => {
    const errorMessage = 'Error fetching modifiers';

    store.dispatch({
      type: fetchMiningModifiers.rejected.type,
      payload: errorMessage
    });

    const state = store.getState().mining;

    expect(state.loading).toBeFalsy();
    expect(state.error).toEqual(errorMessage);
  });

  test('should handle updateMiningStats', () => {
    const mockGameState = {
      gameState: {
        moral_value: 1.1,
        moral_title: 'Content'
      },
      jobs: [
        { id: 1, name: 'Miner', number: 10, sick: 2 },
        { id: 2, name: 'Engineer', number: 5, sick: 1 }
      ]
    };

    // First set some modifiers
    store.dispatch({
      type: fetchMiningModifiers.fulfilled.type,
      payload: {
        miningGeneralModifiers: [{ id: 1, name: 'Test', effect: 0.1 }],
        engineeringModifiers: [{ id: 2, name: 'Test', effect: 0.2 }],
        miningTechModifiers: [{ id: 3, name: 'Test', effect: 0.3 }]
      }
    });

    // Then update mining stats
    store.dispatch(updateMiningStats({ gameState: mockGameState }));

    const state = store.getState().mining;

    expect(state.miningStats.miners).toEqual(8); // 10 - 2
    // Utiliser toBeCloseTo au lieu de toEqual pour les nombres à virgule flottante
    expect(state.miningStats.moralModifier).toBeCloseTo(0.1, 10); // 1.1 - 1.0
    expect(state.miningStats.moralTitle).toEqual('Content');
    expect(state.totals.engineeringMultiplier).toBeGreaterThan(0);
  });

  test('selectors should return the correct values', () => {
    // Set up state
    store.dispatch({
      type: fetchMiningModifiers.fulfilled.type,
      payload: {
        miningGeneralModifiers: [{ id: 1, name: 'Test', effect: 0.1 }],
        engineeringModifiers: [{ id: 2, name: 'Test', effect: 0.2 }],
        miningTechModifiers: [{ id: 3, name: 'Test', effect: 0.3 }]
      }
    });

    const mockGameState = {
      gameState: {
        moral_value: 1.1,
        moral_title: 'Content'
      },
      jobs: [
        { id: 1, name: 'Miner', number: 10, sick: 2 },
        { id: 2, name: 'Engineer', number: 5, sick: 1 }
      ]
    };

    store.dispatch(updateMiningStats({ gameState: mockGameState }));

    const state = store.getState();

    expect(selectMiningGeneralModifiers(state)).toEqual([{ id: 1, name: 'Test', effect: 0.1 }]);
    expect(selectEngineeringModifiers(state)).toEqual([{ id: 2, name: 'Test', effect: 0.2 }]);
    expect(selectMiningTechModifiers(state)).toEqual([{ id: 3, name: 'Test', effect: 0.3 }]);
    expect(selectMiningStats(state).miners).toEqual(8);
    expect(selectMiningTotals(state).generalEffectsTotal).toEqual(0.1);
    expect(selectMiningLoading(state)).toBeFalsy();
  });
});
