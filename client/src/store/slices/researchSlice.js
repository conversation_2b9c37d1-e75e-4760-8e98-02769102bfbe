import { createSlice, createAsyncThunk, createSelector } from '@reduxjs/toolkit';
import { modifiersApi } from '../../services/api';

// Constants
const RESEARCH_MODIFIERS_TABLE_ID = 16; // Research modifiers table ID
const BASE_RESEARCH_PROBABILITY = 0.01; // 1% base chance of research discovery

// Async thunk for fetching research modifiers
export const fetchResearchModifiers = createAsyncThunk(
  'research/fetchModifiers',
  async (_, { rejectWithValue }) => {
    try {
      // Utiliser l'API spécifique pour la recherche qui renvoie à la fois les modificateurs et les statistiques
      const researchData = await modifiersApi.getResearchData();
      console.log('Research data fetched:', researchData);

      // Adapter le format de la réponse selon ce qui est reçu
      let modifiers = [];
      let stats = {};

      if (researchData && typeof researchData === 'object') {
        // Si la réponse est directement l'objet avec modifiers et stats
        if (Array.isArray(researchData.modifiers)) {
          modifiers = researchData.modifiers;
          stats = researchData.stats || {};
        }
        // Si la réponse est encapsulée dans un objet researchData
        else if (researchData.researchData && typeof researchData.researchData === 'object') {
          modifiers = researchData.researchData.modifiers || [];
          stats = researchData.researchData.stats || {};
        }
      }

      // Vérifier que les modificateurs sont bien un tableau
      if (!Array.isArray(modifiers)) {
        console.error('Research modifiers is not an array:', modifiers);
        return {
          researchModifiers: [],
          researchStats: stats || {}
        };
      }

      // Vérifier que chaque modificateur a une propriété effect
      const validModifiers = modifiers.map(mod => {
        if (mod.effect === undefined) {
          console.error('Research modifier has no effect property:', mod);
          return { ...mod, effect: 0 };
        }
        return mod;
      });

      console.log('Valid research modifiers:', validModifiers);
      console.log('Research stats from API:', stats);

      return {
        researchModifiers: validModifiers,
        researchStats: stats || {}
      };
    } catch (error) {
      console.error('Error loading research data:', error);
      return rejectWithValue('Failed to load research data. Please try again.');
    }
  }
);

// Initial state
const initialState = {
  researchModifiers: [],
  researchStats: {
    techLevel: 0,
    researchProbability: BASE_RESEARCH_PROBABILITY,
    totalResearchModifier: 0,
    activeScholars: 0,
    scholars: 0,
    scholarsSick: 0
  },
  loading: false,
  error: null
};

/**
 * Calculate research stats based on game state
 * @param {Object} gameState - Game state object
 * @param {Array} researchModifiers - Research modifiers array
 * @returns {Object} - Research stats object
 */
export const calculateResearchStats = (gameState, researchModifiers = []) => {
  if (!gameState || !gameState.jobs) {
    return {
      techLevel: 0,
      researchProbability: BASE_RESEARCH_PROBABILITY,
      totalResearchModifier: 0,
      activeScholars: 0,
      scholars: 0,
      scholarsSick: 0
    };
  }

  // Get tech level
  const techLevel = gameState.gameState?.tech_level || 0;

  // Get scholars count
  const scholars = gameState.jobs.find(job => job.name === 'Scholar') || { number: 0, sick: 0 };
  const scholarsSick = scholars.sick || 0;
  const activeScholars = scholars.number - scholarsSick;

  // Calculate total research modifier
  let totalResearchModifier = 0;

  if (Array.isArray(researchModifiers) && researchModifiers.length > 0) {
    // Parcourir chaque modificateur et ajouter son effet au total
    researchModifiers.forEach(mod => {
      // Convertir l'effet en nombre si nécessaire
      const effect = typeof mod.effect === 'number' ? mod.effect : parseFloat(mod.effect) || 0;
      totalResearchModifier += effect;
    });
  }

  // Formula: MAX(0.5/(1+EXP(-0.33*(ScholarNumber-ScholarSick)*(1+0.05*TechLevel*TotalResearchModifier)))-(0.6/(1+EXP(0.2*TechLevel)));0)+0.01

  // Calcul avec la formule originale
  const scholarFactor = activeScholars * (1 + 0.05 * techLevel * totalResearchModifier);
  const term1 = 0.5 / (1 + Math.exp(-0.33 * scholarFactor));
  const term2 = 0.6 / (1 + Math.exp(0.2 * techLevel));
  const researchProbability = Math.max(term1 - term2, 0) + 0.01;

  return {
    techLevel,
    researchProbability,
    totalResearchModifier,
    activeScholars,
    scholars: scholars.number,
    scholarsSick
  };
};

// Create the research slice
const researchSlice = createSlice({
  name: 'research',
  initialState,
  reducers: {
    // Update research stats based on game state
    updateResearchStats: (state, action) => {
      const { gameState, researchModifiers } = action.payload;

      if (!gameState) return;

      // Utiliser les modificateurs passés en paramètre s'ils sont disponibles
      // sinon utiliser ceux du state
      const modifiersToUse = researchModifiers || state.researchModifiers;

      console.log('updateResearchStats called with:', {
        gameState: !!gameState,
        researchModifiers: !!researchModifiers,
        stateModifiers: !!state.researchModifiers,
        modifiersToUse
      });

      // Calculate research stats using the modifiers
      const researchStats = calculateResearchStats(gameState, modifiersToUse);

      console.log('Updating research stats:', researchStats);

      // Update research stats
      state.researchStats = researchStats;
    }
  },
  extraReducers: (builder) => {
    builder
      // Handle fetchResearchModifiers
      .addCase(fetchResearchModifiers.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchResearchModifiers.fulfilled, (state, action) => {
        state.loading = false;
        state.researchModifiers = action.payload.researchModifiers;

        // Si des statistiques sont fournies dans la réponse, les utiliser
        if (action.payload.researchStats) {
          state.researchStats = {
            ...state.researchStats,
            ...action.payload.researchStats
          };
          console.log('Research stats updated from API:', state.researchStats);
        }

        // Log pour vérifier que les modificateurs sont bien stockés
        console.log('Research modifiers stored in state:', state.researchModifiers);
      })
      .addCase(fetchResearchModifiers.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  }
});



// Export actions
export const { updateResearchStats } = researchSlice.actions;

// Sélecteurs directs pour les propriétés

// Export selectors - utiliser des sélecteurs simples pour les propriétés directes
export const selectResearchModifiers = (state) => state.research.researchModifiers;
export const selectResearchStats = (state) => state.research.researchStats;
export const selectResearchLoading = (state) => state.research.loading;
export const selectResearchError = (state) => state.research.error;

// Export reducer
export default researchSlice.reducer;
