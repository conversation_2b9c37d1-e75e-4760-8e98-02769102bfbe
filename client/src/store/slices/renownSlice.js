import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { modifiersApi } from '../../services/api';

// Constants
const RENOWN_TABLE_ID = 17; // ID de la table des modificateurs de renommée (même que l'original)

// Async thunk for fetching renown modifiers
export const fetchRenownModifiers = createAsyncThunk(
  'renown/fetchModifiers',
  async (_, { rejectWithValue }) => {
    try {
      // Fetch renown modifiers
      const modifiers = await modifiersApi.getModifiersByTable(RENOWN_TABLE_ID);

      return modifiers;
    } catch (error) {
      console.error('Error loading renown modifiers:', error);
      return rejectWithValue('Failed to load renown modifiers. Please try again.');
    }
  }
);

// Async thunk for fetching renown data
export const fetchRenownData = createAsyncThunk(
  'renown/fetchData',
  async (_, { rejectWithValue }) => {
    try {
      const data = await modifiersApi.getRenownData();
      return data;
    } catch (error) {
      console.error('Error loading renown data:', error);
      return rejectWithValue('Failed to load renown data. Please try again.');
    }
  }
);

// Initial state
const initialState = {
  renownGeneralModifiers: [],
  renownStats: {
    totalRenown: 0,
    renownPerCycle: 0
  },
  totals: {
    generalEffectsTotal: 0
  },
  loading: false,
  error: null
};

/**
 * Calculate renown per cycle based on modifiers
 * @param {Number} generalEffectsTotal - Total of general effects
 * @returns {Number} - Renown per cycle
 */
export const calculateRenownPerCycle = (generalEffectsTotal) => {
  // Dans la version originale, la renommée par cycle est simplement la somme des effets
  return generalEffectsTotal;
};

// Create the renown slice
const renownSlice = createSlice({
  name: 'renown',
  initialState,
  reducers: {
    // Update renown stats based on game state
    updateRenownStats: (state, action) => {
      const { gameState } = action.payload;

      if (!gameState) return;

      // Calculate totals
      const generalEffectsTotal = Array.isArray(state.renownGeneralModifiers)
        ? state.renownGeneralModifiers.reduce((sum, mod) => sum + mod.effect, 0)
        : 0;

      // Update totals
      state.totals = {
        generalEffectsTotal
      };

      // Calculate renown per cycle
      const renownPerCycle = calculateRenownPerCycle(generalEffectsTotal);

      // Total renown is equal to the total of modifiers
      const totalRenown = generalEffectsTotal;

      // Update renown stats
      state.renownStats = {
        totalRenown,
        renownPerCycle
      };
    }
  },
  extraReducers: (builder) => {
    builder
      // Handle fetchRenownModifiers
      .addCase(fetchRenownModifiers.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchRenownModifiers.fulfilled, (state, action) => {
        state.loading = false;
        state.renownGeneralModifiers = action.payload;

        // Calculer immédiatement le total des effets
        const generalEffectsTotal = Array.isArray(action.payload)
          ? action.payload.reduce((sum, mod) => sum + mod.effect, 0)
          : 0;

        // Mettre à jour les totaux
        state.totals = {
          generalEffectsTotal
        };

        // Mettre à jour la renommée par cycle et la renommée totale
        if (state.renownStats) {
          state.renownStats = {
            ...state.renownStats,
            renownPerCycle: calculateRenownPerCycle(generalEffectsTotal),
            totalRenown: generalEffectsTotal
          };
        }
      })
      .addCase(fetchRenownModifiers.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Handle fetchRenownData
      .addCase(fetchRenownData.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchRenownData.fulfilled, (state, action) => {
        state.loading = false;

        // Update renown stats with data from API
        // La valeur totalRenown de l'API est égale au total des modificateurs
        const totalRenown = action.payload.totalRenown || 0;
        const generalEffectsTotal = state.totals.generalEffectsTotal || 0;

        state.renownStats = {
          ...state.renownStats,
          totalRenown: totalRenown,
          renownPerCycle: calculateRenownPerCycle(generalEffectsTotal)
        };
      })
      .addCase(fetchRenownData.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  }
});

// Export actions
export const { updateRenownStats } = renownSlice.actions;

// Sélecteurs directs pour les propriétés

// Export selectors - utiliser des sélecteurs simples pour les propriétés directes
export const selectRenownGeneralModifiers = (state) => state.renown.renownGeneralModifiers;
export const selectRenownStats = (state) => state.renown.renownStats;
export const selectRenownTotals = (state) => state.renown.totals;
export const selectRenownLoading = (state) => state.renown.loading;

// Export reducer
export default renownSlice.reducer;
