import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { createSelector } from 'reselect';
import apiEndpoint from '../../services/ApiEndpoint';

// Async thunk pour récupérer les événements
export const fetchEvents = createAsyncThunk(
  'events/fetchEvents',
  async (_, { rejectWithValue }) => {
    try {
      return await apiEndpoint.get('game/events');
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

const initialState = {
  events: [],
  loading: false,
  error: null
};

const eventsSlice = createSlice({
  name: 'events',
  initialState,
  reducers: {
    clearEvents: (state) => {
      state.events = [];
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchEvents.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchEvents.fulfilled, (state, action) => {
        state.loading = false;
        state.events = action.payload;
      })
      .addCase(fetchEvents.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  }
});

// Export actions
export const { clearEvents } = eventsSlice.actions;

// Sélecteurs directs

// Sélecteurs simples pour les propriétés directes
export const selectEvents = (state) => state.events.events;
export const selectEventsLoading = (state) => state.events.loading;
export const selectEventsError = (state) => state.events.error;

// Sélecteurs dérivés
export const selectEventsByType = (eventType) => createSelector(
  [selectEvents],
  (events) => events.filter(event => event.event_type === eventType)
);

export const selectEventsByCycle = (cycleNumber) => createSelector(
  [selectEvents],
  (events) => events.filter(event => event.cycle === cycleNumber)
);

export default eventsSlice.reducer;
