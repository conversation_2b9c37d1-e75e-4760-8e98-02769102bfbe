import { createSlice } from '@reduxjs/toolkit';
import { createSelector } from 'reselect';
import { fetchGameState } from './gameSlice';

const initialState = {
  months: [
    { name: 'Tanzanite', season: 'Hiver', description: 'Tanzanite (décembre) - La tanzanite, pierre précieuse bleu-violet rare, symbolise la spiritualité, la paix intérieure et la connexion avec le divin. En décembre, on se réunit pour célébrer notre spiritualité et notre connexion avec les forces supérieures, se préparant à accueillir une nouvelle année pleine de promesses et de possibilités. On en profite pour faire le point avec le reste du clan. Idéalement, on remet en questions nos rancunes et on enterre la hache de guerre, du moins jusqu\'au mois de Diamant.' },
    { name: 'Rubis', season: 'Hiver', description: 'Rubis (janvier) - Le rubis, pierre précieuse rouge vif, symbolise la passion, la force et la vitalité. En janvier, on célèbre ces qualités en se préparant pour une nouvelle année pleine de défis.' },
    { name: '<PERSON><PERSON><PERSON>', season: 'Hiver', description: 'Saphir (février) - Le saphir, pierre précieuse bleue, est associé à la sagesse, à la clarté d\'esprit et à la protection. En février, on se concentre sur la réflexion et la planification stratégique pour les mois à venir.' },
    { name: 'Émeraude', season: 'Printemps', description: 'Émeraude (mars) - L\'émeraude, pierre précieuse verte, représente la croissance, la fertilité et la vitalité. En mars, on célèbre le renouveau de la nature et on se prépare à semer les graines des projets futurs.' },
    { name: 'Diamant', season: 'Printemps', description: 'Diamant (avril) - Le diamant, pierre précieuse transparente et étincelante, symbolise la pureté, la résilience et l\'invincibilité. En avril, on célèbre sa force intérieure et sa capacité à surmonter les défis avec grâce et détermination. C\'est aussi le mois durant lequel on déterre la hache de guerre.' },
    { name: 'Améthyste', season: 'Printemps', description: 'Améthyste (mai) - L\'améthyste, pierre précieuse violet profond, est associée à la spiritualité, à la paix intérieure et à la sagesse. En mai, on se tourne vers l\'introspection et la méditation, on cherche à se connecter avec son moi intérieur et à trouver l\'harmonie.' },
    { name: 'Topaze', season: 'Été', description: 'Topaze (juin) - La topaze, pierre précieuse jaune ou orange, symbolise la créativité, la passion et l\'inspiration. En juin, on met l\'accent sur l\'expression artistique et l\'innovation, on cherche à donner vie à de nouvelles idées et à explorer de nouveaux horizons.' },
    { name: 'Sardoine', season: 'Été', description: 'Sardoine (juillet) - La sardoine, pierre précieuse rouge ou brun rougeâtre, représente la force physique, le courage et la détermination. En juillet, on se concentre sur l\'entraînement et la préparation physique. C\'est aussi une période de récoltes importantes.' },
    { name: 'Perle', season: 'Été', description: 'Perle (août) - La perle, une gemme formée dans les coquillages, symbolise la pureté, la beauté et la perfection. En août, on célèbre la beauté de la pierre et la perfection de l\'univers, se reconnectant avec les éléments qui nous entourent.' },
    { name: 'Opale', season: 'Automne', description: 'Opale (septembre) - L\'opale, pierre précieuse aux reflets multicolores, est associée aux arcanes, au mystère et à la transformation. En septembre, on explore les mystères de l\'univers et on cherche à comprendre les secrets cachés derrière les voiles de la réalité.' },
    { name: 'Aventurine', season: 'Automne', description: 'Aventurine (octobre) - L\'aventurine, pierre précieuse verte avec des inclusions scintillantes, symbolise la chance, la prospérité et l\'abondance. En octobre, on célèbre la chance et la fortune, se lançant dans de nouvelles aventures et cherchant à atteindre de nouvelles profondeurs.' },
    { name: 'Grenat', season: 'Automne', description: 'Grenat (novembre) - Le grenat, pierre précieuse rouge foncé à rouge orangé, représente la passion, l\'énergie et la détermination. En novembre, on se concentre sur nos objectifs avec une énergie renouvelée, travaillant sans relâche pour atteindre nos rêves les plus chers.' }
  ],
  seasons: [
    { name: 'Hiver', factor: 0.5, description: 'L\'hiver est une période difficile où la production de nourriture est réduite.' },
    { name: 'Printemps', factor: 1.2, description: 'Le printemps est une période de renouveau où la production de nourriture est légèrement augmentée.' },
    { name: 'Été', factor: 1.5, description: 'L\'été est la période la plus productive pour l\'agriculture.' },
    { name: 'Automne', factor: 1.0, description: 'L\'automne est une période de récolte où la production de nourriture est normale.' }
  ],
  currentMonth: '',
  currentYear: 0,
  currentSeason: '',
  seasonFactor: 0,
  loading: false,
  error: null
};

const calendarSlice = createSlice({
  name: 'calendar',
  initialState,
  reducers: {
    setCurrentDate: (state, action) => {
      const { month, year, season, seasonFactor } = action.payload;
      state.currentMonth = month;
      state.currentYear = year;
      state.currentSeason = season;
      state.seasonFactor = seasonFactor;
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchGameState.fulfilled, (state, action) => {
        if (action.payload && action.payload.gameState) {
          state.currentMonth = action.payload.gameState.month || '';
          state.currentYear = action.payload.gameState.year || 0;
          state.currentSeason = action.payload.gameState.season || '';
          state.seasonFactor = action.payload.gameState.season_factor || 0;
        }
      });
  }
});

// Export actions
export const { setCurrentDate } = calendarSlice.actions;

// Sélecteurs de base
const selectCalendarState = (state) => state.calendar;

// Sélecteurs optimisés avec createSelector pour la mémoisation
export const selectMonths = createSelector(
  [selectCalendarState],
  (calendarState) => calendarState.months
);

export const selectSeasons = createSelector(
  [selectCalendarState],
  (calendarState) => calendarState.seasons
);

export const selectCurrentMonth = createSelector(
  [selectCalendarState],
  (calendarState) => calendarState.currentMonth
);

export const selectCurrentYear = createSelector(
  [selectCalendarState],
  (calendarState) => calendarState.currentYear
);

export const selectCurrentSeason = createSelector(
  [selectCalendarState],
  (calendarState) => calendarState.currentSeason
);

export const selectSeasonFactor = createSelector(
  [selectCalendarState],
  (calendarState) => calendarState.seasonFactor
);

// Sélecteurs dérivés
export const selectCurrentSeasonData = createSelector(
  [selectSeasons, selectCurrentSeason],
  (seasons, currentSeason) => seasons.find(season => season.name === currentSeason) || {}
);

export const selectCurrentMonthData = createSelector(
  [selectMonths, selectCurrentMonth],
  (months, currentMonth) => months.find(month => month.name === currentMonth) || {}
);

export const selectMonthsBySeason = (seasonName) => createSelector(
  [selectMonths],
  (months) => months.filter(month => month.season === seasonName)
);

export default calendarSlice.reducer;
