import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { inhabitantsApi } from '../../services/api';

// Async thunk for fetching inhabitants
export const fetchInhabitants = createAsyncThunk(
  'population/fetchInhabitants',
  async (params = {}, { rejectWithValue }) => {
    try {
      const data = await inhabitantsApi.getInhabitants(params);
      return data;
    } catch (error) {
      console.error('Error loading inhabitants:', error);
      return rejectWithValue('Failed to load inhabitants. Please try again.');
    }
  }
);

// Async thunk for fetching population stats
export const fetchPopulationStats = createAsyncThunk(
  'population/fetchStats',
  async (_, { rejectWithValue }) => {
    try {
      const data = await inhabitantsApi.getPopulationStats();

      // Transform API response to match our state structure
      if (data && data.stats) {
        return {
          total: data.stats.total || 0,
          pc: data.stats.pcCount || 0,
          npc: (data.stats.total || 0) - (data.stats.pcCount || 0),
          sick: data.stats.sickCount || 0,
          jobDistribution: Object.entries(data.stats.byJob || {}).map(([name, count]) => ({ name, count })),
          ageDistribution: {
            young: 0, // We don't have this data from API
            adult: 0, // We don't have this data from API
            old: 0    // We don't have this data from API
          },
          byRace: data.stats.byRace || {},
          byGender: data.stats.byGender || {},
          averageAge: data.stats.averageAge || 0
        };
      }

      return data.stats || {};
    } catch (error) {
      console.error('Error loading population stats:', error);
      return rejectWithValue('Failed to load population stats. Please try again.');
    }
  }
);

// Async thunk for creating an inhabitant
export const createInhabitant = createAsyncThunk(
  'population/createInhabitant',
  async (inhabitant, { rejectWithValue, dispatch }) => {
    try {
      const data = await inhabitantsApi.createInhabitant(inhabitant);

      // Refresh inhabitants list and stats after creating
      dispatch(fetchInhabitants());
      dispatch(fetchPopulationStats());

      return data;
    } catch (error) {
      console.error('Error creating inhabitant:', error);
      return rejectWithValue('Failed to create inhabitant. Please try again.');
    }
  }
);

// Async thunk for updating an inhabitant
export const updateInhabitant = createAsyncThunk(
  'population/updateInhabitant',
  async ({ id, inhabitant }, { rejectWithValue, dispatch }) => {
    try {
      console.log('populationSlice: Updating inhabitant', {
        id,
        inhabitant,
        job_id: inhabitant.job_id,
        job_id_type: typeof inhabitant.job_id
      });

      // Ensure job_id is a number if it's provided
      if (inhabitant.job_id !== undefined && inhabitant.job_id !== null && inhabitant.job_id !== '') {
        inhabitant.job_id = parseInt(inhabitant.job_id, 10);
        console.log('populationSlice: Converted job_id to number:', inhabitant.job_id);
      }

      const data = await inhabitantsApi.updateInhabitant(id, inhabitant);
      console.log('populationSlice: Update response', data);

      // Refresh inhabitants list and stats after updating
      dispatch(fetchInhabitants());
      dispatch(fetchPopulationStats());

      return data;
    } catch (error) {
      console.error('Error updating inhabitant:', error);
      return rejectWithValue('Failed to update inhabitant. Please try again.');
    }
  }
);

// Async thunk for deleting an inhabitant
export const deleteInhabitant = createAsyncThunk(
  'population/deleteInhabitant',
  async (id, { rejectWithValue, dispatch }) => {
    try {
      const data = await inhabitantsApi.deleteInhabitant(id);

      // Refresh inhabitants list and stats after deleting
      dispatch(fetchInhabitants());
      dispatch(fetchPopulationStats());

      return data;
    } catch (error) {
      console.error('Error deleting inhabitant:', error);
      return rejectWithValue('Failed to delete inhabitant. Please try again.');
    }
  }
);

// Async thunk for syncing inhabitants with jobs
export const syncInhabitantsWithJobs = createAsyncThunk(
  'population/syncWithJobs',
  async (_, { rejectWithValue, dispatch }) => {
    try {
      const data = await inhabitantsApi.syncInhabitantsWithJobs();

      // Refresh inhabitants list and stats after syncing
      dispatch(fetchInhabitants());
      dispatch(fetchPopulationStats());

      return data;
    } catch (error) {
      console.error('Error syncing inhabitants with jobs:', error);
      return rejectWithValue('Failed to sync inhabitants with jobs. Please try again.');
    }
  }
);

// Initial state
const initialState = {
  inhabitants: [],
  populationStats: {
    total: 0,
    pc: 0,
    npc: 0,
    sick: 0,
    jobDistribution: [],
    ageDistribution: {
      young: 0,
      adult: 0,
      old: 0
    }
  },
  filters: {
    name: '',
    job: '',
    pc: null,
    sick: null
  },
  viewMode: 'list', // 'list' or 'summary'
  loading: false,
  error: null,
  currentPage: 1,
  totalPages: 1,
  itemsPerPage: 30
};

// Create the population slice
const populationSlice = createSlice({
  name: 'population',
  initialState,
  reducers: {
    // Set view mode
    setViewMode: (state, action) => {
      state.viewMode = action.payload;
    },

    // Set filters
    setFilters: (state, action) => {
      state.filters = {
        ...state.filters,
        ...action.payload
      };
      state.currentPage = 1; // Reset to first page when filters change
    },

    // Clear filters
    clearFilters: (state) => {
      state.filters = {
        name: '',
        job: '',
        pc: null,
        sick: null
      };
      state.currentPage = 1; // Reset to first page when filters are cleared
    },

    // Set current page
    setCurrentPage: (state, action) => {
      state.currentPage = action.payload;
    },

    // Set items per page
    setItemsPerPage: (state, action) => {
      state.itemsPerPage = action.payload;
      state.currentPage = 1; // Reset to first page when items per page changes
    }
  },
  extraReducers: (builder) => {
    builder
      // Handle fetchInhabitants
      .addCase(fetchInhabitants.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchInhabitants.fulfilled, (state, action) => {
        state.loading = false;
        state.inhabitants = action.payload.inhabitants || [];
        state.totalPages = action.payload.totalPages || 1;
      })
      .addCase(fetchInhabitants.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Handle fetchPopulationStats
      .addCase(fetchPopulationStats.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchPopulationStats.fulfilled, (state, action) => {
        state.loading = false;
        // Ensure we have all required fields even if the API doesn't provide them
        state.populationStats = {
          ...state.populationStats,
          ...action.payload,
          total: action.payload.total || 0,
          pc: action.payload.pc || 0,
          npc: action.payload.npc || 0,
          sick: action.payload.sick || 0
        };
      })
      .addCase(fetchPopulationStats.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Handle createInhabitant
      .addCase(createInhabitant.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createInhabitant.fulfilled, (state) => {
        state.loading = false;
        // Actual data update is handled by the fetchInhabitants and fetchPopulationStats dispatched in the thunk
      })
      .addCase(createInhabitant.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Handle updateInhabitant
      .addCase(updateInhabitant.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateInhabitant.fulfilled, (state) => {
        state.loading = false;
        // Actual data update is handled by the fetchInhabitants and fetchPopulationStats dispatched in the thunk
      })
      .addCase(updateInhabitant.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Handle deleteInhabitant
      .addCase(deleteInhabitant.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteInhabitant.fulfilled, (state) => {
        state.loading = false;
        // Actual data update is handled by the fetchInhabitants and fetchPopulationStats dispatched in the thunk
      })
      .addCase(deleteInhabitant.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Handle syncInhabitantsWithJobs
      .addCase(syncInhabitantsWithJobs.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(syncInhabitantsWithJobs.fulfilled, (state) => {
        state.loading = false;
        // Actual data update is handled by the fetchInhabitants and fetchPopulationStats dispatched in the thunk
      })
      .addCase(syncInhabitantsWithJobs.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  }
});

// Export actions
export const {
  setViewMode,
  setFilters,
  clearFilters,
  setCurrentPage,
  setItemsPerPage
} = populationSlice.actions;

// Sélecteurs directs pour les propriétés

// Export selectors - utiliser des sélecteurs simples pour les propriétés directes
export const selectInhabitants = (state) => state.population.inhabitants;
export const selectPopulationStats = (state) => state.population.populationStats;
export const selectFilters = (state) => state.population.filters;

export const selectViewMode = (state) => state.population.viewMode;
export const selectPopulationLoading = (state) => state.population.loading;
export const selectPopulationError = (state) => state.population.error;
export const selectCurrentPage = (state) => state.population.currentPage;
export const selectTotalPages = (state) => state.population.totalPages;
export const selectItemsPerPage = (state) => state.population.itemsPerPage;

// Export reducer
export default populationSlice.reducer;
