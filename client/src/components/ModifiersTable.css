.modifiers-table-container {
  margin-bottom: 30px;
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.modifiers-table-header {
  background-color: #f5f5f5;
  padding: 15px;
  border-bottom: 1px solid #ddd;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modifiers-table-header h2 {
  margin: 0;
  font-size: 1.2rem;
  color: #333;
}

.total-effect {
  font-weight: bold;
}

.modifiers-table {
  width: 100%;
  border-collapse: collapse;
}

.modifiers-table th,
.modifiers-table td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid #ddd;
}

.modifiers-table th {
  background-color: #f9f9f9;
  font-weight: bold;
}

.modifiers-table tr:last-child td {
  border-bottom: none;
}

.modifiers-table tr:hover {
  background-color: #f9f9f9;
}

.add-modifier-button-container {
  padding: 15px;
  background-color: #f9f9f9;
  border-top: 1px solid #ddd;
  text-align: center;
}

.add-modifier-form {
  padding: 15px;
  background-color: #f9f9f9;
  border-top: 1px solid #ddd;
}

.add-modifier-form h3 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 1.1rem;
}

.form-buttons {
  display: flex;
  gap: 10px;
  margin-top: 15px;
}

.form-row {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
}

.form-group {
  flex: 1;
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.form-group input {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.add-btn,
.edit-btn,
.delete-btn,
.save-btn,
.cancel-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.3s;
}

.add-btn {
  background-color: #4CAF50;
  color: white;
}

.add-btn:hover {
  background-color: #45a049;
}

.edit-btn {
  background-color: #2196F3;
  color: white;
  margin-right: 5px;
  display: inline-block;
}

.edit-btn:hover {
  background-color: #0b7dda;
}

.delete-btn {
  background-color: #f44336;
  color: white;
  display: inline-block;
}

.delete-btn:hover {
  background-color: #d32f2f;
}

.save-btn {
  background-color: #4CAF50;
  color: white;
  margin-right: 5px;
}

.save-btn:hover {
  background-color: #45a049;
}

.cancel-btn {
  background-color: #9e9e9e;
  color: white;
}

.cancel-btn:hover {
  background-color: #757575;
}

.positive {
  color: #4CAF50;
}

.negative {
  color: #f44336;
}

.modifiers-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* Style pour les boutons d'action */
.action-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 5px;
}

.action-buttons button {
  flex: 0 0 auto;
  min-width: 80px;
}
