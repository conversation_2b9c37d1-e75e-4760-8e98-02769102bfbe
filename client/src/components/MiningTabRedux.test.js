import React from 'react';
import { render, screen } from '@testing-library/react';
import { Provider } from 'react-redux';
import configureStore from 'redux-mock-store';
import thunk from 'redux-thunk';
import MiningTabRedux from './MiningTabRedux';

// Mock store
const middlewares = [thunk];
const mockStore = configureStore(middlewares);

// Mock initial state
const initialState = {
  mining: {
    miningGeneralModifiers: [
      { id: 1, name: 'Test Modifier 1', effect: 0.1, type: 'production' }
    ],
    engineeringModifiers: [
      { id: 2, name: 'Test Modifier 2', effect: 0.2, type: 'engineering' }
    ],
    miningTechModifiers: [
      { id: 3, name: 'Test Modifier 3', effect: 0.3, type: 'tech' }
    ],
    miningStats: {
      miners: 8,
      production: 300,
      baseProduction: 240,
      revenuePerMiner: 37.5,
      totalBonus: 0.25,
      moralModifier: 0.1
    },
    totals: {
      generalEffectsTotal: 0.1,
      engineeringEffectsTotal: 0.2,
      techEffectsTotal: 0.3,
      totalProductionBonus: 0.4,
      engineeringMultiplier: 0.12
    },
    loading: false,
    error: null
  },
  resources: {
    mining: {
      miners: 8,
      production: 300,
      revenuePerMiner: 37.5,
      totalProduction: 300,
      moralModifier: 0.1
    }
  }
};

// Mock gameState
const mockGameState = {
  gameState: {
    moral_value: 1.1,
    moral_title: 'Content',
    mine_depth: 3
  },
  jobs: [
    { id: 1, name: 'Miner', number: 10, sick: 2 },
    { id: 2, name: 'Engineer', number: 5, sick: 1 }
  ],
  modifierTables: []
};

// Mock updateMiningData function
const mockUpdateMiningData = jest.fn();

describe('MiningTabRedux', () => {
  let store;
  let component;

  beforeEach(() => {
    store = mockStore(initialState);
    store.dispatch = jest.fn();

    component = render(
      <Provider store={store}>
        <MiningTabRedux 
          gameState={mockGameState} 
          updateMiningData={mockUpdateMiningData}
          moralUpdated={0}
        />
      </Provider>
    );
  });

  it('renders without crashing', () => {
    expect(component).toBeTruthy();
  });

  it('displays the correct title', () => {
    expect(screen.getByText('Minage')).toBeInTheDocument();
  });

  it('displays the correct number of miners', () => {
    expect(screen.getByText('8 mineurs')).toBeInTheDocument();
  });

  it('displays the correct production value', () => {
    expect(screen.getByText('300.0 pièces/cycle')).toBeInTheDocument();
  });

  it('dispatches fetchMiningModifiers on mount', () => {
    expect(store.dispatch).toHaveBeenCalled();
  });

  it('displays all three modifier tables', () => {
    expect(screen.getByText('Effets généraux sur la mine')).toBeInTheDocument();
    expect(screen.getByText('Ingénierie')).toBeInTheDocument();
    expect(screen.getByText('Technologies qui affectent la mine')).toBeInTheDocument();
  });

  it('calls updateMiningData with the correct data', () => {
    expect(mockUpdateMiningData).toHaveBeenCalled();
    const callArg = mockUpdateMiningData.mock.calls[0][0];
    expect(callArg).toHaveProperty('miners', 8);
    expect(callArg).toHaveProperty('production', 300);
    expect(callArg).toHaveProperty('modifiers');
  });
});
