import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import {
  selectMonths,
  selectSeasons,
  selectCurrentMonth,
  selectCurrentYear,
  selectCurrentSeason,
  selectSeasonFactor,
  selectCurrentSeasonData,
  selectMonthsBySeason,
  selectCurrentMonthData
} from '../store/slices/calendarSlice';
import { fetchGameState } from '../store/slices/gameSlice';
import './CalendarTab.css';

const CalendarTabRedux = () => {
  const dispatch = useDispatch();
  const months = useSelector(selectMonths);
  const seasons = useSelector(selectSeasons);
  const currentMonth = useSelector(selectCurrentMonth);
  const currentYear = useSelector(selectCurrentYear);
  const currentSeason = useSelector(selectCurrentSeason);
  const seasonFactor = useSelector(selectSeasonFactor);
  const currentSeasonData = useSelector(selectCurrentSeasonData);
  const currentMonthData = useSelector(selectCurrentMonthData);

  // Obtenir les mois pour chaque saison
  const winterMonths = useSelector(selectMonthsBySeason('Hiver'));
  const springMonths = useSelector(selectMonthsBySeason('Printemps'));
  const summerMonths = useSelector(selectMonthsBySeason('Été'));
  const autumnMonths = useSelector(selectMonthsBySeason('Automne'));

  // Charger les données du jeu au montage du composant
  useEffect(() => {
    dispatch(fetchGameState());
  }, [dispatch]);

  return (
    <div className="calendar-tab">
      <h2>Calendrier</h2>

      <div className="calendar-info">
        <div className="calendar-current">
          <h3>Date Actuelle</h3>
          <div className="calendar-current-details">
            <div className="calendar-current-month">
              <span className="calendar-label">Mois:</span>
              <span className="calendar-value">{currentMonth}</span>
            </div>
            <div className="calendar-current-year">
              <span className="calendar-label">Année:</span>
              <span className="calendar-value">{currentYear}</span>
            </div>
            <div className="calendar-current-season">
              <span className="calendar-label">Saison:</span>
              <span className="calendar-value">{currentSeason} ({Math.round(seasonFactor * 100)}%)</span>
            </div>
          </div>
        </div>

        <div className="calendar-description">
          <h3>Description du Mois</h3>
          <div className="month-description-full">
            {currentMonthData?.description || (currentMonth ? `${currentMonth} - Le mois actuel de l'année.` : '')}
          </div>
        </div>
      </div>

      <div className="calendar-seasons">
        <h3>Saisons</h3>
        <div className="seasons-grid">
          <div className={`season-card ${currentSeason === 'Hiver' ? 'active' : ''}`}>
            <div className="season-icon">❄️</div>
            <div className="season-name">Hiver</div>
            <div className="season-months">Tanzanite, Rubis, Saphir</div>
            <div className="season-factor">Facteur: 50%</div>
            <div className="season-description">L'hiver est une période difficile où la production de nourriture est réduite.</div>
          </div>
          <div className={`season-card ${currentSeason === 'Printemps' ? 'active' : ''}`}>
            <div className="season-icon">🌱</div>
            <div className="season-name">Printemps</div>
            <div className="season-months">Émeraude, Diamant, Améthyste</div>
            <div className="season-factor">Facteur: 100%</div>
            <div className="season-description">Le printemps est une période de renouveau et de croissance.</div>
          </div>
          <div className={`season-card ${currentSeason === 'Été' ? 'active' : ''}`}>
            <div className="season-icon">☀️</div>
            <div className="season-name">Été</div>
            <div className="season-months">Topaze, Sardoine, Perle</div>
            <div className="season-factor">Facteur: 125%</div>
            <div className="season-description">L'été est la période la plus productive pour l'agriculture.</div>
          </div>
          <div className={`season-card ${currentSeason === 'Automne' ? 'active' : ''}`}>
            <div className="season-icon">🍂</div>
            <div className="season-name">Automne</div>
            <div className="season-months">Opale, Aventurine, Grenat</div>
            <div className="season-factor">Facteur: 100%</div>
            <div className="season-description">L'automne est une période de récolte et de préparation pour l'hiver.</div>
          </div>
        </div>
      </div>

      <div className="calendar-months">
        <h3>Mois de l'année</h3>
        <div className="months-grid">
          {months.map((month, index) => (
            <div key={index} className={`month-card ${month.name === currentMonth ? 'active' : ''}`}>
              <div className="month-name">{month.name}</div>
              <div className="month-season">{month.season}</div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default CalendarTabRedux;
