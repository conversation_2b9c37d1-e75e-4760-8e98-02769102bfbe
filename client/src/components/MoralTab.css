.moral-tab {
  padding: 0;
}

.moral-tab h2 {
  margin-bottom: 20px;
}

/* Layout principal */
.moral-main-container {
  display: flex;
  flex-direction: row;
  gap: 20px;
  margin-bottom: 20px;
}

@media (max-width: 768px) {
  .moral-main-container {
    flex-direction: column;
  }
}

/* Panneau de gauche */
.moral-left-panel {
  flex: 1;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Cercle de moral global */
.moral-global-circle {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.moral-circle {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
  margin-bottom: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.moral-emoji {
  font-size: 36px;
  margin-bottom: 5px;
}

.moral-value {
  font-size: 28px;
  font-weight: bold;
}

.moral-title {
  font-size: 20px;
  font-weight: bold;
  margin-top: 8px;
  text-align: center;
}

/* Panneau de droite */
.moral-right-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.moral-effects-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
  height: 100%;
}

.effects-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 20px;
  color: #333;
}

.effect-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  position: relative;
}

.effect-label {
  font-size: 16px;
  color: #333;
  flex: 1;
}

.effect-value {
  font-size: 16px;
  font-weight: bold;
  text-align: right;
  flex: 1;
  margin-right: 20px;
}

.effect-info {
  font-size: 14px;
  color: #666;
  text-align: right;
  flex: 1;
}

/* Impact sur la productivité */
.productivity-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

.productivity-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.productivity-label {
  font-size: 16px;
  color: #333;
}

.productivity-value {
  font-size: 24px;
  font-weight: bold;
}

/* Table des modificateurs */
.moral-modifiers {
  margin-top: 20px;
}

/* Loading state */
.moral-tab.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
}

.moral-tab.loading p {
  margin-top: 16px;
  font-size: 16px;
  color: #666;
}
