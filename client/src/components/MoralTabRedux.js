import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Al<PERSON>, Spin } from 'antd';
import MoralModifiersTable from './MoralModifiersTable';
import './MoralTab.css';
import {
  fetchMoralModifiers,
  updateMoralStats,
  selectMoralModifiers,
  selectMoralStats,
  selectMoralTotals,
  selectMoralLoading,
  selectMoralError
} from '../store/slices/moralSlice';
import { withRedux } from '../hoc/withRedux';

function MoralTabRedux({ gameState, onMoralUpdated }) {
  const dispatch = useDispatch();

  // Sélectionner les données depuis le store Redux
  const moralModifiers = useSelector(selectMoralModifiers);
  const moralStats = useSelector(selectMoralStats);
  const totals = useSelector(selectMoralTotals);
  const loading = useSelector(selectMoralLoading);
  const error = useSelector(selectMoralError);

  // Table ID (selon le fichier de base prompt)
  const moralTableId = 10; // General moral effects - Table ID 10 selon le fichier de base prompt

  // Constants
  const MORAL_BASE_VALUE = 1000; // Base moral value (neutral)
  const MORAL_STANDARD_DEVIATION = 200; // Standard deviation for normal distribution

  // Charger les modificateurs au montage du composant et à chaque fois que le composant devient visible
  useEffect(() => {
    console.log('MoralTabRedux: Chargement des modificateurs');
    dispatch(fetchMoralModifiers());
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Mettre à jour les statistiques de moral quand gameState ou moralModifiers change
  useEffect(() => {
    if (gameState) {
      console.log('MoralTabRedux: Mise à jour des statistiques de moral');
      dispatch(updateMoralStats({ gameState }));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [gameState, moralModifiers]);

  // Fonction pour recharger les modificateurs (utilisée par les tables de modificateurs)
  const handleModifierUpdated = async () => {
    console.log('MoralTabRedux: handleModifierUpdated called');

    try {
      // Dispatch l'action pour recharger les modificateurs
      await dispatch(fetchMoralModifiers());
      console.log('MoralTabRedux: Modifiers fetched successfully');

      // Mettre à jour les statistiques si gameState est disponible
      if (gameState) {
        console.log('MoralTabRedux: Updating moral stats');
        dispatch(updateMoralStats({ gameState }));
      }

      // Notifier le parent si nécessaire
      if (onMoralUpdated) {
        onMoralUpdated();
      }
    } catch (error) {
      console.error('MoralTabRedux: Error updating modifiers:', error);
    }
  };

  // S'abonner aux changements de cache du ModifiersService
  React.useEffect(() => {
    // Fonction de callback appelée lorsque le cache est invalidé
    const handleCacheInvalidated = (tableId) => {
      console.log(`MoralTabRedux: Cache invalidated for table ${tableId}`);

      // Si c'est la table de moral ou toutes les tables, recharger les modificateurs
      if (tableId === moralTableId || tableId === 'all') {
        console.log('MoralTabRedux: Reloading modifiers due to cache invalidation');
        dispatch(fetchMoralModifiers());

        // Mettre à jour les statistiques si gameState est disponible
        if (gameState) {
          dispatch(updateMoralStats({ gameState }));
        }
      }
    };

    // S'abonner aux changements de cache
    let unsubscribe = null;
    if (window.modifiersService && typeof window.modifiersService.subscribe === 'function') {
      unsubscribe = window.modifiersService.subscribe(handleCacheInvalidated);
      console.log('MoralTabRedux: Subscribed to cache changes');
    }

    // Se désabonner lorsque le composant est démonté
    return () => {
      if (unsubscribe) {
        unsubscribe();
        console.log('MoralTabRedux: Unsubscribed from cache changes');
      }
    };
  }, [dispatch, gameState, moralTableId]);

  // Format number as percentage
  const formatPercent = (value) => {
    if (value === undefined || value === null) return '0.0%';
    return `${(value * 100).toFixed(1)}%`;
  };

  // Fonction pour obtenir l'emoji en fonction du moral
  const getMoralEmoji = (moralTitle) => {
    if (!moralTitle) return '😐';

    // Utiliser des comparaisons exactes pour éviter les problèmes avec includes
    if (moralTitle === 'Rébellion') return '🤬';
    if (moralTitle === 'En colère') return '😠';
    if (moralTitle === 'Triste') return '😢';
    if (moralTitle === 'Mécontent') return '😒';
    if (moralTitle === 'Neutre') return '😐';
    if (moralTitle === 'Content') return '😊';
    if (moralTitle === 'Epanoui') return '😄';
    if (moralTitle === 'Heureux') return '😁';
    if (moralTitle === 'Ecstatique') return '🥳';

    console.log('Moral title not recognized:', moralTitle);
    return '😐'; // Valeur par défaut
  };

  // Afficher un message d'erreur si nécessaire
  if (error) {
    return (
      <div className="moral-tab">
        <Alert
          message="Erreur"
          description={error}
          type="error"
          showIcon
        />
      </div>
    );
  }

  // Afficher un indicateur de chargement si nécessaire
  if (loading && !moralStats) {
    return (
      <div className="moral-tab loading">
        <Spin size="large" />
        <p>Chargement des données de moral...</p>
      </div>
    );
  }

  return (
    <div className="moral-tab">
      <h2>Moral de la colonie</h2>

      <div className="moral-main-container">
        {/* Panneau de gauche: Valeur globale du moral avec emoji */}
        <div className="moral-left-panel">
          <div className="moral-global-circle">
            <div className="moral-circle" style={{ backgroundColor: moralStats.currentValue >= 1000 ? '#52c41a' : '#f5222d' }}>
              <div className="moral-emoji">
                {getMoralEmoji(moralStats.title)}
              </div>
              <div className="moral-value">
                {moralStats.currentValue || 1000}
              </div>
            </div>
            <div className="moral-title">
              {moralStats.title || 'Neutre'}
              {/* Afficher l'emoji à côté du titre pour déboguer */}
              <span style={{ marginLeft: '10px' }}>{getMoralEmoji(moralStats.title)}</span>
            </div>
          </div>
        </div>

        {/* Panneau de droite: Effets permanents */}
        <div className="moral-right-panel">
          <div className="moral-effects-card">
            <h3 className="effects-title">Effets permanents</h3>

            <div className="effect-row">
              <div className="effect-label">Logement:</div>
              <div
                className="effect-value"
                style={{ color: moralStats.housingEffect >= 0 ? '#52c41a' : '#f5222d' }}
              >
                {moralStats.housingEffect < 0 ? moralStats.housingEffect : `+${moralStats.housingEffect}`} points
              </div>
              <div className="effect-info">
                {moralStats.dwellingsAvailable < 0
                  ? `${Math.abs(moralStats.dwellingsAvailable)} personnes sans logement`
                  : `${moralStats.dwellingsAvailable} logements disponibles`}
              </div>
            </div>

            <div className="effect-row">
              <div className="effect-label">Population:</div>
              <div
                className="effect-value"
                style={{ color: moralStats.populationEffect >= 0 ? '#52c41a' : '#f5222d' }}
              >
                {moralStats.populationEffect < 0 ? moralStats.populationEffect : `+${moralStats.populationEffect}`} points
              </div>
              <div className="effect-info">
                {moralStats.totalInhabitants} habitants
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Impact sur la productivité */}
      <div className="productivity-card">
        <h3 className="effects-title">Impact sur la productivité</h3>
        <div className="productivity-row">
          <div className="productivity-label">Productivité:</div>
          <div
            className="productivity-value"
            style={{ color: moralStats.productivityPercentage >= 0 ? '#52c41a' : '#f5222d' }}
          >
            {moralStats.productivityPercentage >= 0
              ? `+${moralStats.productivityPercentage}%`
              : `${moralStats.productivityPercentage}%`}
          </div>
        </div>
      </div>

      {/* Table des modificateurs */}
      <div className="moral-modifiers">
        <MoralModifiersTable
          title="Modificateurs de moral"
          tableId={moralTableId}
          modifiers={moralModifiers}
          onModifierUpdated={handleModifierUpdated}
        />
      </div>
    </div>
  );
}

// Connecter le composant à Redux tout en maintenant la compatibilité avec les props
export default withRedux(
  MoralTabRedux,
  (state) => ({
    // Mapper l'état Redux aux props
    moralModifiers: selectMoralModifiers(state),
    moralStats: selectMoralStats(state),
    totals: selectMoralTotals(state),
    loading: selectMoralLoading(state)
  }),
  (dispatch) => ({
    // Mapper les actions Redux aux props
    fetchMoralModifiers: () => dispatch(fetchMoralModifiers()),
    updateMoralStats: (gameState) => dispatch(updateMoralStats(gameState))
  })
);
