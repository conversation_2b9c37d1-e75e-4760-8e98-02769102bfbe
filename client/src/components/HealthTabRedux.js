import React, { useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import ModifiersTable from './ModifiersTable';
import './HealthTab.css';
import {
  fetchHealthModifiers,
  updateHealthStats,
  selectHealthModifiers,
  selectHealthStats,
  selectHealthLoading,
  selectHealthError
} from '../store/slices/healthSlice';
import { withRedux } from '../hoc/withRedux';

function HealthTabRedux({ gameState, onHealthUpdated }) {
  const dispatch = useDispatch();

  // Sélectionner les données depuis le store Redux
  const healthModifiers = useSelector(selectHealthModifiers);
  const healthStats = useSelector(selectHealthStats);
  const loading = useSelector(selectHealthLoading);
  const error = useSelector(selectHealthError);

  // Table ID for health modifiers
  const healthModifiersTableId = 13;

  // Charger les modificateurs au montage du composant
  useEffect(() => {
    dispatch(fetchHealthModifiers());
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Mettre à jour les statistiques de santé quand gameState change
  useEffect(() => {
    if (gameState) {
      dispatch(updateHealthStats({ gameState }));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [gameState]);

  // Fonction pour recharger les modificateurs (utilisée par les tables de modificateurs)
  const handleModifierUpdated = () => {
    console.log('HealthTabRedux: handleModifierUpdated called');

    // Ajouter un délai pour s'assurer que l'API a eu le temps de traiter la demande
    setTimeout(() => {
      // Recharger les modificateurs
      dispatch(fetchHealthModifiers());

      // Mettre à jour les statistiques de santé avec le gameState actuel
      if (gameState) {
        dispatch(updateHealthStats({ gameState }));
        console.log('HealthTabRedux: Health stats updated after modifier change');
      }

      // Notifier le parent si nécessaire
      if (onHealthUpdated) {
        onHealthUpdated();
      }
    }, 500);
  };

  // Format number as percentage
  const formatPercent = (value) => {
    return `${value.toFixed(1)}%`;
  };

  // Format small probability as percentage with more precision
  const formatProbability = (value) => {
    return `${(value * 100).toFixed(2)}%`;
  };

  if (loading) {
    return <div className="loading">Loading health data...</div>;
  }

  if (error) {
    return <div className="error-message">{error}</div>;
  }

  return (
    <div className="health-tab">
      <h2>Santé</h2>

      {/* Health Stats Cards */}
      <div className="health-stats-cards">
        <div className="health-stat-card">
          <div className="health-stat-icon">🤒</div>
          <div className="health-stat-title">Malades</div>
          <div className="health-stat-value">{healthStats.totalSick}</div>
          <div className="health-stat-description">Nombre total de malades</div>
        </div>

        <div className="health-stat-card">
          <div className="health-stat-icon">📊</div>
          <div className="health-stat-title">Taux de maladie</div>
          <div className="health-stat-value">{formatPercent(healthStats.sickPercentage)}</div>
          <div className="health-stat-description">Pourcentage de la population malade</div>
        </div>

        <div className="health-stat-card">
          <div className="health-stat-icon">⚕️</div>
          <div className="health-stat-title">Guérisseurs</div>
          <div className="health-stat-value">{healthStats.activeHealers}</div>
          <div className="health-stat-description">Nombre de guérisseurs actifs</div>
        </div>

        <div className="health-stat-card">
          <div className="health-stat-icon">🔮</div>
          <div className="health-stat-title">Risque de maladie</div>
          <div className="health-stat-value">{formatProbability(healthStats.sickProbability)}</div>
          <div className="health-stat-description">Probabilité de maladie au prochain cycle</div>
        </div>
      </div>

      {/* Health Information Card */}
      <div className="health-info-card">
        <h3>Système de santé</h3>
        <p>Les malades sont calculés au début de chaque mois en fonction du risque de maladie. Tous les malades sont automatiquement guéris après que les productions aient été générées.</p>
        <p>Les guérisseurs réduisent le risque de maladie dans la colonie. Plus vous avez de guérisseurs, plus le risque de maladie diminue.</p>
      </div>

      {/* Health Modifiers Table */}
      <div className="modifiers-tables">
        <ModifiersTable
          title="Modificateurs de santé"
          tableId={healthModifiersTableId}
          modifiers={healthModifiers}
          onModifierUpdated={handleModifierUpdated}
        />
      </div>
    </div>
  );
}

// Connecter le composant à Redux tout en maintenant la compatibilité avec les props
export default withRedux(
  HealthTabRedux,
  (state) => ({
    // Mapper l'état Redux aux props
    healthModifiers: selectHealthModifiers(state),
    healthStats: selectHealthStats(state),
    loading: selectHealthLoading(state),
    error: selectHealthError(state)
  }),
  (dispatch) => ({
    // Mapper les actions Redux aux props
    fetchHealthModifiers: () => dispatch(fetchHealthModifiers()),
    updateHealthStats: (gameState) => dispatch(updateHealthStats(gameState))
  })
);
