import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import ModifiersTable from './ModifiersTable';
import './FoodTab.css';
import {
  fetchFoodModifiers,
  updateFoodStats,
  selectFoodGeneralModifiers,
  selectFoodTechModifiers,
  selectPerishableModifiers,
  selectFoodStats,
  selectFoodTotals,
  selectFoodLoading
} from '../store/slices/foodSlice';
import { fetchGameState } from '../store/slices/gameSlice';
import { selectMoralStats } from '../store/slices/moralSlice';
import { withRedux } from '../hoc/withRedux';

function FoodTabRedux({ gameState, updateFoodData, moralUpdated }) {
  const dispatch = useDispatch();

  // Sélectionner les données depuis le store Redux
  const foodGeneralModifiers = useSelector(selectFoodGeneralModifiers);
  const foodTechModifiers = useSelector(selectFoodTechModifiers);
  const perishableModifiers = useSelector(selectPerishableModifiers);
  const foodStats = useSelector(selectFoodStats);
  const totals = useSelector(selectFoodTotals);
  const loading = useSelector(selectFoodLoading);
  const moralStats = useSelector(selectMoralStats); // Récupérer les stats de moral depuis le store Redux

  // Table IDs (gardés identiques à l'original)
  const foodGeneralTableId = 1; // General food effects
  const foodTechTableId = 2;    // Technology effects on food
  const perishableTableId = 3;  // Perishable rate

  // Constants
  const FOOD_PER_FARMER = 4;

  // Charger les modificateurs au montage du composant et quand gameState ou moralUpdated changent
  useEffect(() => {
    dispatch(fetchFoodModifiers());
  }, [dispatch, gameState?.modifierTables, moralUpdated]);



  // Mettre à jour les statistiques alimentaires quand gameState ou moralStats change
  useEffect(() => {
    if (gameState && moralStats) {
      // Log farmer information for debugging
      const farmers = Array.isArray(gameState.jobs) ? gameState.jobs.find(j => j.name === 'Farmer') : null;
      console.log('FoodTabRedux: Farmer information from gameState:', {
        farmers,
        farmersNumber: farmers?.number || 0,
        farmersSick: farmers?.sick || 0,
        effectiveFarmers: (farmers?.number || 0) - (farmers?.sick || 0),
        jobsLength: gameState.jobs?.length || 0
      });

      console.log('FoodTabRedux: Mise à jour des statistiques avec moral:', moralStats);

      // Mettre à jour les statistiques alimentaires avec les données de moral
      dispatch(updateFoodStats({
        gameState: {
          jobs: gameState.jobs,
          gameState: gameState.gameState,
          modifierTables: gameState.modifierTables,
          season_factor: gameState.season_factor,
          food_reserves: gameState.food_reserves,
          foodData: gameState.foodData
        },
        moralModifier: moralStats.modifier,
        moralTitle: moralStats.title
      }));
    }
  }, [dispatch, gameState, moralStats]);

  // Ajouter un effet pour forcer une mise à jour des statistiques lorsque le gameState change
  // Nous utilisons une référence pour suivre les changements dans le nombre de fermiers
  const prevFarmersRef = React.useRef(null);

  useEffect(() => {
    if (gameState && gameState.jobs) {
      // Obtenir le nombre actuel de fermiers
      const currentFarmers = Array.isArray(gameState.jobs) ? gameState.jobs.find(j => j.name === 'Farmer') : null;
      const currentFarmersNumber = currentFarmers?.number || 0;

      // Comparer avec le nombre précédent de fermiers
      if (prevFarmersRef.current !== null && prevFarmersRef.current !== currentFarmersNumber) {
        console.log('FoodTabRedux: Nombre de fermiers changé', {
          previous: prevFarmersRef.current,
          current: currentFarmersNumber
        });

        // Mettre à jour les statistiques alimentaires
        dispatch(updateFoodStats({
          gameState: {
            jobs: gameState.jobs,
            gameState: gameState.gameState,
            modifierTables: gameState.modifierTables,
            season_factor: gameState.season_factor,
            food_reserves: gameState.food_reserves,
            foodData: gameState.foodData
          },
          moralModifier: moralStats?.modifier,
          moralTitle: moralStats?.title
        }));
      }

      // Mettre à jour la référence
      prevFarmersRef.current = currentFarmersNumber;
    }
  }, [dispatch, gameState, moralStats]);

  // Update food data in parent component if the function is provided
  useEffect(() => {
    if (foodStats && updateFoodData) {
      console.log('FoodTabRedux: Updating parent component data:', foodStats);
      updateFoodData({
        production: foodStats.production || 0,
        consumption: foodStats.consumption || 0,
        net: foodStats.net || 0,
        netAfterPerishable: (foodStats.net - foodStats.perishableLoss) || 0,
        modifiers: {
          perishableRate: totals.perishableFactor || 0,
          generalEffectsTotal: totals.generalEffectsTotal || 0,
          techEffectsTotal: totals.techEffectsTotal || 0,
          totalProductionBonus: totals.totalProductionBonus || 0
        }
      });
    }
  }, [foodStats, totals, updateFoodData]);

  // Fonction pour recharger les modificateurs (utilisée par les tables de modificateurs)
  // Version modifiée pour forcer un rechargement complet
  const handleModifierUpdated = () => {
    console.log('FoodTabRedux: handleModifierUpdated appelé');

    // Forcer un rechargement complet en vidant le cache du service ModifiersService
    const modifiersService = require('../services/ModifiersService').default;

    // Vider complètement le cache pour forcer un rechargement
    modifiersService._modifiersCache = {};

    // Recharger immédiatement les modificateurs
    dispatch(fetchFoodModifiers()).then(() => {
      console.log('FoodTabRedux: Modificateurs rechargés après mise à jour');

      // Mettre à jour les statistiques alimentaires avec le gameState actuel et les données de moral
      if (gameState && moralStats) {
        console.log('FoodTabRedux: Mise à jour des statistiques avec moral après modification:', moralStats);
        dispatch(updateFoodStats({
          gameState: {
            jobs: gameState.jobs,
            gameState: gameState.gameState,
            modifierTables: gameState.modifierTables,
            season_factor: gameState.season_factor,
            food_reserves: gameState.food_reserves,
            foodData: gameState.foodData
          },
          moralModifier: moralStats.modifier,
          moralTitle: moralStats.title
        }));
      }
    });
  };

  // Format number as percentage
  const formatPercent = (value) => {
    if (value === undefined || value === null) return '0.0%';
    return `${(value * 100).toFixed(1)}%`;
  };

  // Format number with 1 decimal place
  const formatNumber = (num) => {
    if (num === undefined || num === null) return '0.0';
    return parseFloat(num).toFixed(1);
  };

  // Get moral emoji
  const getMoralEmoji = (moralTitle) => {
    if (!moralTitle) return '😐';

    if (moralTitle.includes('Rébellion')) return '🤬';
    if (moralTitle.includes('En colère')) return '😠';
    if (moralTitle.includes('Triste')) return '😢';
    if (moralTitle.includes('Mécontent')) return '😒';
    if (moralTitle.includes('Neutre')) return '😐';
    if (moralTitle.includes('Content')) return '😊';
    if (moralTitle.includes('Epanoui')) return '😄';
    if (moralTitle.includes('Heureux')) return '😁';
    if (moralTitle.includes('Ecstatique')) return '🥳';

    return '😐'; // Default value
  };

  if (loading) {
    return <div className="loading">Loading food data...</div>;
  }

  // Get moral modifier value from Redux store
  const moralTitle = moralStats?.title || 'Neutre';
  const moralModifier = moralStats?.modifier !== undefined ? moralStats.modifier : 0;

  return (
    <div className="food-tab">
      <h2>Production de Nourriture</h2>

      {/* Food Stats Cards */}
      <div className="food-stats-cards">
        <div className="food-stat-card">
          <div className="food-stat-icon">🍎</div>
          <div className="food-stat-title">Réserves actuelles</div>
          <div className="food-stat-value">{formatNumber(foodStats.currentReserves)} unités</div>
          <div className="food-stat-description">Quantité de nourriture disponible</div>
        </div>

        <div className="food-stat-card">
          <div className="food-stat-icon">🗑️</div>
          <div className="food-stat-title">Péremption</div>
          <div className="food-stat-value negative">-{formatNumber(foodStats.perishableLoss)} unités/cycle</div>
          <div className="food-stat-description">{formatPercent(gameState?.foodData?.modifiers?.perishableRate || totals.perishableFactor || 0)} des réserves actuelles</div>
        </div>

        <div className="food-stat-card">
          <div className="food-stat-icon">🌾</div>
          <div className="food-stat-title">Production {foodStats.isNextSeasonPrediction ? '(prochaine saison)' : ''}</div>
          <div className="food-stat-value positive">+{formatNumber(foodStats.production)} unités/cycle</div>
          <div className="food-stat-description">Nourriture produite chaque cycle</div>
        </div>

        <div className="food-stat-card">
          <div className="food-stat-icon">🍽️</div>
          <div className="food-stat-title">Consommation</div>
          <div className="food-stat-value negative">-{formatNumber(foodStats.consumption)} unités/cycle</div>
          <div className="food-stat-description">Nourriture consommée chaque cycle</div>
        </div>

        <div className="food-stat-card">
          <div className="food-stat-icon">⚖️</div>
          <div className="food-stat-title">Bilan par cycle {foodStats.isNextSeasonPrediction ? '(prochaine saison)' : ''}</div>
          <div className={`food-stat-value ${foodStats.netAfterPerishable >= 0 ? 'positive' : 'negative'}`}>
            {foodStats.netAfterPerishable >= 0 ? '+' : ''}{formatNumber(foodStats.netAfterPerishable)} unités
          </div>
          <div className="food-stat-description">Production moins consommation et péremption</div>
        </div>

        <div className="food-stat-card">
          <div className="food-stat-icon">🌡️</div>
          <div className="food-stat-title">Facteur saisonnier {foodStats.isNextSeasonPrediction ? '(prochaine saison)' : ''}</div>
          <div className="food-stat-value">{formatPercent(foodStats.seasonFactor)}</div>
          <div className="food-stat-description">Effet de la saison sur la production</div>
        </div>

        <div className="food-stat-card">
          <div className="food-stat-icon">👨‍🌾</div>
          <div className="food-stat-title">Nourriture par fermier</div>
          <div className="food-stat-value">{FOOD_PER_FARMER} unités</div>
          <div className="food-stat-description">Production de base par fermier</div>
        </div>

        <div className="food-stat-card">
          <div className="food-stat-icon">📈</div>
          <div className="food-stat-title">Effet des modificateurs</div>
          <div className="food-stat-value positive">{formatPercent(totals.totalProductionBonus || 0)}</div>
          <div className="food-stat-description">Bonus total à la production ({formatPercent(totals.generalEffectsTotal || 0)} général, {formatPercent(totals.techEffectsTotal || 0)} tech)</div>
        </div>

        <div className="food-stat-card">
          <div className="food-stat-icon">{getMoralEmoji(moralTitle)}</div>
          <div className="food-stat-title">Moral: {moralTitle}</div>
          <div className={`food-stat-value ${moralModifier >= 0 ? 'positive' : 'negative'}`}>
            {moralModifier >= 0 ? '+' : ''}{formatPercent(moralModifier)}
          </div>
          <div className="food-stat-description">Impact du moral sur la production</div>
        </div>


      </div>

      {/* Tables de modificateurs */}
      <div className="modifiers-tables">
        <ModifiersTable
          title="Effets généraux sur la nourriture"
          tableId={foodGeneralTableId}
          modifiers={foodGeneralModifiers}
          onModifierUpdated={handleModifierUpdated}
        />

        <ModifiersTable
          title="Technologies qui affectent la production de nourriture"
          tableId={foodTechTableId}
          modifiers={foodTechModifiers}
          onModifierUpdated={handleModifierUpdated}
        />

        <ModifiersTable
          title="Péremption des vivres"
          tableId={perishableTableId}
          modifiers={perishableModifiers}
          onModifierUpdated={handleModifierUpdated}
        />
      </div>
    </div>
  );
}

// Connecter le composant à Redux tout en maintenant la compatibilité avec les props
export default withRedux(
  FoodTabRedux,
  {
    // Sélecteurs
    foodGeneralModifiers: selectFoodGeneralModifiers,
    foodTechModifiers: selectFoodTechModifiers,
    perishableModifiers: selectPerishableModifiers,
    foodStats: selectFoodStats,
    totals: selectFoodTotals,
    loading: selectFoodLoading
  },
  {
    // Actions
    fetchFoodModifiers,
    updateFoodStats
  }
);
