import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import ModifiersTable from './ModifiersTable';
import './FoodTab.css';
import {
  selectGameState,
  selectJobs,
  selectFoodData,
  selectModifierTables,
  fetchGameState
} from '../store/slices/gameSlice';
import { selectMoralStats } from '../store/slices/moralSlice';

function FoodTabRedux({ gameState, updateFoodData, moralUpdated }) {
  const dispatch = useDispatch();

  // Sélectionner les données depuis le store Redux
  const reduxGameState = useSelector(selectGameState);
  const jobs = useSelector(selectJobs);
  const foodData = useSelector(selectFoodData);
  const modifierTables = useSelector(selectModifierTables);
  const moralStats = useSelector(selectMoralStats); // Récupérer les stats de moral depuis le store Redux

  // Table IDs (gardés identiques à l'original)
  const foodGeneralTableId = 1; // General food effects
  const foodTechTableId = 2;    // Technology effects on food
  const perishableTableId = 3;  // Perishable rate

  // Constants
  const FOOD_PER_FARMER = 4;

  // Get modifier tables for food
  const foodGeneralModifiers = modifierTables.filter(table => table.id === foodGeneralTableId);
  const foodTechModifiers = modifierTables.filter(table => table.id === foodTechTableId);
  const perishableModifiers = modifierTables.filter(table => table.id === perishableTableId);

  // Get farmer information
  const farmers = Array.isArray(jobs) ? jobs.find(j => j.name === 'Farmer') : null;
  const effectiveFarmers = (farmers?.number || 0) - (farmers?.sick || 0);

  // Fonction pour recharger les modificateurs (utilisée par les tables de modificateurs)
  const handleModifierUpdated = () => {
    console.log('FoodTabRedux: handleModifierUpdated appelé');
    // Refetch game state to get updated calculations
    dispatch(fetchGameState({ force: true }));
  };

  // Format number as percentage
  const formatPercent = (value) => {
    if (value === undefined || value === null) return '0.0%';
    return `${(value * 100).toFixed(1)}%`;
  };

  // Format number with 1 decimal place
  const formatNumber = (num) => {
    if (num === undefined || num === null) return '0.0';
    return parseFloat(num).toFixed(1);
  };

  // Get moral emoji
  const getMoralEmoji = (moralTitle) => {
    if (!moralTitle) return '😐';

    if (moralTitle.includes('Rébellion')) return '🤬';
    if (moralTitle.includes('En colère')) return '😠';
    if (moralTitle.includes('Triste')) return '😢';
    if (moralTitle.includes('Mécontent')) return '😒';
    if (moralTitle.includes('Neutre')) return '😐';
    if (moralTitle.includes('Content')) return '😊';
    if (moralTitle.includes('Epanoui')) return '😄';
    if (moralTitle.includes('Heureux')) return '😁';
    if (moralTitle.includes('Ecstatique')) return '🥳';

    return '😐'; // Default value
  };

  // Use backend-calculated food data
  const currentReserves = reduxGameState?.food_reserves || 0;
  const production = foodData?.production || 0;
  const consumption = foodData?.consumption || 0;
  const net = foodData?.net || 0;
  const perishableLoss = foodData?.perishableLoss || 0;
  const netAfterPerishable = foodData?.netAfterPerishable || 0;
  const seasonFactor = foodData?.nextSeasonFactor || reduxGameState?.season_factor || 1;
  const perishableRate = foodData?.modifiers?.perishableRate || 0;
  const generalEffects = foodData?.modifiers?.generalEffects || 0;
  const techEffects = foodData?.modifiers?.techEffects || 0;

  // Get moral modifier value from Redux store
  const moralTitle = moralStats?.title || 'Neutre';
  const moralModifier = moralStats?.modifier !== undefined ? moralStats.modifier : 0;

  return (
    <div className="food-tab">
      <h2>Production de Nourriture</h2>

      {/* Food Stats Cards */}
      <div className="food-stats-cards">
        <div className="food-stat-card">
          <div className="food-stat-icon">🍎</div>
          <div className="food-stat-title">Réserves actuelles</div>
          <div className="food-stat-value">{formatNumber(currentReserves)} unités</div>
          <div className="food-stat-description">Quantité de nourriture disponible</div>
        </div>

        <div className="food-stat-card">
          <div className="food-stat-icon">🗑️</div>
          <div className="food-stat-title">Péremption</div>
          <div className="food-stat-value negative">-{formatNumber(perishableLoss)} unités/cycle</div>
          <div className="food-stat-description">{formatPercent(perishableRate)} des réserves actuelles</div>
        </div>

        <div className="food-stat-card">
          <div className="food-stat-icon">🌾</div>
          <div className="food-stat-title">Production</div>
          <div className="food-stat-value positive">+{formatNumber(production)} unités/cycle</div>
          <div className="food-stat-description">Nourriture produite chaque cycle</div>
        </div>

        <div className="food-stat-card">
          <div className="food-stat-icon">🍽️</div>
          <div className="food-stat-title">Consommation</div>
          <div className="food-stat-value negative">-{formatNumber(consumption)} unités/cycle</div>
          <div className="food-stat-description">Nourriture consommée chaque cycle</div>
        </div>

        <div className="food-stat-card">
          <div className="food-stat-icon">⚖️</div>
          <div className="food-stat-title">Bilan par cycle</div>
          <div className={`food-stat-value ${netAfterPerishable >= 0 ? 'positive' : 'negative'}`}>
            {netAfterPerishable >= 0 ? '+' : ''}{formatNumber(netAfterPerishable)} unités
          </div>
          <div className="food-stat-description">Production moins consommation et péremption</div>
        </div>

        <div className="food-stat-card">
          <div className="food-stat-icon">🌡️</div>
          <div className="food-stat-title">Facteur saisonnier</div>
          <div className="food-stat-value">{formatPercent(seasonFactor)}</div>
          <div className="food-stat-description">Effet de la saison sur la production</div>
        </div>

        <div className="food-stat-card">
          <div className="food-stat-icon">👨‍🌾</div>
          <div className="food-stat-title">Nourriture par fermier</div>
          <div className="food-stat-value">{FOOD_PER_FARMER} unités</div>
          <div className="food-stat-description">Production de base par fermier</div>
        </div>

        <div className="food-stat-card">
          <div className="food-stat-icon">📈</div>
          <div className="food-stat-title">Effet des modificateurs</div>
          <div className="food-stat-value positive">+{formatPercent(generalEffects + techEffects)}</div>
          <div className="food-stat-description">Bonus total à la production ({formatPercent(generalEffects)} général, {formatPercent(techEffects)} tech)</div>
        </div>

        <div className="food-stat-card">
          <div className="food-stat-icon">{getMoralEmoji(moralTitle)}</div>
          <div className="food-stat-title">Moral: {moralTitle}</div>
          <div className={`food-stat-value ${moralModifier >= 0 ? 'positive' : 'negative'}`}>
            {moralModifier >= 0 ? '+' : ''}{formatPercent(moralModifier)}
          </div>
          <div className="food-stat-description">Impact du moral sur la production</div>
        </div>


      </div>

      {/* Tables de modificateurs */}
      <div className="modifiers-tables">
        <ModifiersTable
          title="Effets généraux sur la nourriture"
          tableId={foodGeneralTableId}
          modifiers={foodGeneralModifiers}
          onModifierUpdated={handleModifierUpdated}
        />

        <ModifiersTable
          title="Technologies qui affectent la production de nourriture"
          tableId={foodTechTableId}
          modifiers={foodTechModifiers}
          onModifierUpdated={handleModifierUpdated}
        />

        <ModifiersTable
          title="Péremption des vivres"
          tableId={perishableTableId}
          modifiers={perishableModifiers}
          onModifierUpdated={handleModifierUpdated}
        />
      </div>
    </div>
  );
}

export default FoodTabRedux;
