.population-tab {
  padding: 0;
  max-width: 100%;
}

.population-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 15px;
}

.stat-card {
  background-color: white;
  border-radius: 8px;
  padding: 15px;
  text-align: center;
  flex: 1;
  min-width: 120px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stat-value {
  font-size: 28px !important;
  font-weight: bold !important;
  margin-bottom: 5px !important;
}

.stat-label {
  color: #666 !important;
  font-size: 16px !important;
}

.population-controls {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 10px;
  align-items: center;
}

.population-actions {
  display: flex;
  gap: 10px;
}

.filters {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.filters select {
  padding: 8px;
  border-radius: 4px;
  border: 1px solid #ccc;
  background-color: white;
}

.search {
  display: flex;
}

.search form {
  display: flex;
}

.search input {
  padding: 8px;
  border-radius: 4px 0 0 4px;
  border: 1px solid #ccc;
  border-right: none;
  min-width: 200px;
}

.search button {
  padding: 8px 12px;
  border-radius: 0 4px 4px 0;
  border: 1px solid #ccc;
  background-color: #f0f0f0;
  cursor: pointer;
}

.inhabitants-list {
  overflow-x: auto;
  margin-bottom: 20px;
}

.inhabitants-list table {
  width: 100%;
  border-collapse: collapse;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
}

.inhabitants-list th {
  background-color: #f8f8f8;
  padding: 12px 10px;
  text-align: left;
  cursor: pointer;
  user-select: none;
  border-bottom: 2px solid #eee;
  color: #333;
}

.inhabitants-list th:hover {
  background-color: #e0e0e0;
}

.inhabitants-list td {
  padding: 10px;
  border-bottom: 1px solid #eee;
}

.inhabitants-list tr:hover {
  background-color: #f5f9ff;
  cursor: pointer;
}

.inhabitants-list tr.sick {
  background-color: #fff0f0;
}

.inhabitants-list tr.sick:hover {
  background-color: #ffe6e6;
}

.no-data {
  text-align: center;
  padding: 20px;
  color: #666;
}

.pc-badge {
  background-color: #4CAF50;
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.8em;
  margin-left: 8px;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  margin-top: 20px;
}

.pagination button {
  padding: 5px 10px;
  border: 1px solid #ccc;
  background-color: #f0f0f0;
  cursor: pointer;
  border-radius: 4px;
}

.pagination button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination span {
  margin: 0 10px;
}

/* Modal styles */
.inhabitant-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.inhabitant-modal-content {
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  width: 80%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.close {
  position: absolute;
  top: 10px;
  right: 15px;
  font-size: 24px;
  cursor: pointer;
}

.inhabitant-header {
  margin-bottom: 20px;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.inhabitant-header h3 {
  margin-bottom: 5px;
}

.inhabitant-subheader {
  color: #666;
}

.inhabitant-status {
  margin-bottom: 15px;
  padding: 10px;
  border-radius: 4px;
}

.status-sick {
  color: #d32f2f;
  background-color: #ffebee;
  padding: 8px;
  border-radius: 4px;
}

.status-healthy {
  color: #388e3c;
  background-color: #e8f5e9;
  padding: 8px;
  border-radius: 4px;
}

.inhabitant-arrival {
  margin-bottom: 15px;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #eee;
}

.inhabitant-description, .inhabitant-history {
  margin-bottom: 15px;
}

.inhabitant-description h4, .inhabitant-history h4 {
  margin-bottom: 10px;
  border-bottom: 1px solid #eee;
  padding-bottom: 5px;
}

.history-content {
  line-height: 1.6;
}

.inhabitant-actions {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.edit-button {
  padding: 8px 16px;
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
}

.edit-button:hover {
  background-color: #3e8e41;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .population-stats {
    flex-direction: column;
  }

  .population-controls {
    flex-direction: column;
  }

  .search {
    width: 100%;
    margin-top: 10px;
  }

  .search input {
    width: 100%;
  }

  .inhabitant-modal-content {
    width: 95%;
    padding: 15px;
  }
}
