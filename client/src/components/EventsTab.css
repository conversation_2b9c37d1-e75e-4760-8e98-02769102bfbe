.events-tab {
  padding: 20px;
}

.events-list-full {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-top: 20px;
}

.event-card {
  display: flex;
  background-color: white;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s;
}

.event-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.event-icon {
  font-size: 24px;
  margin-right: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
}

.event-content {
  flex: 1;
}

.event-cycle {
  font-size: 12px;
  color: #666;
  margin-bottom: 5px;
}

.event-description {
  font-size: 14px;
}

/* Styles spécifiques par type d'événement */
.event-card.sickness {
  border-left: 4px solid #e74c3c;
}

.event-card.crime {
  border-left: 4px solid #8e44ad;
}

.event-card.research {
  border-left: 4px solid #3498db;
}

.event-card.famine {
  border-left: 4px solid #f39c12;
}

.event-card.treasury {
  border-left: 4px solid #2ecc71;
}

.no-events {
  text-align: center;
  padding: 30px;
  color: #666;
  font-style: italic;
}

.loading, .error {
  text-align: center;
  padding: 30px;
  color: #666;
}
