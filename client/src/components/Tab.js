import React from 'react';

/**
 * Composant d'onglet à utiliser avec TabManager
 */
const Tab = ({ tabId, label, icon, isActive, onTabChange, children }) => {
  return (
    <div className="tab-container">
      <div 
        className={`tab-header ${isActive ? 'active' : ''}`}
        onClick={() => onTabChange(tabId)}
      >
        {icon && <span className="tab-icon">{icon}</span>}
        <span className="tab-label">{label}</span>
      </div>
      {isActive && (
        <div className="tab-content">
          {children}
        </div>
      )}
    </div>
  );
};

export default Tab;
