import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import ModifiersTable from './ModifiersTable';
import { setMaterialsData } from '../store/slices/resourcesSlice';
import withRedux from '../hoc/withRedux';
import {
  fetchMaterialsModifiers,
  updateMaterialsStats,
  selectMaterialsGeneralModifiers,
  selectMaterialsTechModifiers,
  selectMaterialsStats,
  selectMaterialsTotals,
  selectMaterialsLoading
} from '../store/slices/materialsSlice';
import { selectMoralStats } from '../store/slices/moralSlice';
import './MaterialsTab.css';

/**
 * Version Redux du composant MaterialsTab
 * Cette version utilise Redux pour gérer son état
 *
 * @param {Object} props - Les propriétés du composant
 * @param {Object} props.gameState - L'état du jeu
 * @param {Function} props.updateMaterialsData - Fonction pour mettre à jour les données des matériaux dans le composant parent
 * @returns {JSX.Element} - Le composant MaterialsTabRedux
 */
function MaterialsTabRedux({ gameState, updateMaterialsData, moralUpdated }) {
  const dispatch = useDispatch();

  // Sélectionner les données depuis le store Redux
  const materialsGeneralModifiers = useSelector(selectMaterialsGeneralModifiers);
  const materialsTechModifiers = useSelector(selectMaterialsTechModifiers);
  const materialsStats = useSelector(selectMaterialsStats);
  const totals = useSelector(selectMaterialsTotals);
  const moralStats = useSelector(selectMoralStats); // Récupérer les stats de moral depuis le store Redux

  // Table IDs
  const materialsGeneralTableId = 11; // General materials effects
  const materialsTechTableId = 12;    // Technology effects on materials

  // Charger les modificateurs au montage du composant et quand gameState ou moralUpdated changent
  useEffect(() => {
    dispatch(fetchMaterialsModifiers());
  }, [dispatch, gameState?.modifierTables, moralUpdated]);

  // Mettre à jour les statistiques des matériaux quand gameState ou moralStats change
  useEffect(() => {
    if (gameState && moralStats) {
      console.log('MaterialsTabRedux: Mise à jour des statistiques avec moral:', moralStats);

      // Extraire les jobs pour surveiller les changements dans le nombre d'ouvriers et de mineurs
      const workers = gameState.jobs?.find(j => j.name === 'Worker');
      const miners = gameState.jobs?.find(j => j.name === 'Miner');

      console.log('MaterialsTabRedux: Informations sur les ouvriers et mineurs:', {
        workers,
        miners,
        workersNumber: workers?.number || 0,
        workersSick: workers?.sick || 0,
        effectiveWorkers: (workers?.number || 0) - (workers?.sick || 0),
        minersNumber: miners?.number || 0,
        minersSick: miners?.sick || 0,
        effectiveMiners: (miners?.number || 0) - (miners?.sick || 0)
      });

      // Mettre à jour les statistiques des matériaux
      dispatch(updateMaterialsStats({
        gameState,
        moralModifier: moralStats.modifier,
        moralTitle: moralStats.title
      }));

      // Forcer une mise à jour des modificateurs également
      dispatch(fetchMaterialsModifiers());
    }
  }, [dispatch, gameState, moralStats]);

  // Surveiller spécifiquement les changements dans le nombre d'ouvriers et de mineurs
  useEffect(() => {
    if (gameState?.jobs) {
      const workers = gameState.jobs.find(j => j.name === 'Worker');
      const miners = gameState.jobs.find(j => j.name === 'Miner');

      // Extraire les nombres d'ouvriers et de mineurs actifs
      const activeWorkers = workers ? (workers.number - workers.sick) : 0;
      const activeMiners = miners ? (miners.number - miners.sick) : 0;

      console.log('MaterialsTabRedux: Changement détecté dans le nombre d\'ouvriers ou de mineurs:', {
        activeWorkers,
        activeMiners
      });

      // Si les nombres d'ouvriers ou de mineurs actifs ont changé, mettre à jour les statistiques
      if (activeWorkers !== materialsStats.workers || activeMiners !== materialsStats.miners) {
        console.log('MaterialsTabRedux: Mise à jour des statistiques suite à un changement dans le nombre d\'ouvriers ou de mineurs');

        // Mettre à jour les statistiques des matériaux
        if (moralStats) {
          dispatch(updateMaterialsStats({
            gameState,
            moralModifier: moralStats.modifier,
            moralTitle: moralStats.title
          }));
        }
      }
    }
  }, [dispatch, gameState?.jobs, materialsStats.workers, materialsStats.miners, moralStats]);

  // Utiliser les valeurs du store Redux pour le moral
  const moralTitle = moralStats?.title || 'Neutre';
  const moralModifier = moralStats?.modifier !== undefined ? moralStats.modifier : 0;

  // Format number as percentage
  const formatPercent = (value) => {
    if (value === undefined || value === null) return '0.0%';
    return `${(value * 100).toFixed(1)}%`;
  };

  // Format number with 1 decimal place
  const formatNumber = (value) => {
    if (value === undefined || value === null) return '0.0';
    return parseFloat(value).toFixed(1);
  };

  // Get emoji for moral title
  const getMoralEmoji = (moralTitle) => {
    if (!moralTitle) return '😐';

    if (moralTitle.includes('Rébellion')) return '🤬';
    if (moralTitle.includes('En colère')) return '😠';
    if (moralTitle.includes('Triste')) return '😢';
    if (moralTitle.includes('Mécontent')) return '😒';
    if (moralTitle.includes('Neutre')) return '😐';
    if (moralTitle.includes('Content')) return '😊';
    if (moralTitle.includes('Epanoui')) return '😄';
    if (moralTitle.includes('Heureux')) return '😁';
    if (moralTitle.includes('Ecstatique')) return '🥳';

    return '😐'; // Default value
  };

  // Fonction pour recharger les modificateurs (utilisée par les tables de modificateurs)
  const handleModifierUpdated = () => {
    console.log('MaterialsTabRedux: handleModifierUpdated appelé');

    // D'abord, s'assurer que les modificateurs sont chargés
    dispatch(fetchMaterialsModifiers()).then(() => {
      // Ensuite, mettre à jour les statistiques des matériaux avec le gameState actuel et les données de moral
      setTimeout(() => {
        if (gameState && moralStats) {
          console.log('MaterialsTabRedux: Mise à jour des statistiques avec moral après modification:', moralStats);
          dispatch(updateMaterialsStats({
            gameState,
            moralModifier: moralStats.modifier,
            moralTitle: moralStats.title
          }));
        }
      }, 100); // Petit délai pour s'assurer que les modificateurs sont bien pris en compte
    });
  };

  // Update materials data in parent component if the function is provided
  // (gardé pour la compatibilité avec l'approche par props)
  useEffect(() => {
    if (materialsStats) {
      // Always update parent component data if the function is provided
      if (updateMaterialsData) {
        console.log('MaterialsTabRedux: Updating parent component data:', materialsStats);
        updateMaterialsData({
          ...materialsStats,
          production: materialsStats.production || 0,
          workers: materialsStats.workers || 0,
          miners: materialsStats.miners || 0,
          modifiers: {
            generalEffectsTotal: totals.generalEffectsTotal || 0,
            techEffectsTotal: totals.techEffectsTotal || 0,
            totalProductionBonus: totals.totalProductionBonus || 0
          }
        });
      }

      // Dispatch to resourcesSlice for backward compatibility
      dispatch(setMaterialsData({
        workers: materialsStats.workers || 0,
        miners: materialsStats.miners || 0,
        production: materialsStats.production || 0,
        totalProduction: materialsStats.production || 0,
        moralModifier: materialsStats.moralModifier || 0
      }));
    }
  }, [dispatch, materialsStats, totals, updateMaterialsData]);

  return (
    <div className="materials-tab">
      <h2>Matériaux</h2>

      {/* Materials Stats Cards */}
      <div className="materials-stats-cards">
        <div className="materials-stat-card">
          <div className="materials-stat-icon">👷</div>
          <div className="materials-stat-title">Ouvriers actifs</div>
          <div className="materials-stat-value">{materialsStats.workers} ouvriers</div>
          <div className="materials-stat-description">Nombre d'ouvriers au travail</div>
        </div>

        <div className="materials-stat-card">
          <div className="materials-stat-icon">⛏️</div>
          <div className="materials-stat-title">Mineurs actifs</div>
          <div className="materials-stat-value">{materialsStats.miners} mineurs</div>
          <div className="materials-stat-description">Nombre de mineurs au travail</div>
        </div>

        <div className="materials-stat-card">
          <div className="materials-stat-icon">🧱</div>
          <div className="materials-stat-title">Production de base</div>
          <div className="materials-stat-value">{formatNumber(materialsStats.baseProduction)} unités/cycle</div>
          <div className="materials-stat-description">4 unités par ouvrier, 2 unités par mineur</div>
        </div>

        <div className="materials-stat-card">
          <div className="materials-stat-icon">📈</div>
          <div className="materials-stat-title">Effet des modificateurs</div>
          <div className="materials-stat-value positive">{formatPercent(totals.generalEffectsTotal + totals.techEffectsTotal)}</div>
          <div className="materials-stat-description">Bonus total à la production</div>
        </div>

        <div className="materials-stat-card">
          <div className="materials-stat-icon">{getMoralEmoji(moralTitle)}</div>
          <div className="materials-stat-title">Moral: {moralTitle}</div>
          <div className={`materials-stat-value ${moralModifier >= 0 ? 'positive' : 'negative'}`}>
            {moralModifier >= 0 ? '+' : ''}{formatPercent(moralModifier).replace('%', '')}%
          </div>
          <div className="materials-stat-description">Impact du moral sur la production</div>
        </div>

        <div className="materials-stat-card">
          <div className="materials-stat-icon">🏭</div>
          <div className="materials-stat-title">Production totale</div>
          <div className="materials-stat-value">{formatNumber(materialsStats.production)} unités/cycle</div>
          <div className="materials-stat-description">Production de matériaux par cycle</div>
        </div>
      </div>

      {/* Tables de modificateurs */}
      <div className="modifiers-tables">
        <ModifiersTable
          title="Effets généraux sur les matériaux"
          tableId={materialsGeneralTableId}
          modifiers={materialsGeneralModifiers}
          onModifierUpdated={handleModifierUpdated}
        />

        <ModifiersTable
          title="Technologies qui affectent la génération de matériaux"
          tableId={materialsTechTableId}
          modifiers={materialsTechModifiers}
          onModifierUpdated={handleModifierUpdated}
        />
      </div>
    </div>
  );
}

// Connecter le composant à Redux tout en maintenant la compatibilité avec les props
export default withRedux(
  MaterialsTabRedux,
  (state) => ({
    // Mapper l'état Redux aux props
    materialsDataFromRedux: state.resources.materials,
    materialsGeneralModifiers: selectMaterialsGeneralModifiers(state),
    materialsTechModifiers: selectMaterialsTechModifiers(state),
    materialsStats: selectMaterialsStats(state),
    totals: selectMaterialsTotals(state),
    loading: selectMaterialsLoading(state)
  }),
  (dispatch) => ({
    // Mapper les actions Redux aux props
    updateMaterialsDataRedux: (data) => dispatch(setMaterialsData(data)),
    fetchMaterialsModifiers: () => dispatch(fetchMaterialsModifiers()),
    updateMaterialsStats: (gameState) => dispatch(updateMaterialsStats(gameState))
  })
);
