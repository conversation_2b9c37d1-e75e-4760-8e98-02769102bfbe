.cycle-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.cycle-modal {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  width: 80%;
  max-width: 900px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  animation: modal-appear 0.3s ease-out;
}

@keyframes modal-appear {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.cycle-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #eee;
}

.cycle-modal-header h2 {
  margin: 0;
  color: #333;
}

.close-button {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
}

.close-button:hover {
  color: #333;
}

.cycle-modal-content {
  padding: 20px;
  overflow-y: auto;
  max-height: calc(90vh - 140px);
}

.cycle-section {
  margin-bottom: 25px;
  padding-bottom: 20px;
  border-bottom: 1px solid #eee;
}

.cycle-section:last-child {
  border-bottom: none;
}

.cycle-section h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #444;
  font-size: 1.2rem;
}

.events-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.event-item {
  display: flex;
  align-items: center;
  padding: 10px 15px;
  margin-bottom: 8px;
  border-radius: 4px;
  background-color: #f5f5f5;
}

.event-item.sickness {
  background-color: #ffebee;
}

.event-item.crime {
  background-color: #e8eaf6;
}

.event-item.research {
  background-color: #e0f7fa;
}

.event-item.famine {
  background-color: #fff8e1;
}

.event-icon {
  font-size: 1.5rem;
  margin-right: 15px;
}

.resources-changes {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.resource-change {
  background-color: #f9f9f9;
  border-radius: 6px;
  padding: 15px;
}

.resource-change h4 {
  margin-top: 0;
  margin-bottom: 10px;
  color: #555;
  border-bottom: 1px solid #eee;
  padding-bottom: 8px;
}

.resource-details {
  display: flex;
  flex-direction: column;
}

.resource-detail {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  font-size: 0.9rem;
}

.resource-detail.total {
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px dashed #ddd;
  font-weight: bold;
}

.positive {
  color: #4CAF50;
}

.negative {
  color: #F44336;
}

.probabilities {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
}

.probability {
  background-color: #f5f5f5;
  padding: 10px 15px;
  border-radius: 4px;
  display: flex;
  justify-content: space-between;
}

.calendar-update {
  background-color: #f5f5f5;
  padding: 15px;
  border-radius: 6px;
  border-left: 4px solid #4CAF50;
}

.calendar-update p {
  margin: 5px 0;
}

.cycle-modal-footer {
  padding: 15px 20px;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: flex-end;
}

.continue-button {
  padding: 10px 20px;
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.3s;
}

.continue-button:hover {
  background-color: #45a049;
}
