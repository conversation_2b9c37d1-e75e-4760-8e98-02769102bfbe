import React, { useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { 
  fetchBuildingPlans, 
  startConstructionProject,
  selectBuildingPlans, 
  selectConstructionProjects,
  selectBuildingsLoading, 
  selectBuildingsError 
} from '../store/slices/buildingsSlice';
import './ConstructionTab.css';

const ConstructionTabRedux = () => {
  const dispatch = useDispatch();
  const buildingPlans = useSelector(selectBuildingPlans);
  const constructionProjects = useSelector(selectConstructionProjects);
  const loading = useSelector(selectBuildingsLoading);
  const error = useSelector(selectBuildingsError);
  
  const [selectedPlan, setSelectedPlan] = useState(null);
  const [buildingName, setBuildingName] = useState('');

  useEffect(() => {
    dispatch(fetchBuildingPlans());
  }, [dispatch]);

  const handlePlanSelect = (plan) => {
    setSelectedPlan(plan);
    setBuildingName(plan.default_name || '');
  };

  const handleStartConstruction = () => {
    if (selectedPlan && buildingName.trim()) {
      dispatch(startConstructionProject({
        planId: selectedPlan.id,
        name: buildingName
      }));
      setSelectedPlan(null);
      setBuildingName('');
    }
  };

  if (loading) {
    return <div className="loading">Chargement des plans de construction...</div>;
  }

  if (error) {
    return <div className="error">Erreur: {error}</div>;
  }

  return (
    <div className="construction-tab">
      <h2>Construction</h2>
      
      <div className="construction-content">
        <div className="building-plans-section">
          <h3>Plans de construction disponibles</h3>
          
          {buildingPlans && buildingPlans.length > 0 ? (
            <div className="building-plans-grid">
              {buildingPlans.map((plan) => (
                <div 
                  key={plan.id} 
                  className={`building-plan-card ${selectedPlan && selectedPlan.id === plan.id ? 'selected' : ''}`}
                  onClick={() => handlePlanSelect(plan)}
                >
                  <div className="plan-header">
                    <h4 className="plan-name">{plan.name}</h4>
                    <div className="plan-type">{plan.type}</div>
                  </div>
                  <div className="plan-details">
                    <div className="plan-stat">
                      <span className="stat-label">Coût:</span>
                      <span className="stat-value">{plan.cost} pièces</span>
                    </div>
                    <div className="plan-stat">
                      <span className="stat-label">Temps:</span>
                      <span className="stat-value">{plan.build_time} cycles</span>
                    </div>
                    <div className="plan-stat">
                      <span className="stat-label">Ouvriers:</span>
                      <span className="stat-value">{plan.workers_required}</span>
                    </div>
                  </div>
                  <div className="plan-description">{plan.description}</div>
                </div>
              ))}
            </div>
          ) : (
            <div className="no-plans">Aucun plan de construction disponible.</div>
          )}
        </div>
        
        <div className="construction-details-section">
          {selectedPlan ? (
            <div className="selected-plan-details">
              <h3>Détails du plan sélectionné</h3>
              <h4>{selectedPlan.name}</h4>
              <p>{selectedPlan.description}</p>
              
              <div className="plan-requirements">
                <h5>Prérequis</h5>
                <ul>
                  <li>Coût: {selectedPlan.cost} pièces</li>
                  <li>Temps de construction: {selectedPlan.build_time} cycles</li>
                  <li>Ouvriers requis: {selectedPlan.workers_required}</li>
                  {selectedPlan.tech_level_required > 0 && (
                    <li>Niveau technologique requis: {selectedPlan.tech_level_required}</li>
                  )}
                </ul>
              </div>
              
              <div className="building-name-input">
                <label htmlFor="building-name">Nom du bâtiment:</label>
                <input 
                  type="text" 
                  id="building-name" 
                  value={buildingName} 
                  onChange={(e) => setBuildingName(e.target.value)}
                  placeholder="Entrez un nom pour ce bâtiment"
                />
              </div>
              
              <button 
                className="btn btn-primary start-construction-btn"
                onClick={handleStartConstruction}
                disabled={!buildingName.trim()}
              >
                Démarrer la construction
              </button>
            </div>
          ) : (
            <div className="no-plan-selected">
              <p>Sélectionnez un plan de construction pour voir les détails.</p>
            </div>
          )}
        </div>
      </div>
      
      <div className="current-projects-section">
        <h3>Projets de construction en cours</h3>
        
        {constructionProjects && constructionProjects.length > 0 ? (
          <div className="construction-projects-list">
            {constructionProjects.map((project) => (
              <div key={project.id} className="construction-project-card">
                <div className="project-header">
                  <h4 className="project-name">{project.name}</h4>
                  <div className="project-type">{project.type}</div>
                </div>
                <div className="project-progress">
                  <div className="progress-bar">
                    <div 
                      className="progress-fill" 
                      style={{ width: `${(project.progress / project.total_time) * 100}%` }}
                    ></div>
                  </div>
                  <div className="progress-text">
                    {project.progress} / {project.total_time} cycles
                  </div>
                </div>
                <div className="project-workers">
                  <span className="workers-label">Ouvriers assignés:</span>
                  <span className="workers-value">{project.workers_assigned} / {project.workers_required}</span>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="no-projects">Aucun projet de construction en cours.</div>
        )}
      </div>
    </div>
  );
};

export default ConstructionTabRedux;
