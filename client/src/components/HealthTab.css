.health-tab {
  padding: 0;
}

.health-tab h2 {
  margin-bottom: 20px;
}

.health-stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.health-stat-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.health-stat-icon {
  font-size: 2rem;
  margin-bottom: 10px;
}

.health-stat-title {
  font-weight: bold;
  margin-bottom: 5px;
}

.health-stat-value {
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 5px;
}

.health-stat-description {
  font-size: 0.9rem;
  color: #666;
}

.health-info-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 30px;
}

.health-info-card h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #333;
}

.health-info-card p {
  margin-bottom: 10px;
  line-height: 1.5;
}

.modifiers-tables {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* Loading state */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
  font-size: 1.2rem;
  color: #666;
}

/* Error state */
.error-message {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
  font-size: 1.2rem;
  color: #d32f2f;
  background-color: #ffebee;
  border-radius: 8px;
  padding: 20px;
}

@media (max-width: 768px) {
  .health-stats-cards {
    grid-template-columns: 1fr;
  }
}
