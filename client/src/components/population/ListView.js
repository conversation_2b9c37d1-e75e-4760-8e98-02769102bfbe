import React, { useState } from 'react';
import { useDispatch } from 'react-redux';
import './ListView.css';
import InhabitantEditModal from './InhabitantEditModal';
import QuickAddModal from './QuickAddModal';
import { translateRace, translateJob, formatFantasyDate } from '../../utils/translations';
import config from '../../config';
import { wsSend } from '../../middleware/websocketMiddleware';
import { transferJob } from '../../store/slices/gameSlice';
import apiEndpoint from '../../services/ApiEndpoint';

const ListView = ({
  inhabitants,
  loading,
  error,
  filters,
  handleFilterChange,
  handleSearch,
  handleSort,
  sortBy,
  sortOrder,
  page,
  totalPages,
  handlePageChange,
  handleInhabitantSelect,
  selectedInhabitant,
  closeInhabitantDetails,
  gameState,
  onInhabitantUpdated,
  onInhabitantDeleted
}) => {
  const dispatch = useDispatch();
  // Helper function to translate gender
  const translateGender = (gender) => {
    if (!gender) return 'Inconnu';
    return gender === 'Male' ? 'Homme' : gender === 'Female' ? 'Femme' : gender;
  };

  // Helper function to get job name from job ID
  const getJobName = (jobId) => {
    if (!jobId) return 'Aucun';
    const job = gameState?.jobs?.find(j => j.id === jobId);
    return job ? translateJob(job.name) : 'Inconnu';
  };
  const [editingInhabitant, setEditingInhabitant] = useState(null);
  const [showQuickAddModal, setShowQuickAddModal] = useState(false);
  // Render loading state
  if (loading && !inhabitants.length) {
    return <div className="loading">Chargement des données de population...</div>;
  }

  // Render error state
  if (error && !inhabitants.length) {
    return <div className="error">Erreur: {error}</div>;
  }

  return (
    <div className="list-view">
      {/* Filters and Search */}
      <div className="population-controls">
        <div className="filters">
          <select name="job" value={filters.job} onChange={handleFilterChange}>
            <option value="">Tous les métiers</option>
            {gameState?.jobs?.map(job => (
              <option key={job.id} value={job.id}>{translateJob(job.name)}</option>
            ))}
          </select>

          <select name="race" value={filters.race} onChange={handleFilterChange}>
            <option value="">Toutes les races</option>
            <option value="Dwarf">Nain</option>
            <option value="Human">Humain</option>
            <option value="Elf">Elfe</option>
            <option value="Halfling">Halfelin</option>
            <option value="Gnome">Gnome</option>
          </select>

          <select name="gender" value={filters.gender} onChange={handleFilterChange}>
            <option value="">Tous les genres</option>
            <option value="Male">Homme</option>
            <option value="Female">Femme</option>
          </select>

          <select name="isPC" value={filters.isPC} onChange={handleFilterChange}>
            <option value="">PC/NPC</option>
            <option value="1">PC</option>
            <option value="0">NPC</option>
          </select>

          <select name="isSick" value={filters.isSick} onChange={handleFilterChange}>
            <option value="">Santé</option>
            <option value="1">Malade</option>
            <option value="0">En bonne santé</option>
          </select>
        </div>

        <div className="search-and-actions">
          <form onSubmit={handleSearch} className="search-form">
            <input
              type="text"
              name="searchQuery"
              placeholder="Rechercher..."
              value={filters.searchQuery || ''}
              onChange={(e) => handleFilterChange({ target: { name: 'searchQuery', value: e.target.value } })}
            />
            <button type="submit">🔍</button>
          </form>

          <button
            className="quick-add-button"
            onClick={() => setShowQuickAddModal(true)}
          >
            Ajout rapide
          </button>
        </div>
      </div>

      {/* Inhabitants List */}
      <div className="inhabitants-list">
        <table>
          <thead>
            <tr>
              <th onClick={() => handleSort('last_name')}>
                Nom {sortBy === 'last_name' && (sortOrder === 'asc' ? '↑' : '↓')}
              </th>
              <th onClick={() => handleSort('race')}>
                Race {sortBy === 'race' && (sortOrder === 'asc' ? '↑' : '↓')}
              </th>
              <th onClick={() => handleSort('gender')}>
                Genre {sortBy === 'gender' && (sortOrder === 'asc' ? '↑' : '↓')}
              </th>
              <th onClick={() => handleSort('job_id')}>
                Métier {sortBy === 'job_id' && (sortOrder === 'asc' ? '↑' : '↓')}
              </th>
              <th onClick={() => handleSort('age')}>
                Âge {sortBy === 'age' && (sortOrder === 'asc' ? '↑' : '↓')}
              </th>
              <th onClick={() => handleSort('arrival_year')}>
                Arrivée {sortBy === 'arrival_year' && (sortOrder === 'asc' ? '↑' : '↓')}
              </th>
              <th>Statut</th>
            </tr>
          </thead>
          <tbody>
            {inhabitants.length > 0 ? (
              inhabitants.map(inhabitant => (
                <tr
                  key={inhabitant.id}
                  onClick={() => handleInhabitantSelect(inhabitant)}
                  className={inhabitant.is_sick ? 'sick' : ''}
                >
                  <td>
                    {inhabitant.first_name} {inhabitant.last_name}
                    {inhabitant.is_pc === 1 && <span className="pc-badge">PC</span>}
                  </td>
                  <td>{translateRace(inhabitant.race)}</td>
                  <td>{translateGender(inhabitant.gender)}</td>
                  <td>{getJobName(inhabitant.job_id)}</td>
                  <td>{inhabitant.age} ans</td>
                  <td>{inhabitant.arrival_month} {inhabitant.arrival_year}</td>
                  <td>{inhabitant.is_sick === 1 ? '🤒 Malade' : '✅ En bonne santé'}</td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan="7" className="no-data">Aucun habitant trouvé</td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="pagination">
          <button
            onClick={() => handlePageChange(1)}
            disabled={page === 1}
          >
            &laquo;
          </button>
          <button
            onClick={() => handlePageChange(page - 1)}
            disabled={page === 1}
          >
            &lt;
          </button>

          <span>Page {page} sur {totalPages}</span>

          <button
            onClick={() => handlePageChange(page + 1)}
            disabled={page === totalPages}
          >
            &gt;
          </button>
          <button
            onClick={() => handlePageChange(totalPages)}
            disabled={page === totalPages}
          >
            &raquo;
          </button>
        </div>
      )}

      {/* Inhabitant Details Modal */}
      {selectedInhabitant && (
        <div className="inhabitant-modal" onClick={closeInhabitantDetails}>
          <div className="inhabitant-modal-content" onClick={(e) => e.stopPropagation()}>
            <span className="close" onClick={closeInhabitantDetails}>&times;</span>

            <div className="inhabitant-header">
              <h3>
                {selectedInhabitant.first_name} {selectedInhabitant.last_name}
                {selectedInhabitant.is_pc === 1 && <span className="pc-badge">PC</span>}
              </h3>
              <div className="inhabitant-subheader">
                {translateRace(selectedInhabitant.race)} • {translateGender(selectedInhabitant.gender)} •
                {getJobName(selectedInhabitant.job_id)} •
                {selectedInhabitant.age} ans
              </div>
            </div>

            <div className="inhabitant-status">
              {selectedInhabitant.is_sick === 1 ?
                <div className="status-sick">🤒 Actuellement malade</div> :
                <div className="status-healthy">✅ En bonne santé</div>
              }
            </div>

            <div className="inhabitant-arrival">
              <strong>Date d'arrivée:</strong> {selectedInhabitant.arrival_month} {selectedInhabitant.arrival_year} (Cycle {selectedInhabitant.arrival_cycle})
            </div>

            <div className="inhabitant-description">
              <h4>Description</h4>
              <p>{selectedInhabitant.short_description || "Aucune description disponible."}</p>
            </div>

            <div className="inhabitant-history">
              <h4>Historique</h4>
              <div className="history-content">
                {selectedInhabitant.history || "Aucun historique disponible."}
              </div>
            </div>

            <div className="inhabitant-actions">
              <button
                className="edit-button"
                onClick={() => setEditingInhabitant(selectedInhabitant)}
              >
                Modifier
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Edit Modal */}
      {editingInhabitant && (
        <InhabitantEditModal
          inhabitant={editingInhabitant}
          onClose={() => setEditingInhabitant(null)}
          onSave={async (formData) => {
            try {
              // Vérifier si le métier a changé
              const oldJobId = editingInhabitant.job_id !== undefined && editingInhabitant.job_id !== null ?
                              parseInt(editingInhabitant.job_id, 10) : null;
              const newJobId = formData.job_id !== undefined && formData.job_id !== null && formData.job_id !== '' ?
                              parseInt(formData.job_id, 10) : null;
              const isJobChanged = oldJobId !== newJobId;

              console.log('ListView: Mise à jour d\'un habitant', {
                inhabitantId: editingInhabitant.id,
                oldJobId,
                oldJobIdType: typeof oldJobId,
                newJobId,
                newJobIdType: typeof newJobId,
                isJobChanged,
                formData: formData,
                editingInhabitant: editingInhabitant,
                availableJobs: gameState?.jobs
              });

              // Nouvelle approche: Utiliser transferJob pour la mise à jour du métier
              if (isJobChanged) {
                try {
                  // Dans la vue détaillée, nous permettons de changer librement tous les métiers
                  // sans passer par la réserve de workers

                  // Trouver les noms des métiers pour le logging
                  const oldJob = gameState?.jobs?.find(j => j.id === oldJobId);
                  const newJob = gameState?.jobs?.find(j => j.id === newJobId);

                  console.log('ListView: Changement de métier dans la vue détaillée', {
                    inhabitantId: editingInhabitant.id,
                    oldJobId,
                    oldJobName: oldJob?.name,
                    newJobId,
                    newJobName: newJob?.name
                  });

                  // Utiliser directement l'API REST pour mettre à jour l'habitant
                  // Cette approche est plus simple et évite les problèmes avec transferJob
                  const formattedData = {
                    ...formData,
                    job_id: newJobId
                  };

                  console.log('ListView: Mise à jour via API REST:', formattedData);

                  // Utiliser apiEndpoint au lieu de fetch direct
                  const data = await apiEndpoint.put(`inhabitants/${editingInhabitant.id}`, formattedData);
                  console.log('ListView: Réponse de l\'API:', data);

                  // Fermer les modales
                  setEditingInhabitant(null);
                  closeInhabitantDetails();

                  // Notifier le parent que l'habitant a été mis à jour
                  if (onInhabitantUpdated) {
                    onInhabitantUpdated({
                      ...editingInhabitant,
                      ...formData
                    }, true);
                  }

                  console.log('ListView: Mise à jour du métier réussie');
                } catch (error) {
                  console.error('Erreur lors du changement de métier:', error);
                  alert(`Erreur lors du changement de métier: ${error.message}`);
                }
              } else {
                // S'assurer que job_id est un nombre (important pour la comparaison côté serveur)
                const formattedData = {
                  ...formData,
                  job_id: formData.job_id ? parseInt(formData.job_id, 10) : null
                };

                console.log('ListView: Envoi des données formatées via API REST:', formattedData);

                // Approche 2: Utiliser l'API REST pour les mises à jour sans changement de métier
                try {
                  // Utiliser apiEndpoint au lieu de fetch direct
                  const data = await apiEndpoint.put(`inhabitants/${editingInhabitant.id}`, formattedData);

                  setEditingInhabitant(null);
                  closeInhabitantDetails();

                  if (onInhabitantUpdated) {
                    onInhabitantUpdated(data.inhabitant, false);
                  }

                  console.log('ListView: Mise à jour effectuée via API REST');
                } catch (error) {
                  console.error('Error updating inhabitant:', error);
                }
              }
            } catch (error) {
              console.error('Error updating inhabitant:', error);
            }
          }}
          onDelete={async (id) => {
            try {
              console.log('ListView: Suppression d\'un habitant', { inhabitantId: id });

              // Utiliser WebSocket pour la suppression
              dispatch(wsSend({
                type: 'population/deleteInhabitant',
                payload: id
              }));

              // Fermer les modales
              setEditingInhabitant(null);
              closeInhabitantDetails();

              // Notifier le parent que l'habitant a été supprimé
              if (onInhabitantDeleted) {
                onInhabitantDeleted(id);
              }

              console.log('ListView: Suppression envoyée via WebSocket');
            } catch (error) {
              console.error('Error deleting inhabitant:', error);
            }
          }}
          gameState={gameState}
        />
      )}

      {/* Quick Add Modal */}
      {showQuickAddModal && (
        <QuickAddModal
          onClose={() => setShowQuickAddModal(false)}
          onSave={(newInhabitants) => {
            setShowQuickAddModal(false);
            if (onInhabitantUpdated && newInhabitants.length > 0) {
              // Pass the last created inhabitant to trigger update
              onInhabitantUpdated(newInhabitants[newInhabitants.length - 1]);
            }
          }}
          gameState={gameState}
        />
      )}
    </div>
  );
};

export default ListView;
