import React from 'react';
import './ViewToggle.css';

const ViewToggle = ({ currentView, onViewChange }) => {
  return (
    <div className="view-toggle">
      <button
        className={`toggle-button ${currentView === 'list' ? 'active' : ''}`}
        onClick={() => onViewChange('list')}
        title="Vue Détaillée (← Flèche gauche)"
      >
        <span className="toggle-icon">👥</span> Vue Détaillée
      </button>
      <button
        className={`toggle-button ${currentView === 'summary' ? 'active' : ''}`}
        onClick={() => onViewChange('summary')}
        title="Vue Résumé (→ Flèche droite)"
      >
        <span className="toggle-icon">📊</span> Vue Résumé
      </button>
    </div>
  );
};

export default ViewToggle;
