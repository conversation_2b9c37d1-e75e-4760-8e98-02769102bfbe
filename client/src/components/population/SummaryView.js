import React, { useState, useEffect } from 'react';
import './SummaryView.css';
import config from '../../config';
import { useDispatch } from 'react-redux';
import apiEndpoint from '../../services/ApiEndpoint';
import { transferJob, changeFreeWorkers } from '../../store/slices/gameSlice';

const SummaryView = ({
  gameState,
  onJobChange,
  onFreeChange,
  isShiftPressed,
  setIsShiftPressed,
  stats,
  inhabitants
}) => {
  const dispatch = useDispatch();

  // États locaux pour une mise à jour immédiate de l'interface
  const [jobs, setJobs] = useState(gameState?.jobs || []);
  const [totalPopulation, setTotalPopulation] = useState(0);
  const [totalSick, setTotalSick] = useState(0);
  const [totalSalary, setTotalSalary] = useState(0);
  const [pendingJobChanges, setPendingJobChanges] = useState({});

  // Initialize shift key detection if not provided
  const [internalShiftPressed, setInternalShiftPressed] = useState(false);
  const effectiveIsShiftPressed = isShiftPressed !== undefined ? isShiftPressed : internalShiftPressed;
  const effectiveSetIsShiftPressed = setIsShiftPressed || setInternalShiftPressed;

  // Handle shift key press
  useEffect(() => {
    if (setIsShiftPressed === undefined) {
      const handleKeyDown = (e) => {
        // Ne pas traiter les événements clavier si l'élément actif est un input, textarea ou select
        if (
          e.target.tagName === 'INPUT' ||
          e.target.tagName === 'TEXTAREA' ||
          e.target.tagName === 'SELECT' ||
          e.target.isContentEditable
        ) {
          return;
        }

        if (e.key === 'Shift') {
          setInternalShiftPressed(true);
        }
      };

      const handleKeyUp = (e) => {
        if (e.key === 'Shift') {
          setInternalShiftPressed(false);
        }
      };

      window.addEventListener('keydown', handleKeyDown);
      window.addEventListener('keyup', handleKeyUp);

      return () => {
        window.removeEventListener('keydown', handleKeyDown);
        window.removeEventListener('keyup', handleKeyUp);
      };
    }
  }, [setIsShiftPressed]);

  // Fonction pour récupérer les données à jour
  const fetchUpdatedData = async () => {
    try {
      // Utiliser apiEndpoint au lieu de fetch direct
      // Forcer une synchronisation des habitants avec les emplois d'abord
      await apiEndpoint.put('inhabitants/sync-jobs');

      // Ensuite récupérer les données à jour
      const data = await apiEndpoint.get('game/state');

      if (data && data.jobs) {
        // Sort jobs
        const sortedJobs = [...data.jobs].sort((a, b) => {
          // Put Worker at the bottom
          if (a.name === 'Worker') return 1;
          if (b.name === 'Worker') return -1;
          // Sort others alphabetically by translated name
          const getTranslatedName = (name) => {
            switch(name) {
              case 'Craftsman': return 'Artisan';
              case 'Engineer': return 'Ingénieur';
              case 'Scholar': return 'Érudit';
              case 'Farmer': return 'Fermier';
              case 'Healer': return 'Guérisseur';
              case 'Miner': return 'Mineur';
              case 'Protector': return 'Protecteur';
              case 'Soldier': return 'Soldat';
              default: return name;
            }
          };
          return getTranslatedName(a.name).localeCompare(getTranslatedName(b.name));
        });

        setJobs(sortedJobs);

        // Calculate statistics
        const population = sortedJobs.reduce((sum, job) => sum + job.number, 0);
        const sick = sortedJobs.reduce((sum, job) => sum + job.sick, 0);
        const salary = sortedJobs.reduce((sum, job) => sum + ((job.number - job.free) * job.salary), 0);

        setTotalPopulation(population);
        setTotalSick(sick);
        setTotalSalary(salary);

        return data;
      }
    } catch (error) {
      console.error('Erreur lors de la récupération des données:', error);
    }
    return null;
  };

  // Update jobs and statistics when gameState changes or component mounts
  useEffect(() => {
    if (gameState?.jobs) {
      // Sort jobs
      const sortedJobs = [...gameState.jobs].sort((a, b) => {
        // Put Worker at the bottom
        if (a.name === 'Worker') return 1;
        if (b.name === 'Worker') return -1;
        // Sort others alphabetically by translated name
        const getTranslatedName = (name) => {
          switch(name) {
            case 'Craftsman': return 'Artisan';
            case 'Engineer': return 'Ingénieur';
            case 'Scholar': return 'Érudit';
            case 'Farmer': return 'Fermier';
            case 'Healer': return 'Guérisseur';
            case 'Miner': return 'Mineur';
            case 'Protector': return 'Protecteur';
            case 'Soldier': return 'Soldat';
            default: return name;
          }
        };
        return getTranslatedName(a.name).localeCompare(getTranslatedName(b.name));
      });

      setJobs(sortedJobs);

      // Calculate statistics
      const population = sortedJobs.reduce((sum, job) => sum + job.number, 0);
      const sick = sortedJobs.reduce((sum, job) => sum + job.sick, 0);
      const salary = sortedJobs.reduce((sum, job) => sum + ((job.number - job.free) * job.salary), 0);

      setTotalPopulation(population);
      setTotalSick(sick);
      setTotalSalary(salary);
    }
  }, [gameState]);

  // Liste des métiers spécialistes qui ne peuvent pas être modifiés via les boutons +/-
  const SPECIALIST_JOBS = ['Scholar', 'Healer', 'Engineer', 'Protector', 'Craftsman'];

  // Vérifier si un métier est un spécialiste
  const isSpecialistJob = (jobName) => {
    return SPECIALIST_JOBS.includes(jobName);
  };

  // Vérifier si un métier est Worker
  const isWorkerJob = (jobName) => {
    return jobName === 'Worker';
  };

  // Handle job change
  const handleJobChange = async (jobId, change) => {
    try {
      // Trouver le métier source et destination
      const fromJobId = change > 0 ?
        jobs.find(j => j.name === 'Worker')?.id :
        jobId;

      const toJobId = change > 0 ?
        jobId :
        jobs.find(j => j.name === 'Worker')?.id;

      if (!fromJobId || !toJobId) {
        console.error('Erreur: Impossible de trouver les métiers source ou destination');
        return;
      }

      const changeAmount = Math.abs(change) * (effectiveIsShiftPressed ? 5 : 1);

      // Marquer les jobs comme étant en cours de modification
      setPendingJobChanges(prev => ({
        ...prev,
        [fromJobId]: true,
        [toJobId]: true
      }));

      // Mise à jour optimiste - mettre à jour l'UI immédiatement avant l'appel API
      // pour une expérience utilisateur fluide
      setJobs(prevJobs => {
        return prevJobs.map(job => {
          if (job.id === fromJobId) {
            return { ...job, number: job.number - changeAmount };
          } else if (job.id === toJobId) {
            return { ...job, number: job.number + changeAmount };
          }
          return job;
        });
      });

      // Recalculer la masse salariale immédiatement pour l'UI
      setTotalSalary(prevSalary => {
        const fromJob = jobs.find(j => j.id === fromJobId);
        const toJob = jobs.find(j => j.id === toJobId);

        if (fromJob && toJob) {
          const fromJobSalaryChange = -changeAmount * (fromJob.salary || 0);
          const toJobSalaryChange = changeAmount * (toJob.salary || 0);
          return prevSalary + fromJobSalaryChange + toJobSalaryChange;
        }

        return prevSalary;
      });

      // Appeler l'action Redux pour transférer le métier
      try {
        // Dispatch l'action transferJob
        const resultAction = await dispatch(transferJob({
          fromJobId,
          toJobId,
          count: changeAmount
        })).unwrap();

        // Mise à jour réussie, mais préserver l'ordre des jobs
        // Nous allons mettre à jour les valeurs tout en conservant l'ordre original
        setJobs(prevJobs => {
          const updatedJobs = [...prevJobs];
          // Mettre à jour chaque job avec les nouvelles valeurs
          for (const job of resultAction) {
            const index = updatedJobs.findIndex(j => j.id === job.id);
            if (index !== -1) {
              updatedJobs[index] = { ...updatedJobs[index], ...job };
            }
          }
          return updatedJobs;
        });

        // Recalculer la masse salariale
        const updatedSalary = resultAction.reduce((sum, job) => {
          return sum + ((job.number - job.free) * job.salary);
        }, 0);

        setTotalSalary(updatedSalary);

        console.log('Transfert de métier réussi');

        // Réinitialiser l'état des jobs en cours de modification
        setPendingJobChanges({});

        // Appeler le callback onJobChange si fourni
        if (onJobChange) {
          onJobChange();
        }
      } catch (error) {
        // En cas d'échec, annuler les modifications locales
        console.error('Erreur lors du transfert de métier:', error);
        alert(error.message || 'Une erreur est survenue lors du transfert de métier');

        // Restaurer l'état précédent
        setJobs(prevJobs => {
          return prevJobs.map(job => {
            if (job.id === fromJobId) {
              return { ...job, number: job.number + changeAmount };
            } else if (job.id === toJobId) {
              return { ...job, number: job.number - changeAmount };
            }
            return job;
          });
        });

        // Recalculer la masse salariale
        setTotalSalary(prevSalary => {
          const fromJob = jobs.find(j => j.id === fromJobId);
          const toJob = jobs.find(j => j.id === toJobId);

          if (fromJob && toJob) {
            const fromJobSalaryChange = changeAmount * (fromJob.salary || 0);
            const toJobSalaryChange = -changeAmount * (toJob.salary || 0);
            return prevSalary + fromJobSalaryChange + toJobSalaryChange;
          }

          return prevSalary;
        });

        // Réinitialiser l'état des jobs en cours de modification
        setPendingJobChanges({});
      }
    } catch (error) {
      console.error('Erreur lors du transfert de métier:', error);
      alert('Une erreur est survenue lors du transfert de métier. Veuillez réessayer.');
    }
  };

  // Handle free workers change
  const handleFreeChange = async (jobId, change) => {
    const changeAmount = effectiveIsShiftPressed ? change * 5 : change;

    // Trouver le job concerné
    const job = jobs.find(j => j.id === jobId);
    if (!job) {
      console.error('Erreur: Impossible de trouver le métier avec l\'ID', jobId);
      return;
    }

    // Calculer la nouvelle valeur de free, en s'assurant qu'elle reste dans les limites valides
    const newFree = Math.max(0, Math.min(job.number, job.free + changeAmount));
    const actualChange = newFree - job.free; // Le changement réel peut être différent de changeAmount

    // Nous avons supprimé la gestion de pendingFreeChanges

    // Mise à jour optimiste - mettre à jour l'UI immédiatement avant l'appel API
    setJobs(prevJobs => {
      return prevJobs.map(j => {
        if (j.id === jobId) {
          return { ...j, free: newFree };
        }
        return j;
      });
    });

    // Recalculer la masse salariale immédiatement pour l'UI
    setTotalSalary(prevSalary => {
      // Calculer le changement de salaire
      const salaryChange = -actualChange * (job.salary || 0);
      return prevSalary + salaryChange;
    });

    try {
      // Dispatch l'action changeFreeWorkers
      const resultAction = await dispatch(changeFreeWorkers({
        jobId,
        change: actualChange
      })).unwrap();

      // Mise à jour réussie, mais préserver l'ordre des jobs
      // Nous allons mettre à jour les valeurs tout en conservant l'ordre original
      setJobs(prevJobs => {
        const updatedJobs = [...prevJobs];
        // Mettre à jour chaque job avec les nouvelles valeurs
        for (const job of resultAction) {
          const index = updatedJobs.findIndex(j => j.id === job.id);
          if (index !== -1) {
            updatedJobs[index] = { ...updatedJobs[index], ...job };
          }
        }
        return updatedJobs;
      });

      // Recalculer la masse salariale
      const updatedSalary = resultAction.reduce((sum, job) => {
        return sum + ((job.number - job.free) * job.salary);
      }, 0);

      setTotalSalary(updatedSalary);

      console.log('Modification des employés gratuits réussie');

      // Appeler la fonction onFreeChange fournie par le parent
      if (onFreeChange) {
        onFreeChange(jobId, actualChange);
      }
    } catch (error) {
      console.error('Erreur lors de la modification des employés gratuits:', error);

      // Restaurer l'état précédent en cas d'erreur
      setJobs(prevJobs => {
        return prevJobs.map(j => {
          if (j.id === jobId) {
            return { ...j, free: job.free }; // Restaurer la valeur originale
          }
          return j;
        });
      });

      // Restaurer la masse salariale
      setTotalSalary(prevSalary => {
        const salaryChange = actualChange * (job.salary || 0); // Inverser le changement
        return prevSalary + salaryChange;
      });

      alert('Une erreur est survenue lors de la modification des employés gratuits. Veuillez réessayer.');
    }
  };

  // Get job icon
  const getJobIcon = (jobName) => {
    switch(jobName) {
      case 'Scholar': return '🧠';
      case 'Healer': return '⚕️';
      case 'Worker': return '👷';
      case 'Miner': return '⛏️';
      case 'Protector': return '🛡️';
      case 'Craftsman': return '🔨';
      case 'Farmer': return '🌾';
      case 'Engineer': return '🔧';
      case 'Soldier': return '⚔️';
      default: return '👤';
    }
  };

  // Get translated job name
  const getTranslatedJobName = (jobName) => {
    switch(jobName) {
      case 'Scholar': return 'Érudit';
      case 'Healer': return 'Guérisseur';
      case 'Worker': return 'Ouvrier';
      case 'Miner': return 'Mineur';
      case 'Protector': return 'Protecteur';
      case 'Craftsman': return 'Artisan';
      case 'Farmer': return 'Fermier';
      case 'Engineer': return 'Ingénieur';
      case 'Soldier': return 'Soldat';
      default: return jobName;
    }
  };

  // Styles inline pour s'assurer qu'ils sont appliqués
  const summaryLabelStyle = {
    fontSize: '16px',
    color: '#666',
    marginBottom: '5px'
  };

  const summaryValueStyle = {
    fontSize: '24px',
    fontWeight: 'bold',
    color: '#333'
  };

  const tableCellStyle = {
    fontSize: '16px'
  };

  return (
    <div className="summary-view">
      <div className="jobs-summary">
        <div className="jobs-summary-item">
          <div className="jobs-summary-label" style={summaryLabelStyle}>Masse salariale:</div>
          <div className="jobs-summary-value" style={summaryValueStyle}>
            {totalSalary} or/cycle
          </div>
        </div>
      </div>

      <div className="jobs-management">
        <div className="jobs-table-container">
          <table className="jobs-table">
            <thead>
              <tr>
                <th style={{ ...tableCellStyle, textAlign: 'left' }}>Métier</th>
                <th style={{ ...tableCellStyle, textAlign: 'left' }}>Nombre</th>
                <th style={{ ...tableCellStyle, textAlign: 'left' }}>Malades</th>
                <th style={{ ...tableCellStyle, textAlign: 'center' }}>Gratuits</th>
                <th style={{ ...tableCellStyle, textAlign: 'left' }}>Salaire</th>
                <th style={{ ...tableCellStyle, textAlign: 'left' }}>Total</th>
                <th style={{ ...tableCellStyle, textAlign: 'center' }}>Actions</th>
              </tr>
            </thead>
            <tbody>
              {jobs.map(job => (
                <tr key={job.id} className={job.name === 'Worker' ? 'worker-row' : ''}>
                  <td style={tableCellStyle}>
                    <div className="job-name">
                      {getJobIcon(job.name)} {getTranslatedJobName(job.name)}
                    </div>
                  </td>
                  <td style={tableCellStyle}>{job.number}</td>
                  <td style={tableCellStyle}>{job.sick}</td>
                  <td style={{ ...tableCellStyle, textAlign: 'center' }}>
                    {job.free}
                  </td>
                  <td style={tableCellStyle}>{job.salary}</td>
                  <td style={tableCellStyle}>{(job.number - job.free) * job.salary}</td>
                  <td style={{ textAlign: 'center' }}>
                    <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                      {isWorkerJob(job.name) ? (
                        <div className="worker-info" title="Les ouvriers sont la réserve de main-d'œuvre">
                          Réserve
                        </div>
                      ) : (
                        <>
                          <button
                            className={`job-action-btn decrease ${effectiveIsShiftPressed ? 'shift' : ''} ${pendingJobChanges[job.id] ? 'pending' : ''}`}
                            onClick={() => handleJobChange(job.id, -1)}
                            disabled={
                              isSpecialistJob(job.name) ||
                              pendingJobChanges[job.id] ||
                              effectiveIsShiftPressed
                                ? (job.number < 5 || job.number - 5 < job.free)
                                : (job.number <= 0 || job.number <= job.free)
                            }
                            style={{ marginRight: '2px' }}
                            title={
                              isSpecialistJob(job.name)
                                ? "Les spécialistes ne peuvent pas être réaffectés via cette méthode"
                                : pendingJobChanges[job.id]
                                  ? "Modification en cours..."
                                  : "Transformer en ouvrier"
                            }
                          >
                            {pendingJobChanges[job.id] ? '...' : effectiveIsShiftPressed ? '-5' : '-'}
                          </button>
                          <button
                            className={`job-action-btn increase ${effectiveIsShiftPressed ? 'shift' : ''} ${pendingJobChanges[job.id] ? 'pending' : ''}`}
                            onClick={() => handleJobChange(job.id, 1)}
                            disabled={
                              isSpecialistJob(job.name) ||
                              pendingJobChanges[job.id] ||
                              (effectiveIsShiftPressed
                                ? jobs.find(j => j.name === 'Worker')?.number < 5
                                : jobs.find(j => j.name === 'Worker')?.number <= 0)
                            }
                            title={
                              isSpecialistJob(job.name)
                                ? "Les spécialistes ne peuvent pas être ajoutés via cette méthode"
                                : pendingJobChanges[job.id]
                                  ? "Modification en cours..."
                                  : "Transformer un ouvrier"
                            }
                          >
                            {pendingJobChanges[job.id] ? '...' : effectiveIsShiftPressed ? '+5' : '+'}
                          </button>
                        </>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        <div className="jobs-info">
          <div className="jobs-info-card">
            <h3>Information</h3>
            <p>Les ouvriers servent de réserve pour les autres métiers. Lorsque vous augmentez le nombre d'un métier, un ouvrier est converti vers ce métier, et vice versa.</p>
            <p>Les employés "gratuits" ne reçoivent pas de salaire.</p>
            <p>Maintenez la touche <strong>Shift</strong> pour augmenter/diminuer par 5.</p>
            <p className="important-note">Note: Les spécialistes (Érudit, Guérisseur, Ingénieur, Protecteur, Artisan) ne peuvent pas être modifiés via les boutons +/-. Utilisez la vue détaillée pour modifier ces métiers.</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SummaryView;
