import React, { useState, useEffect } from 'react';
import './InhabitantEditModal.css';

const InhabitantEditModal = ({
  inhabitant,
  onClose,
  onSave,
  onDelete,
  gameState
}) => {
  const [formData, setFormData] = useState({
    first_name: '',
    last_name: '',
    race: '',
    gender: '',
    job_id: '',
    is_pc: 0,
    is_sick: 0,
    short_description: '',
    history: '',
    age: 0
  });

  const [confirmDelete, setConfirmDelete] = useState(false);

  // Initialize form data when inhabitant changes
  useEffect(() => {
    if (inhabitant) {
      setFormData({
        first_name: inhabitant.first_name || '',
        last_name: inhabitant.last_name || '',
        race: inhabitant.race || '',
        gender: inhabitant.gender || '',
        job_id: inhabitant.job_id || '',
        is_pc: inhabitant.is_pc || 0,
        is_sick: inhabitant.is_sick || 0,
        short_description: inhabitant.short_description || '',
        history: inhabitant.history || '',
        age: inhabitant.age || 0
      });
    }
  }, [inhabitant]);

  // Handle form input changes
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;

    // Traitement spécial pour job_id
    if (name === 'job_id') {
      console.log('InhabitantEditModal: Changing job_id to', value, 'type:', typeof value);

      // Convertir en nombre si ce n'est pas une chaîne vide
      const newValue = value === '' ? '' : parseInt(value, 10);
      console.log('InhabitantEditModal: Converted job_id to', newValue, 'type:', typeof newValue);

      setFormData(prev => ({
        ...prev,
        [name]: newValue
      }));
    } else {
      // Traitement normal pour les autres champs
      setFormData(prev => ({
        ...prev,
        [name]: type === 'checkbox' ? (checked ? 1 : 0) : value
      }));
    }
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();

    // Log the form data and the original inhabitant for debugging
    console.log('InhabitantEditModal: Submitting form with data:', formData);
    console.log('InhabitantEditModal: Original inhabitant:', inhabitant);

    // Vérifier si le job_id a changé
    const oldJobId = inhabitant.job_id !== undefined && inhabitant.job_id !== null ?
                    parseInt(inhabitant.job_id, 10) : null;
    const newJobId = formData.job_id !== undefined && formData.job_id !== null && formData.job_id !== '' ?
                    parseInt(formData.job_id, 10) : null;

    console.log('InhabitantEditModal: Job comparison:', {
      oldJobId,
      oldJobIdType: typeof oldJobId,
      newJobId,
      newJobIdType: typeof newJobId,
      isChanged: oldJobId !== newJobId
    });

    // Create a copy of the form data to ensure we're not modifying the state directly
    const dataToSave = { ...formData };

    // Ensure job_id is a number (if it's not already)
    if (dataToSave.job_id !== undefined && dataToSave.job_id !== null && dataToSave.job_id !== '') {
      dataToSave.job_id = parseInt(dataToSave.job_id, 10);
    }

    console.log('InhabitantEditModal: Data to save:', dataToSave);

    // Afficher les métiers disponibles pour le débogage
    console.log('InhabitantEditModal: Available jobs:', gameState?.jobs);

    // Save the data
    onSave(dataToSave);
  };

  // Handle delete button click
  const handleDeleteClick = () => {
    if (confirmDelete) {
      onDelete(inhabitant.id);
    } else {
      setConfirmDelete(true);
    }
  };

  // Reset delete confirmation when closing
  useEffect(() => {
    return () => setConfirmDelete(false);
  }, []); // Pas besoin de dépendance ici car c'est un effet de nettoyage

  return (
    <div className="inhabitant-edit-modal" onClick={onClose}>
      <div className="inhabitant-edit-content" onClick={(e) => e.stopPropagation()}>
        <span className="close" onClick={onClose}>&times;</span>

        <h2>Modifier l'habitant</h2>

        <form onSubmit={handleSubmit}>
          <div className="form-row">
            <div className="form-group">
              <label htmlFor="first_name">Prénom</label>
              <input
                type="text"
                id="first_name"
                name="first_name"
                value={formData.first_name}
                onChange={handleChange}
                required
              />
            </div>

            <div className="form-group">
              <label htmlFor="last_name">Nom</label>
              <input
                type="text"
                id="last_name"
                name="last_name"
                value={formData.last_name}
                onChange={handleChange}
                required
              />
            </div>
          </div>

          <div className="form-row">
            <div className="form-group">
              <label htmlFor="race">Race</label>
              <select
                id="race"
                name="race"
                value={formData.race}
                onChange={handleChange}
                required
              >
                <option value="">Sélectionner une race</option>
                <option value="Dwarf">Nain</option>
                <option value="Human">Humain</option>
                <option value="Elf">Elfe</option>
                <option value="Halfling">Halfelin</option>
                <option value="Gnome">Gnome</option>
              </select>
            </div>

            <div className="form-group">
              <label htmlFor="gender">Genre</label>
              <select
                id="gender"
                name="gender"
                value={formData.gender}
                onChange={handleChange}
                required
              >
                <option value="">Sélectionner un genre</option>
                <option value="Male">Homme</option>
                <option value="Female">Femme</option>
              </select>
            </div>
          </div>

          <div className="form-row">
            <div className="form-group">
              <label htmlFor="job_id">Métier</label>
              <select
                id="job_id"
                name="job_id"
                value={formData.job_id}
                onChange={handleChange}
                required
              >
                <option value="">Sélectionner un métier</option>
                {gameState?.jobs?.map(job => (
                  <option key={job.id} value={job.id}>{job.name}</option>
                ))}
              </select>
            </div>

            <div className="form-group">
              <label htmlFor="age">Âge</label>
              <input
                type="number"
                id="age"
                name="age"
                value={formData.age}
                onChange={handleChange}
                min="1"
                max="200"
                required
              />
            </div>
          </div>

          <div className="form-row checkbox-row">
            <div className="form-group checkbox-group">
              <label>
                <input
                  type="checkbox"
                  name="is_pc"
                  checked={formData.is_pc === 1}
                  onChange={handleChange}
                />
                Personnage Joueur (PC)
              </label>
            </div>

            <div className="form-group checkbox-group">
              <label>
                <input
                  type="checkbox"
                  name="is_sick"
                  checked={formData.is_sick === 1}
                  onChange={handleChange}
                />
                Malade
              </label>
            </div>
          </div>

          <div className="form-group">
            <label htmlFor="short_description">Description</label>
            <textarea
              id="short_description"
              name="short_description"
              value={formData.short_description}
              onChange={handleChange}
              rows="3"
            ></textarea>
          </div>

          <div className="form-group">
            <label htmlFor="history">Historique</label>
            <textarea
              id="history"
              name="history"
              value={formData.history}
              onChange={handleChange}
              rows="5"
            ></textarea>
          </div>

          <div className="form-actions">
            <button
              type="button"
              className={`delete-button ${confirmDelete ? 'confirm' : ''}`}
              onClick={handleDeleteClick}
            >
              {confirmDelete ? 'Confirmer la suppression' : 'Supprimer'}
            </button>
            <button type="submit" className="save-button">Enregistrer</button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default InhabitantEditModal;
