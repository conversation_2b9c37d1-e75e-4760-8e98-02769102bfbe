import React, { useState, useEffect } from 'react';
import './PopulationStats.css';
import CreateInhabitantButton from './CreateInhabitantButton';
import InhabitantEditModal from './InhabitantEditModal';

const PopulationStats = ({ stats, gameState, onInhabitantCreated }) => {
  const [showModal, setShowModal] = useState(false);
  const [displayStats, setDisplayStats] = useState({
    total: 0,
    pc: 0,
    npc: 0,
    sick: 0
  });

  // Update display stats when stats prop changes
  useEffect(() => {
    if (stats) {
      setDisplayStats({
        total: stats.total || 0,
        pc: stats.pc || 0,
        npc: stats.npc || 0,
        sick: stats.sick || 0
      });
    }
  }, [stats]);

  if (!stats) return null;

  // Styles inline pour s'assurer qu'ils sont appliqués
  const statValueStyle = {
    fontSize: '28px',
    fontWeight: 'bold',
    marginBottom: '5px'
  };

  const statLabelStyle = {
    color: '#666',
    fontSize: '16px'
  };

  return (
    <div className="population-stats">
      <div className="stat-card">
        <div className="stat-value" style={statValueStyle}>{displayStats.total}</div>
        <div className="stat-label" style={statLabelStyle}>Habitants</div>
      </div>

      <div className="stat-card">
        <div className="stat-value" style={statValueStyle}>{displayStats.pc}</div>
        <div className="stat-label" style={statLabelStyle}>PC</div>
      </div>

      <div className="stat-card">
        <div className="stat-value" style={statValueStyle}>{displayStats.npc}</div>
        <div className="stat-label" style={statLabelStyle}>NPC</div>
      </div>

      <div className="stat-card">
        <div className="stat-value" style={statValueStyle}>{displayStats.sick}</div>
        <div className="stat-label" style={statLabelStyle}>Malades</div>
      </div>

      <div className="stat-card">
        <CreateInhabitantButton
          onClick={() => setShowModal(true)}
        />
      </div>

      {showModal && (
        <InhabitantEditModal
          inhabitant={{
            first_name: '',
            last_name: '',
            race: 'Human',
            gender: 'Male',
            job_id: gameState?.jobs?.find(job => job.name === 'Worker')?.id || '',
            is_pc: 0,
            is_sick: 0,
            short_description: '',
            history: '',
            age: 30
          }}
          onSave={(formData) => {
            if (onInhabitantCreated) {
              onInhabitantCreated(formData);
            }
            setShowModal(false);
          }}
          onCancel={() => setShowModal(false)}
          availableJobs={gameState?.jobs || []}
        />
      )}
    </div>
  );
};

export default PopulationStats;
