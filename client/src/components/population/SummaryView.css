.summary-view {
  width: 100%;
}

.jobs-summary {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  background-color: white;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.jobs-summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  padding: 10px;
  border-right: 1px solid #eee;
}

.jobs-summary-item:last-child {
  border-right: none;
}

.jobs-summary-label {
  font-size: 16px !important;
  color: #666 !important;
  margin-bottom: 5px !important;
}

.jobs-summary-value {
  font-size: 24px !important;
  font-weight: bold !important;
  color: #333 !important;
}

.jobs-management {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.jobs-table-container {
  overflow-x: auto;
}

.jobs-table {
  width: 100%;
  border-collapse: collapse;
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.jobs-table th {
  background-color: #f8f8f8 !important;
  padding: 12px 10px !important;
  text-align: left !important;
  border-bottom: 2px solid #eee !important;
  color: #333 !important;
  font-size: 16px !important;
}

.jobs-table td {
  padding: 10px !important;
  border-bottom: 1px solid #eee !important;
  font-size: 16px !important;
}

.jobs-table tr:hover {
  background-color: #f5f9ff;
}

.worker-row {
  background-color: #f5f5f5;
}

.worker-row:hover {
  background-color: #e8e8e8;
}

.worker-info {
  padding: 5px 10px;
  background-color: #e0e0e0;
  border-radius: 4px;
  color: #555;
  font-size: 0.9em;
  font-style: italic;
}

.job-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.job-action-btn {
  width: 30px;
  height: 30px;
  border: 1px solid #ccc;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  transition: all 0.2s;
  color: white;
}

.job-action-btn.increase {
  background-color: #4CAF50;
}

.job-action-btn.decrease {
  background-color: #F44336;
}

.job-action-btn:hover {
  opacity: 0.9;
}

.job-action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: #cccccc;
  color: white !important;
}

.job-action-btn:disabled:hover {
  background-color: #cccccc;
  opacity: 0.5;
}

.job-action-btn.shift {
  font-size: 12px;
}

.job-action-btn.pending {
  background-color: #9e9e9e;
  cursor: wait;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% { opacity: 0.6; }
  50% { opacity: 1; }
  100% { opacity: 0.6; }
}

.jobs-info {
  margin-top: 20px;
}

.jobs-info-card {
  background-color: white;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.jobs-info-card h3 {
  margin-top: 0;
  margin-bottom: 10px;
  border-bottom: 1px solid #eee;
  padding-bottom: 5px;
}

.jobs-info-card p {
  margin: 10px 0;
  line-height: 1.5;
}

.important-note {
  background-color: #fff3cd;
  border-left: 4px solid #ffc107;
  padding: 10px;
  margin: 15px 0 !important;
  border-radius: 4px;
  color: #856404;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .jobs-summary {
    flex-direction: column;
  }

  .jobs-summary-item {
    border-right: none;
    border-bottom: 1px solid #eee;
    padding: 10px 0;
  }

  .jobs-summary-item:last-child {
    border-bottom: none;
  }
}
