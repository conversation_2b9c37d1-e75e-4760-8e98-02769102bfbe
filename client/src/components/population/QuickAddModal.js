import React, { useState, useEffect } from 'react';
import './QuickAddModal.css';
import config from '../../config';
import apiEndpoint from '../../services/ApiEndpoint';

const QuickAddModal = ({ onClose, onSave, gameState }) => {
  const [step, setStep] = useState(1); // 1: Initial form, 2: Job selection
  const [formData, setFormData] = useState({
    count: 1,
    race: 'Human'
  });
  const [jobSelections, setJobSelections] = useState([]);

  // Get worker job ID
  const workerJobId = gameState?.jobs?.find(job => job.name === 'Worker')?.id || '';

  // Update job selections when count changes
  useEffect(() => {
    const count = parseInt(formData.count) || 0;
    setJobSelections(Array(count).fill(workerJobId));
  }, [formData.count, workerJobId]);

  // Handle form input changes
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle job selection changes
  const handleJobChange = (index, jobId) => {
    const newSelections = [...jobSelections];
    newSelections[index] = jobId;
    setJobSelections(newSelections);
  };

  // Handle form submission for step 1
  const handleStep1Submit = (e) => {
    e.preventDefault();
    setStep(2);
  };

  // Handle form submission for step 2
  const handleStep2Submit = async (e) => {
    e.preventDefault();

    try {
      const inhabitants = [];

      // Create each inhabitant
      for (let i = 0; i < jobSelections.length; i++) {
        try {
          const data = await apiEndpoint.post('inhabitants', {
            race: formData.race,
            gender: Math.random() > 0.5 ? 'Male' : 'Female', // Random gender
            job_id: jobSelections[i],
            is_pc: 0,
            is_sick: 0,
            age: Math.floor(Math.random() * (100 - 20 + 1)) + 20 // Random age between 20-100
          });

          inhabitants.push(data.inhabitant);
        } catch (error) {
          console.error('Error creating inhabitant:', error);
        }
      }

      onClose();
      if (onSave) {
        onSave(inhabitants);
      }
    } catch (error) {
      console.error('Error creating inhabitants:', error);
    }
  };

  return (
    <div className="quick-add-modal" onClick={onClose}>
      <div className="quick-add-content" onClick={(e) => e.stopPropagation()}>
        <span className="close" onClick={onClose}>&times;</span>

        <h2>Ajout rapide d'habitants</h2>

        {step === 1 ? (
          <form onSubmit={handleStep1Submit}>
            <div className="form-group">
              <label htmlFor="count">Nombre d'habitants à ajouter</label>
              <input
                type="number"
                id="count"
                name="count"
                value={formData.count}
                onChange={handleChange}
                min="1"
                max="50"
                required
              />
            </div>

            <div className="form-group">
              <label htmlFor="race">Race</label>
              <select
                id="race"
                name="race"
                value={formData.race}
                onChange={handleChange}
                required
              >
                <option value="Dwarf">Nain</option>
                <option value="Human">Humain</option>
                <option value="Elf">Elfe</option>
                <option value="Halfling">Halfelin</option>
                <option value="Gnome">Gnome</option>
              </select>
            </div>

            <div className="form-actions">
              <button type="button" className="cancel-button" onClick={onClose}>Annuler</button>
              <button type="submit" className="next-button">Suivant</button>
            </div>
          </form>
        ) : (
          <form onSubmit={handleStep2Submit}>
            <div className="job-selection-container">
              <h3>Sélection des métiers</h3>
              <p>Sélectionnez un métier pour chaque nouvel habitant :</p>

              <div className="job-selection-list">
                {jobSelections.map((jobId, index) => (
                  <div key={index} className="job-selection-item">
                    <label htmlFor={`job-${index}`}>Habitant {index + 1}</label>
                    <select
                      id={`job-${index}`}
                      value={jobId}
                      onChange={(e) => handleJobChange(index, e.target.value)}
                      required
                    >
                      {gameState?.jobs?.map(job => (
                        <option key={job.id} value={job.id}>{job.name}</option>
                      ))}
                    </select>
                  </div>
                ))}
              </div>
            </div>

            <div className="form-actions">
              <button type="button" className="back-button" onClick={() => setStep(1)}>Retour</button>
              <button type="submit" className="save-button">Créer les habitants</button>
            </div>
          </form>
        )}
      </div>
    </div>
  );
};

export default QuickAddModal;
