.justice-defense-tab {
  padding: 0;
}

.justice-defense-tab h2 {
  margin-bottom: 20px;
  color: #333;
}

.justice-defense-stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.justice-defense-stat-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.justice-defense-stat-icon {
  font-size: 2rem;
  margin-bottom: 10px;
}

.justice-defense-stat-title {
  font-weight: bold;
  margin-bottom: 5px;
  color: #333;
}

.justice-defense-stat-value {
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 5px;
}

.justice-defense-stat-description {
  font-size: 0.9rem;
  color: #666;
}

.justice-defense-stat-value.positive {
  color: #28a745;
}

.justice-defense-stat-value.negative {
  color: #dc3545;
}

.justice-defense-info-card {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 30px;
}

.justice-defense-info-card h3 {
  margin-bottom: 15px;
  color: #333;
}

.justice-defense-info-card p {
  margin-bottom: 10px;
  line-height: 1.5;
}

.modifiers-tables {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 30px;
}

.defense-means-section {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 30px;
}

.defense-means-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.defense-means-header h3 {
  margin: 0;
}

.add-defense-button {
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  cursor: pointer;
  font-weight: bold;
}

.add-defense-button:hover {
  background-color: #0069d9;
}

.add-defense-form {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 8px;
  border: 1px solid #ced4da;
  border-radius: 4px;
}

.form-group textarea {
  height: 100px;
  resize: vertical;
}

.submit-button {
  background-color: #28a745;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  cursor: pointer;
  font-weight: bold;
}

.submit-button:hover {
  background-color: #218838;
}

.defense-means-table {
  width: 100%;
  border-collapse: collapse;
}

.defense-means-table th,
.defense-means-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #dee2e6;
}

.defense-means-table th {
  background-color: #f8f9fa;
  font-weight: bold;
}

.defense-means-table .no-data {
  text-align: center;
  color: #6c757d;
  padding: 20px;
}

.delete-button {
  background-color: #dc3545;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  cursor: pointer;
}

.delete-button:hover {
  background-color: #c82333;
}



@media (max-width: 768px) {
  .justice-defense-stats-cards {
    grid-template-columns: 1fr;
  }

  .modifiers-tables {
    grid-template-columns: 1fr;
  }
}
