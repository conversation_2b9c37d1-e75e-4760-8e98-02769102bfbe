import React from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { setActiveTab } from '../store/slices/uiSlice';

/**
 * Composant de gestion des onglets utilisant Redux
 * Ce composant peut être utilisé progressivement dans l'application
 * sans perturber le fonctionnement existant
 */
const TabManager = ({ children }) => {
  const activeTab = useSelector(state => state.ui.activeTab);
  const dispatch = useDispatch();

  // Fonction pour changer d'onglet
  const handleTabChange = (tabId) => {
    dispatch(setActiveTab(tabId));
  };

  return (
    <div className="tab-manager">
      {React.Children.map(children, child => {
        // Cloner l'enfant et lui passer les props nécessaires
        return React.cloneElement(child, {
          isActive: child.props.tabId === activeTab,
          onTabChange: handleTabChange
        });
      })}
    </div>
  );
};

export default TabManager;
