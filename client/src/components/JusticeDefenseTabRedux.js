import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import ModifiersTable from './ModifiersTable';
import './JusticeDefenseTab.css';
import {
  fetchJusticeDefenseModifiers,
  updateJusticeDefenseStats,
  addDefenseMean,
  deleteDefenseMean,
  selectJusticeModifiers,
  selectArmyModifiers,
  selectDefenseMeans,
  selectJusticeDefenseStats,
  selectJusticeDefenseLoading,
  selectJusticeDefenseError
} from '../store/slices/justiceDefenseSlice';
import { withRedux } from '../hoc/withRedux';

function JusticeDefenseTabRedux({ gameState, updateJusticeDefenseData }) {
  const dispatch = useDispatch();
  const [newDefenseMean, setNewDefenseMean] = useState({ title: '', description: '' });
  const [showAddDefenseForm, setShowAddDefenseForm] = useState(false);

  // Sélectionner les données depuis le store Redux
  const justiceModifiers = useSelector(selectJusticeModifiers);
  const armyModifiers = useSelector(selectArmyModifiers);
  const defenseMeans = useSelector(selectDefenseMeans);
  const stats = useSelector(selectJusticeDefenseStats);
  const loading = useSelector(selectJusticeDefenseLoading);
  const error = useSelector(selectJusticeDefenseError);

  // Table IDs (identiques à l'original)
  const justiceTableId = 19;
  const armyTableId = 20;

  // Charger les modificateurs au montage du composant
  useEffect(() => {
    dispatch(fetchJusticeDefenseModifiers())
      .then(() => {
        // Mettre à jour les statistiques après avoir récupéré les modificateurs
        if (gameState) {
          dispatch(updateJusticeDefenseStats({ gameState }));
        }
      });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Mettre à jour les statistiques de justice et défense quand gameState change
  useEffect(() => {
    if (gameState) {
      dispatch(updateJusticeDefenseStats({ gameState }));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [gameState]);

  // Fonction pour recharger les modificateurs (utilisée par les tables de modificateurs)
  const handleModifierUpdated = () => {
    dispatch(fetchJusticeDefenseModifiers())
      .then(() => {
        // Mettre à jour les statistiques après avoir récupéré les modificateurs
        if (gameState) {
          dispatch(updateJusticeDefenseStats({ gameState }));
        }

        if (updateJusticeDefenseData) {
          updateJusticeDefenseData();
        }
      });
  };

  // Format number with 1 decimal place
  const formatNumber = (value) => {
    if (value === undefined || value === null) return '0.0';
    return value.toFixed(1);
  };

  // Format percentage value
  const formatPercentValue = (value) => {
    if (value === undefined || value === null) return '+0.0%';
    return (value >= 0 ? '+' : '') + (value * 100).toFixed(1) + '%';
  };

  // Get emoji for crime probability
  const getCrimeEmoji = (probability) => {
    if (probability >= 0.1) return '🔴';
    if (probability >= 0.05) return '🟠';
    if (probability >= 0.02) return '🟡';
    return '🟢';
  };

  // Get emoji for army strength
  const getArmyEmoji = (strength) => {
    if (strength >= 10) return '🛡️';
    if (strength >= 5) return '⚔️';
    if (strength >= 2) return '🗡️';
    return '🔪';
  };

  // Handle adding a new defense mean
  const handleAddDefenseMean = async (e) => {
    e.preventDefault();

    if (!newDefenseMean.title.trim()) {
      alert('Veuillez remplir tous les champs obligatoires');
      return;
    }

    try {
      await dispatch(addDefenseMean(newDefenseMean)).unwrap();

      // Reset form and reload data
      setNewDefenseMean({ title: '', description: '' });
      setShowAddDefenseForm(false);

      // Recharger les modificateurs et mettre à jour les statistiques
      dispatch(fetchJusticeDefenseModifiers())
        .then(() => {
          if (gameState) {
            dispatch(updateJusticeDefenseStats({ gameState }));
          }

          if (updateJusticeDefenseData) {
            updateJusticeDefenseData();
          }
        });
    } catch (err) {
      alert('Erreur lors de l\'ajout du moyen de défense: ' + err.message);
    }
  };

  // Handle deleting a defense mean
  const handleDeleteDefenseMean = async (id) => {
    if (!window.confirm('Êtes-vous sûr de vouloir supprimer ce moyen de défense ?')) {
      return;
    }

    try {
      await dispatch(deleteDefenseMean(id)).unwrap();

      // Recharger les modificateurs et mettre à jour les statistiques
      dispatch(fetchJusticeDefenseModifiers())
        .then(() => {
          if (gameState) {
            dispatch(updateJusticeDefenseStats({ gameState }));
          }

          if (updateJusticeDefenseData) {
            updateJusticeDefenseData();
          }
        });
    } catch (err) {
      alert('Erreur lors de la suppression du moyen de défense: ' + err.message);
    }
  };

  if (loading && !stats) {
    return <div className="loading">Chargement des données de justice et défense...</div>;
  }

  if (error) {
    return <div className="error">Erreur: {error}</div>;
  }

  // Calculate active protectors and soldiers
  const activeProtectors = stats.protectors - stats.protectors_sick;
  const activeSoldiers = stats.soldiers - stats.soldiers_sick;

  return (
    <div className="justice-defense-tab">
      <h2>Justice et Défense</h2>

      {/* Justice and Defense Stats Cards */}
      <div className="justice-defense-stats-cards">
        <div className="justice-defense-stat-card">
          <div className="justice-defense-stat-icon">👮</div>
          <div className="justice-defense-stat-title">Protecteurs actifs</div>
          <div className="justice-defense-stat-value">{activeProtectors} protecteurs</div>
          <div className="justice-defense-stat-description">Nombre de protecteurs au travail</div>
        </div>

        <div className="justice-defense-stat-card">
          <div className="justice-defense-stat-icon">⚔️</div>
          <div className="justice-defense-stat-title">Soldats actifs</div>
          <div className="justice-defense-stat-value">{activeSoldiers} soldats</div>
          <div className="justice-defense-stat-description">Nombre de soldats au travail</div>
        </div>

        <div className="justice-defense-stat-card">
          <div className="justice-defense-stat-icon">📊</div>
          <div className="justice-defense-stat-title">Effet des modificateurs de justice</div>
          <div className={`justice-defense-stat-value ${stats.justiceModifier >= 0 ? 'positive' : 'negative'}`}>
            {formatPercentValue(stats.justiceModifier)}
          </div>
          <div className="justice-defense-stat-description">Impact des modificateurs sur la justice</div>
        </div>

        <div className="justice-defense-stat-card">
          <div className="justice-defense-stat-icon">🛡️</div>
          <div className="justice-defense-stat-title">Effet des modificateurs de défense</div>
          <div className={`justice-defense-stat-value ${stats.armyModifier >= 0 ? 'positive' : 'negative'}`}>
            {formatPercentValue(stats.armyModifier)}
          </div>
          <div className="justice-defense-stat-description">Impact des modificateurs sur l'armée</div>
        </div>

        <div className="justice-defense-stat-card">
          <div className="justice-defense-stat-icon">{getCrimeEmoji(stats.crimeProbability)}</div>
          <div className="justice-defense-stat-title">Probabilité de crime</div>
          <div className={`justice-defense-stat-value ${stats.crimeProbability < 0.05 ? 'positive' : 'negative'}`}>
            {(stats.crimeProbability * 100).toFixed(1)}%
          </div>
          <div className="justice-defense-stat-description">Chance qu'un crime se produise</div>
        </div>

        <div className="justice-defense-stat-card">
          <div className="justice-defense-stat-icon">{getArmyEmoji(stats.armyStrength)}</div>
          <div className="justice-defense-stat-title">Force de l'armée</div>
          <div className="justice-defense-stat-value">{formatNumber(stats.armyStrength)}</div>
          <div className="justice-defense-stat-description">Force totale de défense</div>
        </div>
      </div>

      {/* Justice and Defense Information Card */}
      <div className="justice-defense-info-card">
        <h3>Système de Justice et Défense</h3>
        <p><strong>Formule de calcul de la probabilité de crime :</strong> MAX(0, 0.15 / (1 + EXP(-0.1 * (TotalInhabitants - (15 * activeProtectors * (1 + justiceModifier) - (2.5 * activeSoldiers * (1 + justiceModifier)))))))</p>
        <p><strong>Formule de calcul de la force de l'armée :</strong> (activeSoldiers * 0.5 * (1 + armyModifier)) + ((inhabitantsToPay - allSick - soldiers.number) * 0.125)</p>
        <p>Les protecteurs réduisent la probabilité de crime, tandis que les soldats augmentent la force de défense de la colonie. Les modificateurs de justice et d'armée améliorent l'efficacité de ces deux aspects. <strong>Valeur de défense par habitant :</strong> Citoyen: {stats.defenseValues.citizenDefense.toFixed(3)}, Soldat: {stats.defenseValues.soldierDefense.toFixed(3)}, Protecteur: {stats.defenseValues.protectorDefense.toFixed(3)}.</p>
      </div>

      {/* Modifier Tables */}
      <div className="modifiers-tables">
        <ModifiersTable
          title="Efficacité de la justice"
          tableId={justiceTableId}
          modifiers={justiceModifiers}
          onModifierUpdated={handleModifierUpdated}
        />

        <ModifiersTable
          title="Armée"
          tableId={armyTableId}
          modifiers={armyModifiers}
          onModifierUpdated={handleModifierUpdated}
        />
      </div>

      {/* Defense Means Table */}
      <div className="defense-means-section">
        <div className="defense-means-header">
          <h3>Moyens de défense</h3>
          <button
            className="add-defense-button"
            onClick={() => setShowAddDefenseForm(!showAddDefenseForm)}
          >
            {showAddDefenseForm ? 'Annuler' : 'Ajouter'}
          </button>
        </div>

        {showAddDefenseForm && (
          <form className="add-defense-form" onSubmit={handleAddDefenseMean}>
            <div className="form-group">
              <label htmlFor="title">Titre:</label>
              <input
                type="text"
                id="title"
                value={newDefenseMean.title}
                onChange={(e) => setNewDefenseMean({...newDefenseMean, title: e.target.value})}
                required
              />
            </div>
            <div className="form-group">
              <label htmlFor="description">Description:</label>
              <textarea
                id="description"
                value={newDefenseMean.description}
                onChange={(e) => setNewDefenseMean({...newDefenseMean, description: e.target.value})}
              />
            </div>
            <button type="submit" className="submit-button">Ajouter</button>
          </form>
        )}

        <table className="defense-means-table">
          <thead>
            <tr>
              <th>Titre</th>
              <th>Description</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {!defenseMeans || defenseMeans.length === 0 ? (
              <tr>
                <td colSpan="3" className="no-data">Aucun moyen de défense enregistré</td>
              </tr>
            ) : (
              defenseMeans.map(mean => (
                <tr key={mean.id}>
                  <td>{mean.title}</td>
                  <td>{mean.description}</td>
                  <td>
                    <button
                      className="delete-button"
                      onClick={() => handleDeleteDefenseMean(mean.id)}
                    >
                      Supprimer
                    </button>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
}

// Connecter le composant à Redux tout en maintenant la compatibilité avec les props
export default withRedux(
  JusticeDefenseTabRedux,
  (state) => ({
    // Mapper l'état Redux aux props
    justiceModifiers: selectJusticeModifiers(state),
    armyModifiers: selectArmyModifiers(state),
    defenseMeans: selectDefenseMeans(state),
    stats: selectJusticeDefenseStats(state),
    loading: selectJusticeDefenseLoading(state),
    error: selectJusticeDefenseError(state)
  }),
  (dispatch) => ({
    // Mapper les actions Redux aux props
    fetchJusticeDefenseModifiers: () => dispatch(fetchJusticeDefenseModifiers()),
    updateJusticeDefenseStats: (gameState) => dispatch(updateJusticeDefenseStats(gameState)),
    addDefenseMean: (defenseMean) => dispatch(addDefenseMean(defenseMean)),
    deleteDefenseMean: (id) => dispatch(deleteDefenseMean(id))
  })
);
