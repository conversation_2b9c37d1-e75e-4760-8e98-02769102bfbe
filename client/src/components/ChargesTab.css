.charges-tab {
  padding: 0;
}

.charges-stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.charges-stat-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.charges-stat-icon {
  font-size: 2rem;
  margin-bottom: 10px;
}

.charges-stat-title {
  font-weight: bold;
  margin-bottom: 5px;
}

.charges-stat-value {
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 5px;
}

.charges-stat-description {
  font-size: 0.9rem;
  color: #666;
}

.positive {
  color: #52c41a;
}

.negative {
  color: #f5222d;
}

.modifiers-tables {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.moral-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  font-size: 2rem;
  color: white;
}
