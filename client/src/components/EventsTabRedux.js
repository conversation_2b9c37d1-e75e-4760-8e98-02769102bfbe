import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { fetchEvents, selectEvents, selectEventsLoading, selectEventsError } from '../store/slices/eventsSlice';
import './EventsTab.css';

const EventsTabRedux = () => {
  const dispatch = useDispatch();
  const events = useSelector(selectEvents);
  const loading = useSelector(selectEventsLoading);
  const error = useSelector(selectEventsError);

  useEffect(() => {
    dispatch(fetchEvents());
  }, [dispatch]);

  if (loading) {
    return <div className="loading">Chargement des événements...</div>;
  }

  if (error) {
    return <div className="error">Erreur: {error}</div>;
  }

  return (
    <div className="events-tab">
      <h2>Journal des Événements</h2>
      {events && events.length > 0 ? (
        <div className="events-list-full">
          {events.map((event, index) => (
            <div key={index} className={`event-card ${event.event_type.toLowerCase()}`}>
              <div className="event-icon">
                {event.event_type === 'SICKNESS' && '🤒'}
                {event.event_type === 'CRIME' && '🔪'}
                {event.event_type === 'RESEARCH' && '💡'}
                {event.event_type === 'FAMINE' && '🍽️'}
                {event.event_type === 'TREASURY' && '💰'}
              </div>
              <div className="event-content">
                <div className="event-cycle">Cycle {event.cycle}</div>
                <div className="event-description">{event.description}</div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <p>Aucun événement n'a été enregistré.</p>
      )}
    </div>
  );
};

export default EventsTabRedux;
