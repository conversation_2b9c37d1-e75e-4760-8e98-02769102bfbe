import React from 'react';
import { But<PERSON>, Result } from 'antd';
import { logError, ERROR_TYPES, ERROR_SEVERITY } from '../../utils/errorHandler';

/**
 * Error Boundary Component
 * 
 * Catches JavaScript errors anywhere in the child component tree,
 * logs those errors, and displays a fallback UI.
 */
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { 
      hasError: false, 
      error: null,
      errorInfo: null
    };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    // Log the error to the error handling system
    logError({
      message: error.message,
      type: ERROR_TYPES.UNKNOWN,
      severity: ERROR_SEVERITY.ERROR,
      details: errorInfo.componentStack
    });

    // Update state with error details
    this.setState({ errorInfo });

    // Call onError callback if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  handleReset = () => {
    this.setState({ 
      hasError: false, 
      error: null,
      errorInfo: null
    });

    // Call onReset callback if provided
    if (this.props.onReset) {
      this.props.onReset();
    }
  }

  render() {
    const { fallback, children } = this.props;
    const { hasError, error, errorInfo } = this.state;

    // If there's an error, render the fallback UI
    if (hasError) {
      // Use custom fallback if provided
      if (fallback) {
        return fallback(error, errorInfo, this.handleReset);
      }

      // Default fallback UI
      return (
        <Result
          status="error"
          title="Something went wrong"
          subTitle={error ? error.message : 'An unexpected error occurred'}
          extra={[
            <Button type="primary" key="reset" onClick={this.handleReset}>
              Try Again
            </Button>
          ]}
        >
          {this.props.showDetails && errorInfo && (
            <div style={{ marginTop: 20, textAlign: 'left' }}>
              <details style={{ whiteSpace: 'pre-wrap' }}>
                <summary>Error Details</summary>
                <p>{error && error.toString()}</p>
                <p>Component Stack:</p>
                <p>{errorInfo.componentStack}</p>
              </details>
            </div>
          )}
        </Result>
      );
    }

    // If there's no error, render the children
    return children;
  }
}

// Default props
ErrorBoundary.defaultProps = {
  showDetails: process.env.NODE_ENV !== 'production',
  onError: null,
  onReset: null,
  fallback: null
};

export default ErrorBoundary;
