.construction-tab {
  padding: 20px;
}

.construction-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 30px;
}

@media (max-width: 992px) {
  .construction-content {
    grid-template-columns: 1fr;
  }
}

.building-plans-section,
.construction-details-section {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.building-plans-section h3,
.construction-details-section h3,
.current-projects-section h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #333;
}

.building-plans-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
  max-height: 500px;
  overflow-y: auto;
  padding-right: 10px;
}

.building-plan-card {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
}

.building-plan-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
}

.building-plan-card.selected {
  border: 2px solid #3498db;
  background-color: #ebf5fb;
}

.plan-header {
  margin-bottom: 10px;
}

.plan-name {
  margin: 0 0 5px 0;
  font-size: 16px;
  color: #333;
}

.plan-type {
  font-size: 12px;
  color: #666;
}

.plan-details {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 10px;
}

.plan-stat {
  display: flex;
  flex-direction: column;
  font-size: 12px;
}

.plan-description {
  font-size: 12px;
  color: #666;
}

.selected-plan-details {
  display: flex;
  flex-direction: column;
}

.selected-plan-details h4 {
  margin-top: 0;
  color: #333;
}

.selected-plan-details p {
  font-size: 14px;
  color: #666;
  margin-bottom: 15px;
}

.plan-requirements {
  margin-bottom: 20px;
}

.plan-requirements h5 {
  margin-top: 0;
  margin-bottom: 10px;
  color: #333;
}

.plan-requirements ul {
  padding-left: 20px;
  margin: 0;
}

.plan-requirements li {
  font-size: 14px;
  margin-bottom: 5px;
}

.building-name-input {
  margin-bottom: 20px;
}

.building-name-input label {
  display: block;
  margin-bottom: 5px;
  font-size: 14px;
  color: #333;
}

.building-name-input input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.start-construction-btn {
  align-self: flex-start;
}

.no-plan-selected {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #666;
  text-align: center;
}

.current-projects-section {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.construction-projects-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 15px;
}

.construction-project-card {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.project-header {
  margin-bottom: 10px;
}

.project-name {
  margin: 0 0 5px 0;
  font-size: 16px;
  color: #333;
}

.project-type {
  font-size: 12px;
  color: #666;
}

.project-progress {
  margin-bottom: 10px;
}

.progress-bar {
  height: 10px;
  background-color: #ecf0f1;
  border-radius: 5px;
  overflow: hidden;
  margin-bottom: 5px;
}

.progress-fill {
  height: 100%;
  background-color: #3498db;
}

.progress-text {
  font-size: 12px;
  color: #666;
  text-align: right;
}

.project-workers {
  font-size: 14px;
  color: #333;
}

.workers-label {
  margin-right: 5px;
}

.workers-value {
  font-weight: bold;
}

.no-plans,
.no-projects {
  text-align: center;
  padding: 20px;
  color: #666;
  font-style: italic;
}

.loading, .error {
  text-align: center;
  padding: 30px;
  color: #666;
}
