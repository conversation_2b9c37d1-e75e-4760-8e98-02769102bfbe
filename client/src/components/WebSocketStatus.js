import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import {
  selectWebSocketConnected,
  selectWebSocketError,
  selectWebSocketConnecting,
  selectWebSocketStatus,
  selectWebSocketDisconnectReason,
  selectWebSocketConnectionAttempts,
  selectWebSocketConnectionInfo,
  clearError
} from '../store/slices/websocketSlice';
import { wsConnect } from '../middleware/websocketMiddleware';

const WebSocketStatus = () => {
  const dispatch = useDispatch();
  const isConnected = useSelector(selectWebSocketConnected);
  const isConnecting = useSelector(selectWebSocketConnecting);
  const error = useSelector(selectWebSocketError);
  const status = useSelector(selectWebSocketStatus);
  const disconnectReason = useSelector(selectWebSocketDisconnectReason);
  const connectionAttempts = useSelector(selectWebSocketConnectionAttempts);
  const connectionInfo = useSelector(selectWebSocketConnectionInfo);

  const [showMessage, setShowMessage] = useState(false);
  const [showDetails, setShowDetails] = useState(false);

  // Show message for 5 seconds when connection status changes
  useEffect(() => {
    setShowMessage(true);
    const timer = setTimeout(() => {
      // Ne pas cacher le message s'il y a une erreur ou si la connexion est perdue
      if (isConnected && !error) {
        setShowMessage(false);
      }
    }, 5000);

    return () => clearTimeout(timer);
  }, [isConnected, isConnecting, error, status]);

  // Toujours afficher s'il y a une erreur ou si non connecté
  const shouldShow = showMessage || !isConnected || !!error;

  // Fonction pour reconnecter manuellement
  const handleReconnect = () => {
    // Construire l'URL WebSocket
    const wsUrl = `${window.location.protocol === 'https:' ? 'wss:' : 'ws:'}//${window.location.hostname}:3003`;

    // Effacer l'erreur précédente
    dispatch(clearError());

    // Tenter de se reconnecter
    dispatch(wsConnect(wsUrl));
  };

  const getStatusColor = () => {
    if (isConnected) return '#4CAF50'; // Green
    if (isConnecting) return '#FFA500'; // Orange
    return '#F44336'; // Red
  };

  const getStatusText = () => {
    if (isConnected) return 'Connecté au serveur';
    if (isConnecting) return `Connexion en cours... (${connectionAttempts})`;
    if (error) return 'Erreur de connexion';
    return 'Déconnecté du serveur';
  };

  const getDisconnectReasonText = () => {
    if (!disconnectReason) return '';

    switch (disconnectReason) {
      case 'user_initiated':
        return 'Déconnexion manuelle';
      case 'connection_lost':
        return 'Connexion perdue';
      case 'TIMEOUT':
        return 'Timeout de connexion';
      case 'MAX_RECONNECT_ATTEMPTS':
        return 'Nombre maximum de tentatives atteint';
      default:
        return `Raison: ${disconnectReason}`;
    }
  };

  const statusStyle = {
    position: 'fixed',
    bottom: '10px',
    right: '10px',
    padding: '8px 12px',
    borderRadius: '4px',
    fontSize: '13px',
    fontWeight: 'bold',
    color: 'white',
    backgroundColor: getStatusColor(),
    zIndex: 1000,
    display: shouldShow ? 'flex' : 'none',
    flexDirection: 'column',
    boxShadow: '0 2px 4px rgba(0, 0, 0, 0.2)',
    transition: 'all 0.3s ease',
    opacity: shouldShow ? 1 : 0,
    cursor: 'pointer',
    maxWidth: showDetails ? '300px' : '200px'
  };

  const headerStyle = {
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
    width: '100%'
  };

  const indicatorStyle = {
    width: '10px',
    height: '10px',
    borderRadius: '50%',
    backgroundColor: 'white',
    boxShadow: `0 0 5px ${getStatusColor()}`,
    animation: isConnecting ? 'pulse 1.5s infinite' : 'none'
  };

  const detailsStyle = {
    marginTop: '8px',
    fontSize: '11px',
    backgroundColor: 'rgba(0, 0, 0, 0.2)',
    padding: '6px',
    borderRadius: '3px',
    display: showDetails ? 'block' : 'none'
  };

  const buttonStyle = {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    border: 'none',
    color: 'white',
    padding: '4px 8px',
    borderRadius: '3px',
    fontSize: '11px',
    cursor: 'pointer',
    marginTop: '6px',
    transition: 'background-color 0.2s',
    alignSelf: 'flex-start'
  };

  // Add a pulsing animation for the connecting state
  useEffect(() => {
    const styleElement = document.createElement('style');
    styleElement.textContent = `
      @keyframes pulse {
        0% { opacity: 0.4; }
        50% { opacity: 1; }
        100% { opacity: 0.4; }
      }
    `;
    document.head.appendChild(styleElement);

    return () => {
      document.head.removeChild(styleElement);
    };
  }, []);

  return (
    <div
      style={statusStyle}
      title={error ? `Erreur: ${error.message || 'Inconnue'}` : ''}
      onClick={() => setShowDetails(!showDetails)}
    >
      <div style={headerStyle}>
        <div style={indicatorStyle}></div>
        <div style={{ flex: 1 }}>
          {getStatusText()}
          {!isConnected && !isConnecting && disconnectReason && (
            <div style={{ fontSize: '11px', marginTop: '2px', color: '#FFD700' }}>
              {getDisconnectReasonText()}
            </div>
          )}
          {error && (
            <div style={{ fontSize: '11px', marginTop: '2px', color: '#FFD700' }}>
              Erreur: {error.message || JSON.stringify(error)}
            </div>
          )}
        </div>
      </div>

      {showDetails && (
        <div style={detailsStyle}>
          <div><strong>Status:</strong> {status}</div>
          <div><strong>Tentatives:</strong> {connectionAttempts}</div>
          <div><strong>Dernière tentative:</strong> {connectionInfo.lastConnectionAttempt ? new Date(connectionInfo.lastConnectionAttempt).toLocaleTimeString() : 'Aucune'}</div>
          {connectionInfo.lastSuccessfulConnection && (
            <div><strong>Dernière connexion réussie:</strong> {new Date(connectionInfo.lastSuccessfulConnection).toLocaleTimeString()}</div>
          )}
          {disconnectReason && <div><strong>Raison de déconnexion:</strong> {disconnectReason}</div>}
        </div>
      )}

      {(!isConnected && !isConnecting) && (
        <button
          style={buttonStyle}
          onClick={(e) => {
            e.stopPropagation();
            handleReconnect();
          }}
        >
          Reconnecter
        </button>
      )}
    </div>
  );
};

export default WebSocketStatus;
