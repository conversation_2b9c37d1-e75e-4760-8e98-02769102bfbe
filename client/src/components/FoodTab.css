.food-tab {
  padding: 0;
}

.food-tab h2 {
  margin-bottom: 20px;
}

.food-summary {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.food-summary-item {
  background-color: #fff;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #ddd;
}

.food-summary-label {
  font-weight: bold;
  margin-bottom: 8px;
  color: #555;
}

.food-summary-value {
  font-size: 1.2rem;
  font-weight: bold;
}

.food-stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.food-stat-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  transition: transform 0.2s, box-shadow 0.2s;
}

.food-stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.food-stat-icon {
  font-size: 2rem;
  margin-bottom: 10px;
  background-color: #f5f5f5;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.food-stat-title {
  font-weight: bold;
  margin-bottom: 5px;
  color: #333;
}

.food-stat-value {
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 5px;
}

.food-stat-description {
  color: #666;
  font-size: 0.9rem;
}

.food-stat-debug {
  color: #999;
  font-size: 0.8rem;
  margin-top: 5px;
  font-family: monospace;
}



/* Tables de modificateurs */
.modifiers-tables {
  margin-top: 30px;
}

/* Adjust table layout */
.modifiers-table th:last-child,
.modifiers-table td:last-child {
  text-align: right;
  width: 180px;
}

.modifiers-table th:nth-child(3),
.modifiers-table td:nth-child(3) {
  width: 40%;
}

/* Moral modifier card */
.moral-modifier-card {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  display: flex;
  align-items: center;
}

.moral-icon {
  font-size: 2.5rem;
  margin-right: 20px;
}

.moral-info {
  flex: 1;
}

.moral-title {
  font-weight: bold;
  margin-bottom: 5px;
  font-size: 1.2rem;
}

.moral-value {
  font-size: 1.5rem;
  font-weight: bold;
}

.moral-description {
  color: #666;
  margin-top: 5px;
}
