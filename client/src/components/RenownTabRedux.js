import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import ModifiersTable from './ModifiersTable';
import './RenownTab.css';
import {
  fetchRenownModifiers,
  updateRenownStats,
  selectRenownGeneralModifiers,
  selectRenownStats
} from '../store/slices/renownSlice';
import { withRedux } from '../hoc/withRedux';

function RenownTabRedux({ gameState, updateRenownData }) {
  const dispatch = useDispatch();

  // Sélectionner les données depuis le store Redux
  const renownModifiers = useSelector(selectRenownGeneralModifiers);
  const renownStats = useSelector(selectRenownStats);

  // ID de la table des modificateurs de renommée (même que l'original)
  const renownTableId = 17;

  // Charger les modificateurs au montage du composant
  useEffect(() => {
    dispatch(fetchRenownModifiers());
  }, [dispatch]);

  // Mettre à jour les statistiques de renommée quand gameState change
  useEffect(() => {
    if (gameState) {
      dispatch(updateRenownStats({ gameState }));
    }
  }, [dispatch, gameState]);

  // Mettre à jour les données du composant parent si la fonction est fournie
  useEffect(() => {
    if (renownStats && updateRenownData) {
      const dataToUpdate = {
        totalRenown: renownStats.totalRenown || 0,
        renownPerCycle: renownStats.renownPerCycle || 0
      };

      updateRenownData(dataToUpdate);
    }
  }, [renownStats, updateRenownData]);

  // Fonction pour recharger les modificateurs (utilisée par la table de modificateurs)
  const handleModifierUpdated = () => {
    dispatch(fetchRenownModifiers());

    // Forcer une mise à jour des statistiques si gameState est disponible
    if (gameState) {
      setTimeout(() => {
        dispatch(updateRenownStats({ gameState }));
      }, 100); // Petit délai pour s'assurer que les modificateurs sont chargés
    }
  };

  // Formater un nombre avec 0 décimales (valeurs entières)
  const formatNumber = (value) => {
    if (value === undefined || value === null) return '0';
    return Math.round(value).toString();
  };

  return (
    <div className="renown-tab">
      <div className="renown-stats">
        <div className="renown-stats-card">
          <div className="renown-stat-icon">🏆</div>
          <div className="renown-stat-title">Renommée</div>
          <div className="renown-stat-value">{formatNumber(renownStats.totalRenown)}</div>
          <div className="renown-stat-description">
            Points de renommée accumulés
          </div>
        </div>
      </div>

      <div className="modifiers-tables">
        <ModifiersTable
          title="Modificateurs de renommée"
          tableId={renownTableId}
          modifiers={renownModifiers}
          onModifierUpdated={handleModifierUpdated}
          useAbsoluteValues={true} // Utiliser des valeurs entières, pas des pourcentages
        />
      </div>
    </div>
  );
}

// Connecter le composant à Redux tout en maintenant la compatibilité avec les props
export default withRedux(
  RenownTabRedux,
  (state) => ({
    // Mapper l'état Redux aux props
    renownModifiers: selectRenownGeneralModifiers(state),
    renownStats: selectRenownStats(state)
  }),
  (dispatch) => ({
    // Mapper les actions Redux aux props
    fetchRenownModifiers: () => dispatch(fetchRenownModifiers()),
    updateRenownStats: (gameState) => dispatch(updateRenownStats(gameState))
  })
);
