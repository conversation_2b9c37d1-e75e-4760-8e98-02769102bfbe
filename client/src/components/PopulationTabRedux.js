import React, { useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import './PopulationTab.css';
import config from '../config';
import ListView from './population/ListView';
import SummaryView from './population/SummaryView';
import ViewToggle from './population/ViewToggle';
import PopulationStats from './population/PopulationStats';
import CreateInhabitantButton from './population/CreateInhabitantButton';
import InhabitantEditModal from './population/InhabitantEditModal';
import QuickAddModal from './population/QuickAddModal';
import {
  fetchInhabitants,
  fetchPopulationStats,
  createInhabitant,
  updateInhabitant,
  deleteInhabitant,
  syncInhabitantsWithJobs,
  setViewMode,
  setFilters,
  clearFilters,
  setCurrentPage,
  setItemsPerPage,
  selectInhabitants,
  selectPopulationStats,
  selectFilters,
  selectViewMode,
  selectPopulationLoading,
  selectCurrentPage,
  selectTotalPages,
  selectItemsPerPage
} from '../store/slices/populationSlice';
import { fetchGameState } from '../store/slices/gameSlice';
import { withRedux } from '../hoc/withRedux';
import { getAllTabsToUpdate } from '../utils/jobUpdateUtils';
import { updateSpecificTabs } from '../store/actions/tabUpdateActions';

function PopulationTabRedux({ gameState, fetchGameState }) {
  const dispatch = useDispatch();

  // Sélectionner les données depuis le store Redux
  const inhabitants = useSelector(selectInhabitants);
  const populationStats = useSelector(selectPopulationStats);
  const filters = useSelector(selectFilters);
  const viewMode = useSelector(selectViewMode);
  const loading = useSelector(selectPopulationLoading);
  const currentPage = useSelector(selectCurrentPage);
  const totalPages = useSelector(selectTotalPages);
  const itemsPerPage = useSelector(selectItemsPerPage);

  // État local pour les modales
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [quickAddModalOpen, setQuickAddModalOpen] = useState(false);
  const [selectedInhabitant, setSelectedInhabitant] = useState(null);
  const [isShiftPressed, setIsShiftPressed] = useState(false);

  // Charger les habitants et les statistiques au montage du composant
  useEffect(() => {
    const params = {
      ...filters,
      page: currentPage,
      limit: itemsPerPage
    };
    dispatch(fetchInhabitants(params));
    dispatch(fetchPopulationStats());
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Recharger les habitants quand les filtres, la page ou le nombre d'éléments par page changent
  useEffect(() => {
    const params = {
      ...filters,
      page: currentPage,
      limit: itemsPerPage
    };
    dispatch(fetchInhabitants(params));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [filters, currentPage, itemsPerPage]);

  // Recharger les statistiques de population quand gameState change
  useEffect(() => {
    if (gameState) {
      dispatch(fetchPopulationStats());
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [gameState]);

  // Gestionnaire d'événements clavier pour les touches fléchées et Shift
  useEffect(() => {
    const handleKeyDown = (e) => {
      // Ne pas traiter les événements clavier si l'élément actif est un input, textarea ou select
      if (
        e.target.tagName === 'INPUT' ||
        e.target.tagName === 'TEXTAREA' ||
        e.target.tagName === 'SELECT' ||
        e.target.isContentEditable
      ) {
        return;
      }

      // Gérer la touche Shift pour les changements de job
      if (e.key === 'Shift') {
        setIsShiftPressed(true);
      }

      // Gérer les touches fléchées pour la navigation entre les vues
      if (e.key === 'ArrowLeft' && viewMode === 'summary') {
        handleViewModeChange('list');
      } else if (e.key === 'ArrowRight' && viewMode === 'list') {
        handleViewModeChange('summary');
      }
    };

    const handleKeyUp = (e) => {
      if (e.key === 'Shift') {
        setIsShiftPressed(false);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    window.addEventListener('keyup', handleKeyUp);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('keyup', handleKeyUp);
    };
  }, [viewMode]); // eslint-disable-line react-hooks/exhaustive-deps

  // Gestionnaires d'événements
  const handleViewModeChange = async (mode) => {
    // Si on passe à la vue résumé, synchroniser d'abord les habitants avec les emplois
    if (mode === 'summary') {
      try {
        // Forcer la synchronisation entre les habitants et les emplois
        await dispatch(syncInhabitantsWithJobs()).unwrap();

        // Mettre à jour les statistiques
        dispatch(fetchPopulationStats());

        // Mettre à jour l'état du jeu pour refléter les changements
        if (fetchGameState) {
          fetchGameState();
        }
      } catch (error) {
        console.error('Erreur lors de la synchronisation pour le changement de vue:', error);
      }
    }

    // Changer le mode de vue après la synchronisation
    dispatch(setViewMode(mode));
  };

  const handleFilterChange = (newFilters) => {
    dispatch(setFilters(newFilters));
  };

  const handleClearFilters = () => {
    dispatch(clearFilters());
  };

  const handlePageChange = (page) => {
    dispatch(setCurrentPage(page));
  };

  const handleItemsPerPageChange = (limit) => {
    dispatch(setItemsPerPage(limit));
  };

  const handleEditInhabitant = (inhabitant) => {
    setSelectedInhabitant(inhabitant);
    setEditModalOpen(true);
  };

  const handleDeleteInhabitant = async (id) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer cet habitant ?')) {
      try {
        // Supprimer l'habitant
        await dispatch(deleteInhabitant(id)).unwrap();

        // Synchroniser automatiquement avec les emplois après la suppression
        await dispatch(syncInhabitantsWithJobs()).unwrap();

        // Mettre à jour uniquement l'onglet population après la suppression d'un habitant
        if (fetchGameState) {
          dispatch(updateSpecificTabs(['population'], gameState, gameState?.moralStats));
        }
      } catch (error) {
        console.error('Erreur lors de la suppression de l\'habitant:', error);
      }
    }
  };

  const handleSaveInhabitant = async (inhabitant) => {
    try {
      // Vérifier si le métier a changé
      const isJobChanged = selectedInhabitant &&
                          selectedInhabitant.job_id !== inhabitant.job_id;

      console.log('PopulationTabRedux: Sauvegarde d\'un habitant', {
        oldJobId: selectedInhabitant?.job_id,
        oldJobIdType: typeof selectedInhabitant?.job_id,
        newJobId: inhabitant.job_id,
        newJobIdType: typeof inhabitant.job_id,
        isJobChanged,
        inhabitant: inhabitant,
        selectedInhabitant: selectedInhabitant
      });

      if (selectedInhabitant) {
        // Créer une copie de l'habitant pour s'assurer que nous ne modifions pas l'état directement
        const inhabitantToUpdate = { ...inhabitant };

        // S'assurer que job_id est un nombre
        if (inhabitantToUpdate.job_id !== undefined && inhabitantToUpdate.job_id !== null && inhabitantToUpdate.job_id !== '') {
          inhabitantToUpdate.job_id = parseInt(inhabitantToUpdate.job_id, 10);
        }

        console.log('PopulationTabRedux: Données à envoyer:', inhabitantToUpdate);

        await dispatch(updateInhabitant({ id: selectedInhabitant.id, inhabitant: inhabitantToUpdate })).unwrap();

        // Si le métier a changé, rafraîchir les données du jeu
        if (isJobChanged) {
          console.log('PopulationTabRedux: Le métier a changé, rafraîchissement des données du jeu');
          await dispatch(fetchGameState());
        }
      } else {
        await dispatch(createInhabitant(inhabitant)).unwrap();
      }

      // Synchroniser automatiquement avec les emplois après chaque modification d'habitant
      await dispatch(syncInhabitantsWithJobs()).unwrap();

      // Vérifier si le statut de maladie a changé
      const isSicknessChanged = selectedInhabitant &&
                              inhabitant.is_sick !== undefined &&
                              selectedInhabitant.is_sick !== inhabitant.is_sick;

      console.log('PopulationTabRedux: Vérification des changements:', {
        isJobChanged,
        isSicknessChanged,
        oldSickness: selectedInhabitant?.is_sick,
        newSickness: inhabitant.is_sick
      });

      // Déterminer quels onglets doivent être mis à jour
      const tabsToUpdate = getAllTabsToUpdate(
        inhabitant,
        isJobChanged,
        isSicknessChanged,
        selectedInhabitant?.job_id,
        selectedInhabitant?.job_name
      );

      console.log('PopulationTabRedux: Onglets à mettre à jour:', tabsToUpdate);

      // Mettre à jour les onglets spécifiques
      if (fetchGameState) {
        if (isJobChanged || isSicknessChanged) {
          console.log('PopulationTabRedux: Métier ou statut de maladie modifié, mise à jour des onglets spécifiques');
          // Attendre un court instant pour s'assurer que les données sont bien enregistrées côté serveur
          setTimeout(() => {
            dispatch(updateSpecificTabs(tabsToUpdate, gameState, gameState?.moralStats));
          }, 200); // Augmenter le délai pour s'assurer que les données sont bien synchronisées
        } else {
          // Si aucun changement significatif, mettre à jour uniquement l'onglet population
          dispatch(updateSpecificTabs(['population'], gameState, gameState?.moralStats));
        }
      }

      setEditModalOpen(false);
      setSelectedInhabitant(null);
    } catch (error) {
      console.error('Erreur lors de la sauvegarde de l\'habitant:', error);
    }
  };

  const handleQuickAdd = async (count, options) => {
    try {
      // Créer plusieurs habitants rapidement
      const promises = [];
      for (let i = 0; i < count; i++) {
        promises.push(dispatch(createInhabitant(options)).unwrap());
      }

      // Attendre que tous les habitants soient créés
      await Promise.all(promises);

      // Synchroniser automatiquement avec les emplois après l'ajout des habitants
      await dispatch(syncInhabitantsWithJobs()).unwrap();

      // Mettre à jour uniquement l'onglet population après l'ajout rapide d'habitants
      if (fetchGameState) {
        dispatch(updateSpecificTabs(['population'], gameState, gameState?.moralStats));
      }

      // Fermer la modale une fois terminé
      setQuickAddModalOpen(false);
    } catch (error) {
      console.error('Erreur lors de l\'ajout rapide d\'habitants:', error);
    }
  };

  // La fonction handleSyncWithJobs a été supprimée car la synchronisation se fait automatiquement

  // Obtenir les emplois disponibles à partir de gameState
  const availableJobs = gameState?.jobs || [];

  if (loading && inhabitants.length === 0) {
    return <div className="loading">Loading population data...</div>;
  }

  return (
    <div className="population-tab">
      <h2>Population</h2>

      {/* View Toggle */}
      <ViewToggle currentView={viewMode} onViewChange={handleViewModeChange} />

      {/* Statistiques de population */}
      <PopulationStats
        stats={populationStats}
        gameState={gameState}
        onInhabitantCreated={handleSaveInhabitant}
      />

      {/* Vue principale */}
      {viewMode === 'list' ? (
        <ListView
          inhabitants={inhabitants}
          loading={loading}
          error={null}
          filters={filters}
          handleFilterChange={(e) => handleFilterChange({ [e.target.name]: e.target.value })}
          handleSearch={(e) => {
            e.preventDefault();
            handleFilterChange(filters);
          }}
          handleSort={(field) => {
            // Implement sorting logic here if needed
            console.log('Sort by', field);
          }}
          sortBy="last_name"
          sortOrder="asc"
          page={currentPage}
          totalPages={totalPages}
          handlePageChange={handlePageChange}
          handleInhabitantSelect={(inhabitant) => {
            setSelectedInhabitant(inhabitant);
          }}
          selectedInhabitant={selectedInhabitant}
          closeInhabitantDetails={() => setSelectedInhabitant(null)}
          gameState={gameState}
          onInhabitantUpdated={(inhabitant, isJobUpdate) => {
            console.log('PopulationTabRedux: Habitant mis à jour', { inhabitant, isJobUpdate });

            // Mettre à jour la liste des habitants
            dispatch(fetchInhabitants({
              ...filters,
              page: currentPage,
              limit: itemsPerPage
            }));

            // Mettre à jour les statistiques de population
            dispatch(fetchPopulationStats());

            // Vérifier si c'est une mise à jour de statut de maladie
            const isSicknessUpdate = inhabitant.is_sick !== undefined;

            // Déterminer quels onglets doivent être mis à jour
            const tabsToUpdate = getAllTabsToUpdate(
              inhabitant,
              isJobUpdate,
              isSicknessUpdate,
              inhabitant.old_job_id,
              inhabitant.old_job_name
            );

            console.log('PopulationTabRedux: Onglets à mettre à jour:', tabsToUpdate);

            // Mettre à jour les onglets spécifiques
            if (tabsToUpdate.length > 0) {
              // Attendre un court instant pour s'assurer que les données sont bien enregistrées côté serveur
              setTimeout(() => {
                dispatch(updateSpecificTabs(tabsToUpdate, gameState, gameState?.moralStats));
              }, 200);
            }
          }}
          onInhabitantDeleted={(id) => handleDeleteInhabitant(id)}
        />
      ) : (
        <SummaryView
          stats={populationStats}
          inhabitants={inhabitants}
          isShiftPressed={isShiftPressed}
          setIsShiftPressed={setIsShiftPressed}
          gameState={gameState}
          onJobChange={() => {
            // Mettre à jour les statistiques de population
            dispatch(fetchPopulationStats());

            // Note: Nous ne mettons plus à jour l'état du jeu global ici
            // car cela provoque un rechargement de la page
          }}
          onFreeChange={(jobId, change) => {
            // La logique d'appel API a été déplacée dans le composant SummaryView
            // pour une meilleure gestion des erreurs et des mises à jour Redux

            // Mettre à jour les statistiques de population
            dispatch(fetchPopulationStats());
          }}
        />
      )}

      {/* Modales */}
      {editModalOpen && (
        <InhabitantEditModal
          inhabitant={selectedInhabitant}
          onSave={handleSaveInhabitant}
          onClose={() => {
            setEditModalOpen(false);
            setSelectedInhabitant(null);
          }}
          onDelete={(id) => {
            dispatch(deleteInhabitant(id));
            setEditModalOpen(false);
            setSelectedInhabitant(null);
          }}
          gameState={gameState}
        />
      )}

      {quickAddModalOpen && (
        <QuickAddModal
          onAdd={handleQuickAdd}
          onCancel={() => setQuickAddModalOpen(false)}
          availableJobs={availableJobs}
        />
      )}
    </div>
  );
}

// Connecter le composant à Redux tout en maintenant la compatibilité avec les props
export default withRedux(
  PopulationTabRedux,
  (state) => ({
    // Mapper l'état Redux aux props
    inhabitants: selectInhabitants(state),
    populationStats: selectPopulationStats(state),
    filters: selectFilters(state),
    viewMode: selectViewMode(state),
    loading: selectPopulationLoading(state),
    currentPage: selectCurrentPage(state),
    totalPages: selectTotalPages(state),
    itemsPerPage: selectItemsPerPage(state)
  }),
  (dispatch) => ({
    // Mapper les actions Redux aux props
    fetchInhabitants: (params) => dispatch(fetchInhabitants(params)),
    fetchPopulationStats: () => dispatch(fetchPopulationStats()),
    createInhabitant: (inhabitant) => dispatch(createInhabitant(inhabitant)),
    updateInhabitant: (id, inhabitant) => dispatch(updateInhabitant({ id, inhabitant })),
    deleteInhabitant: (id) => dispatch(deleteInhabitant(id)),
    syncInhabitantsWithJobs: () => dispatch(syncInhabitantsWithJobs()),
    setViewMode: (mode) => dispatch(setViewMode(mode)),
    setFilters: (filters) => dispatch(setFilters(filters)),
    clearFilters: () => dispatch(clearFilters()),
    setCurrentPage: (page) => dispatch(setCurrentPage(page)),
    setItemsPerPage: (limit) => dispatch(setItemsPerPage(limit))
  })
);
