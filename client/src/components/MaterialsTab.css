.materials-tab {
    padding: 0;
}

.materials-tab h2 {
    margin-bottom: 20px;
    font-size: 1.8rem;
    color: #333;
}

.materials-stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.materials-stat-card {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    padding: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.materials-stat-icon {
    font-size: 2rem;
    margin-bottom: 10px;
}

.materials-stat-title {
    font-weight: bold;
    margin-bottom: 5px;
    color: #555;
}

.materials-stat-value {
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.materials-stat-description {
    font-size: 0.9rem;
    color: #777;
}

.positive {
    color: #28a745;
}

.negative {
    color: #dc3545;
}

.modifiers-tables {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

@media (max-width: 768px) {
    .materials-stats-cards {
        grid-template-columns: 1fr;
    }

    .modifiers-tables {
        grid-template-columns: 1fr;
    }
}
