import React, { useState } from 'react';
import { useDispatch } from 'react-redux';
import './ModifiersTable.css';
import { addModifier, updateModifier, deleteModifier } from '../services/ModifiersService';
import { fetchChargesModifiers } from '../store/slices/chargesSlice';

const ModifiersTable = ({ title = 'Modificateurs', tableId = 0, modifiers = [], onModifierUpdated = () => {} }) => {
  const dispatch = useDispatch();
  // Utiliser directement les props modifiers au lieu d'un état local
  const [newModifier, setNewModifier] = useState({
    title: '',
    effect: 0,
    description: ''
  });
  const [editingModifier, setEditingModifier] = useState(null);
  const [showAddForm, setShowAddForm] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Format number as percentage
  const formatPercent = (value) => {
    return `${(value * 100).toFixed(1)}%`;
  };

  // Format number as flat value (for moral table) - sans décimales
  const formatFlat = (value) => {
    return `${Math.round(value)}`; // Pas de multiplication pour la table de moral
  };

  // Fonction spéciale pour afficher les valeurs avec des couleurs inversées pour la table de péremption
  const renderPerishableValue = (effect) => {
    const color = effect >= 0 ? '#f44336' : '#4CAF50'; // Rouge pour positif, vert pour négatif
    return (
      <span style={{ color: color, fontWeight: 'bold' }}>
        {formatPercent(effect)}
      </span>
    );
  };

  // Handle adding a new modifier
  const handleAddModifier = async () => {
    if (!newModifier.title || newModifier.effect === '') {
      alert('Veuillez remplir tous les champs obligatoires');
      return;
    }

    if (isSubmitting) {
      return; // Prevent multiple submissions
    }

    setIsSubmitting(true);

    // Pour les tables de moral et de renommée, on utilise directement la valeur entière
    let effectValue;
    if (isMoralTable || isRenownTable) {
      effectValue = parseInt(newModifier.effect); // Valeur entière pour le moral et la renommée
    } else {
      effectValue = parseFloat(newModifier.effect) / 100; // Conversion en décimal pour les autres tables
    }

    const modifierData = {
      title: newModifier.title,
      effect: effectValue,
      description: newModifier.description || ''
    };

    try {
      console.log(`ModifiersTable: Ajout d'un modificateur à la table ${tableId}`, modifierData);

      // Utiliser directement le service pour garantir une mise à jour immédiate
      const result = await addModifier(tableId, modifierData);
      console.log(`ModifiersTable: Modificateur ajouté avec succès via Service`, result);

      // Ne pas mettre à jour l'état local, laisser le parent s'en charger

      // Suppression du traitement spécial pour les tables de charges
      // Nous utilisons maintenant le rechargement de page pour toutes les tables

      // Reset the form
      setNewModifier({
        title: '',
        effect: 0,
        description: ''
      });

      // Hide the form
      setShowAddForm(false);

      // Notifier le parent pour toutes les tables - plus de rechargement de page
      console.log(`ModifiersTable: Appel de onModifierUpdated pour la table ${tableId}`);
      if (onModifierUpdated) {
        onModifierUpdated();
      }
    } catch (error) {
      console.error('Erreur lors de l\'ajout du modificateur:', error);
      alert(`Échec de l'ajout du modificateur: ${error.message}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle editing a modifier
  const handleEditModifier = (modifier) => {
    setEditingModifier({
      ...modifier,
      // Pour les tables de moral et de renommée, on utilise directement la valeur
      effect: isMoralTable || isRenownTable ? modifier.effect : modifier.effect * 100
    });
  };

  // Handle saving edited modifier
  const handleSaveEdit = async () => {
    if (!editingModifier || !editingModifier.title || editingModifier.effect === '') {
      alert('Veuillez remplir tous les champs obligatoires');
      return;
    }

    if (isSubmitting) {
      return; // Prevent multiple submissions
    }

    setIsSubmitting(true);

    try {
      // Utiliser le service ModifiersService au lieu d'un appel fetch direct
      let effectValue;
      if (isMoralTable || isRenownTable) {
        effectValue = parseInt(editingModifier.effect); // Valeur entière pour le moral et la renommée
      } else {
        effectValue = parseFloat(editingModifier.effect) / 100; // Conversion en décimal pour les autres tables
      }

      const modifierData = {
        title: editingModifier.title,
        effect: effectValue,
        description: editingModifier.description || ''
      };

      console.log(`ModifiersTable: Mise à jour du modificateur ${editingModifier.id}`, modifierData);

      await updateModifier(editingModifier.id, modifierData);
      console.log(`ModifiersTable: Modificateur mis à jour avec succès`);

      // Reset editing state
      setEditingModifier(null);

      // Notifier le parent pour toutes les tables - plus de rechargement de page
      console.log(`ModifiersTable: Appel de onModifierUpdated après modification pour la table ${tableId}`);
      if (onModifierUpdated) {
        onModifierUpdated();
      }
    } catch (error) {
      console.error('Erreur lors de la mise à jour du modificateur:', error);
      alert('Échec de la mise à jour du modificateur. Veuillez réessayer.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle canceling edit
  const handleCancelEdit = () => {
    setEditingModifier(null);
  };

  // Handle key press in edit form
  const handleEditKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleSaveEdit();
    } else if (e.key === 'Escape') {
      handleCancelEdit();
    }
  };

  // Handle key press in new modifier form
  const handleNewModifierKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleAddModifier();
    }
  };

  // Handle deleting a modifier
  const handleDeleteModifier = async (modifierId) => {
    if (isSubmitting) {
      return; // Prevent multiple submissions
    }

    setIsSubmitting(true);

    try {
      console.log(`ModifiersTable: Suppression du modificateur ${modifierId}`);

      // Utiliser directement le service pour garantir une mise à jour immédiate
      try {
        const result = await deleteModifier(modifierId);
        console.log(`ModifiersTable: Modificateur supprimé avec succès`, result);

        // Ne pas mettre à jour l'état local, laisser le parent s'en charger
      } catch (error) {
        // Si le modificateur n'est pas trouvé, c'est qu'il a déjà été supprimé
        // On considère que c'est un succès
        if (error.message && error.message.includes('Modifier not found')) {
          console.log(`ModifiersTable: Le modificateur ${modifierId} a déjà été supprimé`);
          // Ne pas mettre à jour l'état local, laisser le parent s'en charger
        } else {
          // Pour les autres erreurs, on les propage
          throw error;
        }
      }

      // Suppression du traitement spécial pour les tables de charges
      // Nous utilisons maintenant le rechargement de page pour toutes les tables

      // Notifier le parent pour toutes les tables - plus de rechargement de page
      console.log(`ModifiersTable: Appel de onModifierUpdated après suppression pour la table ${tableId}`);
      if (onModifierUpdated) {
        onModifierUpdated();
      }
    } catch (error) {
      console.error('Erreur lors de la suppression du modificateur:', error);
      alert(`Échec de la suppression du modificateur: ${error.message || 'Erreur inconnue'}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Calculate total effect - vérifier que modifiers est un tableau et que les effets sont définis
  const displayModifiers = modifiers || [];
  const totalEffect = Array.isArray(displayModifiers)
    ? displayModifiers.reduce((sum, mod) => {
        // Vérifier que mod et mod.effect sont définis
        if (!mod) return sum;
        const effect = mod.effect !== undefined ? mod.effect : 0;
        return sum + effect;
      }, 0)
    : 0;

  // Log pour le débogage
  console.log(`ModifiersTable (${title}): Modifiers:`, displayModifiers);
  console.log(`ModifiersTable (${title}): Total Effect:`, totalEffect);

  // Determine if this is the perishable table (tableId === 3) or charges global modifiers table (tableId === 8)
  // or moral table (tableId === 10) or renown table (tableId === 17)
  const isPerishableTable = tableId === 3;
  const isChargesGlobalTable = tableId === 8;
  const isMoralTable = tableId === 10; // ID de la table de moral est maintenant 10
  const isRenownTable = tableId === 17; // ID de la table de renommée

  // For perishable table and charges global modifiers table, invert the color logic
  const getEffectClass = (effect) => {
    if (isPerishableTable || isChargesGlobalTable) {
      return effect >= 0 ? 'negative' : 'positive';
    } else {
      return effect >= 0 ? 'positive' : 'negative';
    }
  };

  return (
    <div className="modifiers-table-container">
      <div className="modifiers-table-header">
        <h2>{title}</h2>
        <div className="total-effect">
          Total: {isPerishableTable
            ? renderPerishableValue(totalEffect)
            : isMoralTable || isRenownTable
              ? <span className={getEffectClass(totalEffect)}>{formatFlat(totalEffect)}</span>
              : <span className={getEffectClass(totalEffect)}>{formatPercent(totalEffect)}</span>
          }
        </div>
      </div>

      <table className="modifiers-table">
        <thead>
          <tr>
            <th>Intitulé</th>
            <th>{isMoralTable || isRenownTable ? 'Effet' : 'Effet (%)'}</th>
            <th>Description</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {Array.isArray(displayModifiers) && displayModifiers.filter(mod => mod && typeof mod.id === 'number').map(modifier => (
            <tr key={modifier.id}>
              {editingModifier && editingModifier.id === modifier.id ? (
                // Edit mode
                <>
                  <td>
                    <input
                      type="text"
                      value={editingModifier.title}
                      onChange={e => setEditingModifier({...editingModifier, title: e.target.value})}
                      onKeyDown={handleEditKeyPress}
                      autoFocus
                    />
                  </td>
                  <td>
                    <input
                      type="number"
                      value={editingModifier.effect}
                      onChange={e => setEditingModifier({...editingModifier, effect: e.target.value})}
                      onKeyDown={handleEditKeyPress}
                      step="0.1"
                      style={{ width: '80px' }}
                    />
                  </td>
                  <td>
                    <input
                      type="text"
                      value={editingModifier.description || ''}
                      onChange={e => setEditingModifier({...editingModifier, description: e.target.value})}
                      onKeyDown={handleEditKeyPress}
                      style={{ width: '100%' }}
                    />
                  </td>
                  <td>
                    <button
                      className="save-btn"
                      onClick={handleSaveEdit}
                    >
                      Sauvegarder
                    </button>
                    <button
                      className="cancel-btn"
                      onClick={handleCancelEdit}
                    >
                      Annuler
                    </button>
                  </td>
                </>
              ) : (
                // View mode
                <>
                  <td>{modifier.title}</td>
                  <td>
                    {isPerishableTable
                      ? renderPerishableValue(modifier.effect)
                      : isMoralTable || isRenownTable
                        ? <span className={getEffectClass(modifier.effect)}>{formatFlat(modifier.effect)}</span>
                        : <span className={getEffectClass(modifier.effect)}>{formatPercent(modifier.effect)}</span>
                    }
                  </td>
                  <td>{modifier.description}</td>
                  <td>
                    {/* Don't allow editing or deletion of the base perishable rate */}
                    {!(tableId === 3 && modifier.title === 'Base') && (
                      <div className="action-buttons">
                        <button
                          className="edit-btn"
                          onClick={() => handleEditModifier(modifier)}
                        >
                          Modifier
                        </button>
                        <button
                          className="delete-btn"
                          onClick={() => handleDeleteModifier(modifier.id)}
                        >
                          Supprimer
                        </button>
                      </div>
                    )}
                  </td>
                </>
              )}
            </tr>
          ))}
        </tbody>
      </table>

      {!showAddForm ? (
        <div className="add-modifier-button-container">
          <button
            className="add-btn"
            onClick={() => setShowAddForm(true)}
          >
            + Ajouter un modificateur
          </button>
        </div>
      ) : (
        <div className="add-modifier-form">
          <h3>Ajouter un modificateur</h3>
          <div className="form-row">
            <div className="form-group">
              <label>Intitulé:</label>
              <input
                type="text"
                value={newModifier.title}
                onChange={e => setNewModifier({...newModifier, title: e.target.value})}
                onKeyDown={handleNewModifierKeyPress}
                autoFocus
              />
            </div>
            <div className="form-group">
              <label>{isMoralTable || isRenownTable ? 'Effet:' : 'Effet (%):'}</label>
              <input
                type="number"
                value={newModifier.effect}
                onChange={e => setNewModifier({...newModifier, effect: e.target.value})}
                onKeyDown={handleNewModifierKeyPress}
                step="0.1"
              />
            </div>
          </div>
          <div className="form-group">
            <label>Description:</label>
            <input
              type="text"
              value={newModifier.description}
              onChange={e => setNewModifier({...newModifier, description: e.target.value})}
              onKeyDown={handleNewModifierKeyPress}
            />
          </div>
          <div className="form-buttons">
            <button
              className="add-btn"
              onClick={handleAddModifier}
            >
              Ajouter
            </button>
            <button
              className="cancel-btn"
              onClick={() => {
                setShowAddForm(false);
                setNewModifier({
                  title: '',
                  effect: 0,
                  description: ''
                });
              }}
            >
              Annuler
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ModifiersTable;
