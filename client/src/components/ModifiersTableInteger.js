import React, { useState } from 'react';
import './ModifiersTable.css';
import { useApiEndpoint } from '../hooks';

const ModifiersTableInteger = ({ title, tableId, modifiers, onModifierUpdated }) => {
  const [newModifier, setNewModifier] = useState({
    title: '',
    effect: 0,
    description: ''
  });
  const [editingModifier, setEditingModifier] = useState(null);
  const [showAddForm, setShowAddForm] = useState(false);

  // Use enhanced API hooks
  const addModifierApi = useApiEndpoint({
    method: 'POST'
  });

  const updateModifierApi = useApiEndpoint({
    method: 'PUT'
  });

  const deleteModifierApi = useApiEndpoint({
    method: 'DELETE'
  });

  // Format number as integer
  const formatInteger = (value) => {
    return `${parseInt(value)}`;
  };

  // Handle adding a new modifier
  const handleAddModifier = async () => {
    if (!newModifier.title || newModifier.effect === '') {
      alert('Veuillez remplir tous les champs obligatoires');
      return;
    }

    try {
      console.log(`ModifiersTableInteger: Ajout d'un modificateur à la table ${tableId}`, {
        title: newModifier.title,
        effect: parseInt(newModifier.effect),
        description: newModifier.description || ''
      });

      // Utiliser le hook API amélioré
      const data = await addModifierApi.fetchData({
        endpoint: `modifiers/table/${tableId}`,
        data: {
          title: newModifier.title,
          effect: parseInt(newModifier.effect), // Use integer value directly
          description: newModifier.description || ''
        }
      });

      console.log(`ModifiersTableInteger: Modificateur ajouté avec succès`, data);

      // Reset the form
      setNewModifier({
        title: '',
        effect: 0,
        description: ''
      });

      // Hide the form
      setShowAddForm(false);

      // Notify parent component to refresh data
      onModifierUpdated();
    } catch (error) {
      console.error('Erreur lors de l\'ajout du modificateur:', error);
      alert('Échec de l\'ajout du modificateur. Veuillez réessayer.');
    }
  };

  // Handle editing a modifier
  const handleEditModifier = (modifier) => {
    setEditingModifier({
      ...modifier,
      effect: modifier.effect // No conversion needed for integers
    });
  };

  // Handle saving edited modifier
  const handleSaveEdit = async () => {
    if (!editingModifier || !editingModifier.title || editingModifier.effect === '') {
      alert('Veuillez remplir tous les champs obligatoires');
      return;
    }

    try {
      // Utiliser le hook API amélioré
      await updateModifierApi.fetchData({
        endpoint: `modifiers/${editingModifier.id}`,
        data: {
          title: editingModifier.title,
          effect: parseInt(editingModifier.effect), // Use integer value directly
          description: editingModifier.description || ''
        }
      });

      // Reset editing state
      setEditingModifier(null);

      // Notify parent component to refresh data
      onModifierUpdated();
    } catch (error) {
      console.error('Erreur lors de la mise à jour du modificateur:', error);
      alert('Échec de la mise à jour du modificateur. Veuillez réessayer.');
    }
  };

  // Handle canceling edit
  const handleCancelEdit = () => {
    setEditingModifier(null);
  };

  // Handle key press in edit form
  const handleEditKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleSaveEdit();
    } else if (e.key === 'Escape') {
      handleCancelEdit();
    }
  };

  // Handle key press in new modifier form
  const handleNewModifierKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleAddModifier();
    }
  };

  // Handle deleting a modifier
  const handleDeleteModifier = async (modifierId) => {
    try {
      console.log(`ModifiersTableInteger: Suppression du modificateur ${modifierId}`);

      // Utiliser le hook API amélioré
      const result = await deleteModifierApi.fetchData({
        endpoint: `modifiers/${modifierId}`
      });

      console.log(`ModifiersTableInteger: Modificateur supprimé avec succès`, result);

      // Notify parent component to refresh data
      onModifierUpdated();
    } catch (error) {
      console.error('Erreur lors de la suppression du modificateur:', error);
      alert(`Échec de la suppression du modificateur: ${error.message || 'Erreur inconnue'}`);
    }
  };

  // Calculate total effect
  const totalEffect = modifiers.reduce((sum, mod) => sum + mod.effect, 0);

  // Get effect class for styling
  const getEffectClass = (effect) => {
    return effect >= 0 ? 'positive' : 'negative';
  };

  return (
    <div className="modifiers-table-container">
      <div className="modifiers-table-header">
        <h2>{title}</h2>
        <div className="total-effect">
          Total: <span className={getEffectClass(totalEffect)}>{formatInteger(totalEffect)}</span>
        </div>
      </div>

      <table className="modifiers-table">
        <thead>
          <tr>
            <th>Intitulé</th>
            <th>Effet</th>
            <th>Description</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {modifiers.map(modifier => (
            <tr key={modifier.id}>
              {editingModifier && editingModifier.id === modifier.id ? (
                // Edit mode
                <>
                  <td>
                    <input
                      type="text"
                      value={editingModifier.title}
                      onChange={e => setEditingModifier({...editingModifier, title: e.target.value})}
                      onKeyDown={handleEditKeyPress}
                      autoFocus
                    />
                  </td>
                  <td>
                    <input
                      type="number"
                      value={editingModifier.effect}
                      onChange={e => setEditingModifier({...editingModifier, effect: e.target.value})}
                      onKeyDown={handleEditKeyPress}
                      step="1"
                      style={{ width: '80px' }}
                    />
                  </td>
                  <td>
                    <input
                      type="text"
                      value={editingModifier.description || ''}
                      onChange={e => setEditingModifier({...editingModifier, description: e.target.value})}
                      onKeyDown={handleEditKeyPress}
                      style={{ width: '100%' }}
                    />
                  </td>
                  <td>
                    <button
                      className="save-btn"
                      onClick={handleSaveEdit}
                    >
                      Sauvegarder
                    </button>
                    <button
                      className="cancel-btn"
                      onClick={handleCancelEdit}
                    >
                      Annuler
                    </button>
                  </td>
                </>
              ) : (
                // View mode
                <>
                  <td>{modifier.title}</td>
                  <td>
                    <span className={getEffectClass(modifier.effect)}>{formatInteger(modifier.effect)}</span>
                  </td>
                  <td>{modifier.description}</td>
                  <td>
                    <div className="action-buttons">
                      <button
                        className="edit-btn"
                        onClick={() => handleEditModifier(modifier)}
                      >
                        Modifier
                      </button>
                      <button
                        className="delete-btn"
                        onClick={() => handleDeleteModifier(modifier.id)}
                      >
                        Supprimer
                      </button>
                    </div>
                  </td>
                </>
              )}
            </tr>
          ))}
        </tbody>
      </table>

      {!showAddForm ? (
        <div className="add-modifier-button-container">
          <button
            className="add-btn"
            onClick={() => setShowAddForm(true)}
          >
            + Ajouter un modificateur
          </button>
        </div>
      ) : (
        <div className="add-modifier-form">
          <h3>Ajouter un modificateur</h3>
          <div className="form-row">
            <div className="form-group">
              <label>Intitulé:</label>
              <input
                type="text"
                value={newModifier.title}
                onChange={e => setNewModifier({...newModifier, title: e.target.value})}
                onKeyDown={handleNewModifierKeyPress}
                autoFocus
              />
            </div>
            <div className="form-group">
              <label>Effet:</label>
              <input
                type="number"
                value={newModifier.effect}
                onChange={e => setNewModifier({...newModifier, effect: e.target.value})}
                onKeyDown={handleNewModifierKeyPress}
                step="1"
              />
            </div>
          </div>
          <div className="form-group">
            <label>Description:</label>
            <input
              type="text"
              value={newModifier.description}
              onChange={e => setNewModifier({...newModifier, description: e.target.value})}
              onKeyDown={handleNewModifierKeyPress}
            />
          </div>
          <div className="form-buttons">
            <button
              className="add-btn"
              onClick={handleAddModifier}
            >
              Ajouter
            </button>
            <button
              className="cancel-btn"
              onClick={() => {
                setShowAddForm(false);
                setNewModifier({
                  title: '',
                  effect: 0,
                  description: ''
                });
              }}
            >
              Annuler
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ModifiersTableInteger;
