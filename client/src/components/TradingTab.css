.trading-tab {
  padding: 0;
}

.trading-stats-cards {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 20px;
}

.trading-stat-card {
  background-color: #f5f5f5;
  border-radius: 8px;
  padding: 15px;
  width: 220px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.trading-stat-icon {
  font-size: 2rem;
  margin-bottom: 10px;
}

.trading-stat-title {
  font-weight: bold;
  margin-bottom: 5px;
}

.trading-stat-value {
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 5px;
  color: #2c3e50;
}

.positive {
  color: #27ae60;
}

.negative {
  color: #e74c3c;
}

.trading-stat-description {
  font-size: 0.8rem;
  color: #7f8c8d;
}

.trading-info-card {
  background-color: #f5f5f5;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.trading-info-card h3 {
  margin-top: 0;
  margin-bottom: 10px;
  color: #2c3e50;
}

.trading-info-card p {
  margin-bottom: 10px;
  line-height: 1.5;
}

.modifiers-tables {
  margin-top: 20px;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  font-size: 1.2rem;
  color: #7f8c8d;
}

.error-message {
  color: #e74c3c;
  padding: 15px;
  background-color: #fadbd8;
  border-radius: 5px;
  margin-bottom: 20px;
}
