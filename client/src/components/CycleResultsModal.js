import React from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { setShowCycleModal } from '../store/slices/uiSlice';
import { selectCycleResults, selectGameState } from '../store/slices/gameSlice';
import './CycleResultsModal.css';

const CycleResultsModal = ({ isOpen, onClose }) => {
  const dispatch = useDispatch();
  const cycleResults = useSelector(selectCycleResults);
  const gameState = useSelector(selectGameState);

  if (!isOpen || !cycleResults || !gameState) return null;

  const handleClose = () => {
    if (onClose) {
      onClose();
    } else {
      dispatch(setShowCycleModal(false));
    }
  };

  const formatNumber = (num, decimals = 0) => {
    if (decimals === 0) {
      return Math.round(num).toLocaleString();
    } else {
      return num.toFixed(decimals);
    }
  };

  return (
    <div className="cycle-modal-overlay">
      <div className="cycle-modal">
        <div className="cycle-modal-header">
          <h2>Résultats du Cycle</h2>
          <button className="close-button" onClick={handleClose}>×</button>
        </div>

        <div className="cycle-modal-content">
          {/* Events Section */}
          <div className="cycle-section">
            <h3>Événements</h3>
            {cycleResults.currentCycleEvents && cycleResults.currentCycleEvents.length > 0 ? (
              <ul className="events-list">
                {cycleResults.currentCycleEvents.map((event, index) => (
                  <li key={index} className={`event-item ${event.type.toLowerCase()}`}>
                    <span className="event-icon">
                      {event.type === 'SICKNESS' && '🤒'}
                      {event.type === 'CRIME' && '🔪'}
                      {event.type === 'RESEARCH' && '💡'}
                      {event.type === 'FAMINE' && '🍽️'}
                      {event.type === 'TREASURY' && '💰'}
                    </span>
                    <span className="event-description">{event.description}</span>
                  </li>
                ))}
              </ul>
            ) : (
              <p>Pas d'événement marquant durant ce cycle.</p>
            )}
          </div>

          {/* Resources Changes */}
          <div className="cycle-section">
            <h3>Changements de Ressources</h3>
            <div className="resources-changes">
              <div className="resource-change">
                <h4>Finances</h4>
                <div className="resource-details">
                  <div className="resource-detail">
                    <span>Revenus miniers:</span>
                    <span className="positive">+{formatNumber(cycleResults.calculations.mining.miningProduction)}</span>
                  </div>
                  <div className="resource-detail">
                    <span>Revenus commerciaux:</span>
                    <span className="positive">+{formatNumber(cycleResults.calculations.trading.revenue)}</span>
                  </div>
                  <div className="resource-detail">
                    <span>Charges non salariales:</span>
                    <span className="negative">{formatNumber(cycleResults.calculations.finances.nonSalaryCharges)}</span>
                  </div>
                  <div className="resource-detail">
                    <span>Salaires:</span>
                    <span className="negative">{formatNumber(cycleResults.calculations.finances.totalSalary)}</span>
                  </div>
                  <div className="resource-detail total">
                    <span>Bilan:</span>
                    <span className={cycleResults.calculations.finances.totalRevenue - cycleResults.calculations.finances.totalCharges > 0 ? "positive" : "negative"}>
                      {formatNumber(cycleResults.calculations.finances.totalRevenue - cycleResults.calculations.finances.totalCharges)}
                    </span>
                  </div>
                </div>
              </div>

              <div className="resource-change">
                <h4>Matériaux</h4>
                <div className="resource-details">
                  <div className="resource-detail">
                    <span>Production:</span>
                    <span className="positive">+{formatNumber(cycleResults.calculations.materials.production)}</span>
                  </div>
                  <div className="resource-detail total">
                    <span>Total:</span>
                    <span>{formatNumber(gameState.materials || 0)}</span>
                  </div>
                </div>
              </div>

              <div className="resource-change">
                <h4>Nourriture</h4>
                <div className="resource-details">
                  <div className="resource-detail">
                    <span>Production:</span>
                    <span className="positive">+{formatNumber(cycleResults.calculations.food.production)}</span>
                  </div>
                  <div className="resource-detail">
                    <span>Consommation:</span>
                    <span className="negative">-{formatNumber(cycleResults.calculations.food.consumption)}</span>
                  </div>
                  <div className="resource-detail">
                    <span>Péremption:</span>
                    <span className="negative">-{formatNumber(cycleResults.calculations.food.perishableLoss)}</span>
                  </div>
                  <div className="resource-detail total">
                    <span>Bilan:</span>
                    <span className={cycleResults.calculations.food.netAfterPerishable > 0 ? "positive" : "negative"}>
                      {formatNumber(cycleResults.calculations.food.netAfterPerishable)}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Probabilities */}
          <div className="cycle-section">
            <h3>Probabilités pour le prochain cycle</h3>
            <div className="probabilities">
              <div className="probability">
                <span>Maladie:</span>
                <span>{(cycleResults.calculations.probabilities.sick * 100).toFixed(1)}%</span>
              </div>
              <div className="probability">
                <span>Crime:</span>
                <span>{(cycleResults.calculations.probabilities.crime * 100).toFixed(1)}%</span>
              </div>
              <div className="probability">
                <span>Découverte:</span>
                <span>{(cycleResults.calculations.probabilities.research * 100).toFixed(1)}%</span>
              </div>
            </div>
          </div>

          {/* Calendar Update */}
          <div className="cycle-section">
            <h3>Calendrier</h3>
            <div className="calendar-update">
              <p>Nous sommes maintenant au mois de <strong>{gameState.month}</strong> de l'an <strong>{gameState.year}</strong>.</p>
              <p>Saison actuelle: <strong>{gameState.season}</strong> (Facteur: {(gameState.season_factor * 100)}%)</p>
            </div>
          </div>
        </div>

        <div className="cycle-modal-footer">
          <button className="continue-button" onClick={handleClose}>Continuer</button>
        </div>
      </div>
    </div>
  );
};

export default CycleResultsModal;
