import React, { useEffect } from 'react';
import { useSelector, useDispatch, useStore } from 'react-redux';
import ModifiersTable from './ModifiersTable';
import './ResearchTab.css';
import {
  fetchResearchModifiers,
  updateResearchStats,
  selectResearchModifiers,
  selectResearchStats,
  selectResearchLoading,
  selectResearchError
} from '../store/slices/researchSlice';
import { withRedux } from '../hoc/withRedux';

function ResearchTabRedux({ gameState, onResearchUpdated }) {
  const dispatch = useDispatch();
  const store = useStore();

  // Sélectionner les données depuis le store Redux
  const researchModifiers = useSelector(selectResearchModifiers);
  const researchStats = useSelector(selectResearchStats);
  const loading = useSelector(selectResearchLoading);
  const error = useSelector(selectResearchError);

  // Table ID for research modifiers
  const researchModifiersTableId = 16;

  // Charger les modificateurs au montage du composant
  useEffect(() => {
    dispatch(fetchResearchModifiers());
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Mettre à jour les statistiques de recherche quand gameState ou researchModifiers change
  useEffect(() => {
    if (gameState) {
      console.log('ResearchTabRedux: Mise à jour des statistiques de recherche', {
        gameState: !!gameState,
        researchModifiers: researchModifiers
      });

      // Forcer une mise à jour complète des statistiques de recherche
      dispatch(updateResearchStats({
        gameState,
        researchModifiers
      }));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [gameState, researchModifiers]);

  // Fonction pour recharger les modificateurs (utilisée par les tables de modificateurs)
  const handleModifierUpdated = () => {
    console.log('ResearchTabRedux: handleModifierUpdated appelé');

    // Recharger immédiatement les modificateurs
    dispatch(fetchResearchModifiers()).then(() => {
      console.log('ResearchTabRedux: Modificateurs rechargés après mise à jour');

      // Récupérer les modificateurs mis à jour
      const updatedModifiers = selectResearchModifiers(store.getState());
      console.log('ResearchTabRedux: Modificateurs mis à jour récupérés:', updatedModifiers);

      // Mettre à jour les statistiques de recherche avec le gameState actuel et les modificateurs mis à jour
      if (gameState) {
        dispatch(updateResearchStats({
          gameState,
          researchModifiers: updatedModifiers
        }));
        console.log('ResearchTabRedux: Research stats updated after modifier change');
      }

      // Notifier le parent si nécessaire
      if (onResearchUpdated) {
        console.log('ResearchTabRedux: Notification du parent que la recherche a été mise à jour');
        onResearchUpdated();
      }
    });
  };

  // Format probability as percentage with more precision
  const formatProbability = (value) => {
    return `${(value * 100).toFixed(2)}%`;
  };

  if (loading) {
    return <div className="loading">Loading research data...</div>;
  }

  if (error) {
    return <div className="error-message">{error}</div>;
  }

  return (
    <div className="research-tab">
      <h2>Gestion de la Recherche</h2>

      {/* Research Stats Cards */}
      <div className="research-stats-cards">
        <div className="research-stat-card">
          <div className="research-stat-icon">🧪</div>
          <div className="research-stat-title">Niveau Technologique</div>
          <div className="research-stat-value">{researchStats.techLevel}</div>
          <div className="research-stat-description">Niveau actuel de technologie</div>
        </div>

        <div className="research-stat-card">
          <div className="research-stat-icon">🧠</div>
          <div className="research-stat-title">Érudits Actifs</div>
          <div className="research-stat-value">{researchStats.activeScholars}</div>
          <div className="research-stat-description">Nombre d'érudits travaillant sur la recherche</div>
        </div>

        <div className="research-stat-card">
          <div className="research-stat-icon">💡</div>
          <div className="research-stat-title">Probabilité de Découverte</div>
          <div className="research-stat-value">{formatProbability(researchStats.researchProbability)}</div>
          <div className="research-stat-description">Chance de découverte au prochain cycle</div>
        </div>
      </div>

      {/* Research Information Card */}
      <div className="research-info-card">
        <h3>Système de Recherche</h3>
        <p>Les érudits travaillent sur des projets de recherche qui peuvent aboutir à des découvertes technologiques. À chaque cycle, il y a une chance qu'une découverte soit faite, en fonction du nombre d'érudits actifs et des modificateurs de recherche.</p>
        <p>Le niveau technologique augmente lorsque vous construisez de nouveaux bâtiments.</p>
      </div>

      {/* Research Modifiers Table */}
      <div className="modifiers-tables">
        <ModifiersTable
          title="Modificateurs de recherche"
          tableId={researchModifiersTableId}
          modifiers={researchModifiers || []}
          onModifierUpdated={handleModifierUpdated}
        />
      </div>
    </div>
  );
}

// Connecter le composant à Redux tout en maintenant la compatibilité avec les props
export default withRedux(
  ResearchTabRedux,
  (state) => ({
    // Mapper l'état Redux aux props
    researchModifiers: selectResearchModifiers(state),
    researchStats: selectResearchStats(state),
    loading: selectResearchLoading(state),
    error: selectResearchError(state)
  }),
  (dispatch) => ({
    // Mapper les actions Redux aux props
    fetchResearchModifiers: () => dispatch(fetchResearchModifiers()),
    updateResearchStats: (gameState) => dispatch(updateResearchStats(gameState))
  })
);
