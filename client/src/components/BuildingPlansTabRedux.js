import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { 
  fetchBuildingPlans, 
  selectBuildingPlans, 
  selectBuildingsLoading, 
  selectBuildingsError 
} from '../store/slices/buildingsSlice';
import './BuildingPlansTab.css';

const BuildingPlansTabRedux = () => {
  const dispatch = useDispatch();
  const buildingPlans = useSelector(selectBuildingPlans);
  const loading = useSelector(selectBuildingsLoading);
  const error = useSelector(selectBuildingsError);

  useEffect(() => {
    dispatch(fetchBuildingPlans());
  }, [dispatch]);

  if (loading) {
    return <div className="loading">Chargement des plans de bâtiments...</div>;
  }

  if (error) {
    return <div className="error">Erreur: {error}</div>;
  }

  // Grouper les plans par type
  const plansByType = buildingPlans.reduce((acc, plan) => {
    if (!acc[plan.type]) {
      acc[plan.type] = [];
    }
    acc[plan.type].push(plan);
    return acc;
  }, {});

  return (
    <div className="building-plans-tab">
      <h2>Plans de Bâtiments</h2>
      
      <div className="plans-description">
        <p>Cette section présente tous les plans de bâtiments disponibles dans le jeu. Pour démarrer la construction d'un bâtiment, rendez-vous dans l'onglet Construction.</p>
      </div>
      
      {Object.keys(plansByType).length > 0 ? (
        Object.entries(plansByType).map(([type, plans]) => (
          <div key={type} className="plans-category">
            <h3 className="category-title">{type}</h3>
            <div className="plans-grid">
              {plans.map((plan) => (
                <div key={plan.id} className="plan-card">
                  <div className="plan-header">
                    <h4 className="plan-name">{plan.name}</h4>
                  </div>
                  <div className="plan-details">
                    <div className="plan-stat">
                      <span className="stat-label">Coût:</span>
                      <span className="stat-value">{plan.cost} pièces</span>
                    </div>
                    <div className="plan-stat">
                      <span className="stat-label">Temps:</span>
                      <span className="stat-value">{plan.build_time} cycles</span>
                    </div>
                    <div className="plan-stat">
                      <span className="stat-label">Ouvriers:</span>
                      <span className="stat-value">{plan.workers_required}</span>
                    </div>
                    {plan.tech_level_required > 0 && (
                      <div className="plan-stat">
                        <span className="stat-label">Niveau tech:</span>
                        <span className="stat-value">{plan.tech_level_required}</span>
                      </div>
                    )}
                  </div>
                  <div className="plan-description">{plan.description}</div>
                  <div className="plan-effects">
                    <h5>Effets</h5>
                    <ul>
                      {plan.effects && plan.effects.map((effect, index) => (
                        <li key={index}>{effect}</li>
                      ))}
                      {(!plan.effects || plan.effects.length === 0) && (
                        <li>Aucun effet spécial</li>
                      )}
                    </ul>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ))
      ) : (
        <div className="no-plans">Aucun plan de bâtiment disponible.</div>
      )}
    </div>
  );
};

export default BuildingPlansTabRedux;
