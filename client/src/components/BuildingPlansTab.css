.building-plans-tab {
  padding: 20px;
}

.plans-description {
  background-color: white;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.plans-description p {
  margin: 0;
  color: #666;
}

.plans-category {
  margin-bottom: 30px;
}

.category-title {
  margin-bottom: 15px;
  padding-bottom: 5px;
  border-bottom: 1px solid #ddd;
  color: #333;
}

.plans-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.plan-card {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s;
}

.plan-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.plan-header {
  margin-bottom: 15px;
}

.plan-name {
  margin: 0;
  color: #333;
}

.plan-details {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
  margin-bottom: 15px;
}

.plan-stat {
  display: flex;
  flex-direction: column;
}

.stat-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 3px;
}

.stat-value {
  font-size: 14px;
  font-weight: bold;
  color: #333;
}

.plan-description {
  font-size: 14px;
  color: #666;
  margin-bottom: 15px;
}

.plan-effects h5 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 14px;
  color: #333;
}

.plan-effects ul {
  margin: 0;
  padding-left: 20px;
}

.plan-effects li {
  font-size: 14px;
  color: #666;
  margin-bottom: 5px;
}

.no-plans {
  text-align: center;
  padding: 30px;
  color: #666;
  font-style: italic;
}

.loading, .error {
  text-align: center;
  padding: 30px;
  color: #666;
}
