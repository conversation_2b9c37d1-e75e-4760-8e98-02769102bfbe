.buildings-tab {
  padding: 20px;
}

.buildings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.building-card {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s;
}

.building-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.building-header {
  margin-bottom: 15px;
}

.building-name {
  margin: 0 0 5px 0;
  color: #333;
}

.building-type {
  font-size: 14px;
  color: #666;
}

.building-details {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 15px;
}

.building-stat {
  display: flex;
  flex-direction: column;
}

.stat-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 3px;
}

.stat-value {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.building-description {
  font-size: 14px;
  color: #666;
  margin-bottom: 15px;
}

.building-actions {
  display: flex;
  gap: 10px;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.btn-primary {
  background-color: #3498db;
  color: white;
}

.btn-primary:hover {
  background-color: #2980b9;
}

.btn-secondary {
  background-color: #ecf0f1;
  color: #333;
}

.btn-secondary:hover {
  background-color: #bdc3c7;
}

.no-buildings {
  text-align: center;
  padding: 30px;
  color: #666;
}

.loading, .error {
  text-align: center;
  padding: 30px;
  color: #666;
}
