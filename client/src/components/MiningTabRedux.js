import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import ModifiersTable from './ModifiersTable';
import { setMiningData } from '../store/slices/resourcesSlice';
import withRedux from '../hoc/withRedux';
import './MiningTab.css';
import {
  fetchMiningModifiers,
  updateMiningStats,
  selectMiningGeneralModifiers,
  selectEngineeringModifiers,
  selectMiningTechModifiers,
  selectMiningStats,
  selectMiningTotals,
  selectMiningLoading
} from '../store/slices/miningSlice';
import { selectMoralStats } from '../store/slices/moralSlice';
import { selectGameState } from '../store/slices/gameSlice';

/**
 * Version Redux du composant MiningTab
 * Cette version utilise Redux pour gérer son état
 *
 * @param {Object} props - Les propriétés du composant
 * @param {Object} props.gameState - L'état du jeu
 * @param {Function} props.updateMiningData - Fonction pour mettre à jour les données de la mine dans le composant parent
 * @returns {JSX.Element} - Le composant MiningTabRedux
 */
function MiningTabRedux({ gameState, updateMiningData, moralUpdated }) {
  const dispatch = useDispatch();

  // Sélectionner les données depuis le store Redux
  const miningGeneralModifiers = useSelector(selectMiningGeneralModifiers);
  const engineeringModifiers = useSelector(selectEngineeringModifiers);
  const miningTechModifiers = useSelector(selectMiningTechModifiers);
  const miningStats = useSelector(selectMiningStats);
  const totals = useSelector(selectMiningTotals);
  const moralStats = useSelector(selectMoralStats); // Récupérer les stats de moral depuis le store Redux

  // Table IDs
  const miningGeneralTableId = 4; // General mining effects
  const engineeringTableId = 5;   // Engineering effects
  const miningTechTableId = 6;    // Technology effects on mining

  // Charger les modificateurs au montage du composant et quand gameState ou moralUpdated changent
  useEffect(() => {
    console.log('MiningTabRedux: Chargement initial des modificateurs');
    dispatch(fetchMiningModifiers()).then(() => {
      // Après avoir chargé les modificateurs, mettre à jour les statistiques
      if (gameState && moralStats) {
        console.log('MiningTabRedux: Mise à jour des statistiques après chargement des modificateurs');
        dispatch(updateMiningStats({
          gameState,
          moralModifier: moralStats.modifier,
          moralTitle: moralStats.title
        }));
      }
    });
  }, [dispatch, gameState?.modifierTables, moralUpdated]);

  // Mettre à jour les statistiques de la mine quand gameState ou moralStats change
  useEffect(() => {
    if (gameState && moralStats) {
      console.log('MiningTabRedux: Mise à jour des statistiques de la mine avec moral:', moralStats);

      // Extraire les jobs correctement, qu'ils soient dans gameState.jobs ou gameState.gameState.jobs
      const jobs = Array.isArray(gameState.jobs) ? gameState.jobs :
                  (gameState.gameState && Array.isArray(gameState.gameState.jobs)) ? gameState.gameState.jobs : [];

      // Vérifier si nous avons des mineurs
      const miners = jobs.find(j => j.name === 'Miner') || { number: 0, sick: 0 };
      console.log('MiningTabRedux: Miners found:', miners);

      // Vérifier si nous avons des ingénieurs
      const engineers = jobs.find(j => j.name === 'Engineer') || { number: 0, sick: 0 };
      console.log('MiningTabRedux: Engineers found:', engineers);

      // Mettre à jour les statistiques de la mine avec un objet gameState complet
      dispatch(updateMiningStats({
        gameState: {
          jobs: gameState.jobs,
          gameState: gameState.gameState,
          modifierTables: gameState.modifierTables
        },
        moralModifier: moralStats.modifier,
        moralTitle: moralStats.title
      }));
    }
  }, [dispatch, gameState, moralStats]);

  // Utiliser les valeurs du store Redux pour le moral
  const moralTitle = moralStats?.title || 'Neutre';
  const moralModifier = moralStats?.modifier !== undefined ? moralStats.modifier : 0;

  // Log pour déboguer les valeurs - afficher l'état complet
  console.log('MiningTabRedux: FULL STATE:', {
    miningStats,
    totals,
    moralStats,
    gameState
  });

  // Format number as percentage
  const formatPercent = (value) => {
    if (value === undefined || value === null) return '0.0%';
    return `${(value * 100).toFixed(1)}%`;
  };

  // Format number with 1 decimal place
  const formatNumber = (value) => {
    if (value === undefined || value === null) return '0.0';
    return parseFloat(value).toFixed(1);
  };

  // Get emoji for moral title
  const getMoralEmoji = (moralTitle) => {
    if (!moralTitle) return '😐';

    if (moralTitle.includes('Rébellion')) return '🤬';
    if (moralTitle.includes('En colère')) return '😠';
    if (moralTitle.includes('Triste')) return '😢';
    if (moralTitle.includes('Mécontent')) return '😒';
    if (moralTitle.includes('Neutre')) return '😐';
    if (moralTitle.includes('Content')) return '😊';
    if (moralTitle.includes('Epanoui')) return '😄';
    if (moralTitle.includes('Heureux')) return '😁';
    if (moralTitle.includes('Ecstatique')) return '🥳';

    return '😐'; // Default value
  };

  // Fonction pour recharger les modificateurs (utilisée par les tables de modificateurs)
  const handleModifierUpdated = () => {
    console.log('MiningTabRedux: handleModifierUpdated appelé');

    // Recharger immédiatement les modificateurs
    dispatch(fetchMiningModifiers()).then(() => {
      console.log('MiningTabRedux: Modificateurs rechargés après mise à jour');

      // Mettre à jour les statistiques de la mine avec le gameState actuel et les données de moral
      if (gameState && moralStats) {
        console.log('MiningTabRedux: Mise à jour des statistiques avec moral après modification:', moralStats);
        dispatch(updateMiningStats({
          gameState: {
            jobs: gameState.jobs,
            gameState: gameState.gameState,
            modifierTables: gameState.modifierTables
          },
          moralModifier: moralStats.modifier,
          moralTitle: moralStats.title
        }));
      }
    });
  };

  // Update mining data in parent component if the function is provided
  // (gardé pour la compatibilité avec l'approche par props)
  useEffect(() => {
    if (miningStats) {
      // Always update parent component data if the function is provided
      if (updateMiningData) {
        console.log('MiningTabRedux: Updating parent component data:', miningStats);
        updateMiningData({
          ...miningStats,
          production: miningStats.production || 0,
          miners: miningStats.miners || 0,
          revenuePerMiner: miningStats.revenuePerMiner || 0,
          modifiers: {
            generalEffectsTotal: totals.generalEffectsTotal || 0,
            engineeringEffectsTotal: totals.engineeringEffectsTotal || 0,
            techEffectsTotal: totals.techEffectsTotal || 0,
            engineeringMultiplier: totals.engineeringMultiplier || 0,
            totalProductionBonus: totals.totalProductionBonus || 0
          }
        });
      }

      // Dispatch to resourcesSlice for backward compatibility
      dispatch(setMiningData({
        miners: miningStats.miners || 0,
        production: miningStats.production || 0,
        revenuePerMiner: miningStats.revenuePerMiner || 0,
        totalProduction: miningStats.production || 0,
        moralModifier: miningStats.moralModifier || 0
      }));
    }
  }, [miningStats, totals, dispatch, updateMiningData]);

  return (
    <div className="mining-tab">
      <h2>Minage</h2>

      {/* Mining Stats Cards */}
      <div className="mining-stats-cards">
        <div className="mining-stat-card">
          <div className="mining-stat-icon">⛏️</div>
          <div className="mining-stat-title">Mineurs actifs</div>
          <div className="mining-stat-value">{miningStats?.miners || 0} mineurs</div>
          <div className="mining-stat-description">Nombre de mineurs au travail</div>
        </div>

        <div className="mining-stat-card">
          <div className="mining-stat-icon">🧰</div>
          <div className="mining-stat-title">Revenus par mineur</div>
          <div className="mining-stat-value">{formatNumber(miningStats?.revenuePerMiner)} pièces/cycle</div>
          <div className="mining-stat-description">Incluant tous les bonus</div>
        </div>

        <div className="mining-stat-card">
          <div className="mining-stat-icon">🔧</div>
          <div className="mining-stat-title">Bonus d'ingénierie</div>
          <div className="mining-stat-value positive">{formatPercent(totals?.engineeringMultiplier)}</div>
          <div className="mining-stat-description">Bonus des ingénieurs</div>
        </div>

        <div className="mining-stat-card">
          <div className="mining-stat-icon">📈</div>
          <div className="mining-stat-title">Effet des modificateurs</div>
          <div className="mining-stat-value positive">{formatPercent((totals?.generalEffectsTotal || 0) + (totals?.techEffectsTotal || 0))}</div>
          <div className="mining-stat-description">Bonus total à la production</div>
        </div>

        <div className="mining-stat-card">
          <div className="mining-stat-icon">{getMoralEmoji(moralTitle)}</div>
          <div className="mining-stat-title">Moral: {moralTitle}</div>
          <div className={`mining-stat-value ${moralModifier >= 0 ? 'positive' : 'negative'}`}>
            {moralModifier >= 0 ? '+' : ''}{formatPercent(moralModifier).replace('%', '')}%
          </div>
          <div className="mining-stat-description">Impact du moral sur la production</div>
        </div>

        <div className="mining-stat-card">
          <div className="mining-stat-icon">💰</div>
          <div className="mining-stat-title">Production totale</div>
          <div className="mining-stat-value">{formatNumber(miningStats?.production)} pièces/cycle</div>
          <div className="mining-stat-description">Revenus de la mine par cycle</div>
        </div>
      </div>

      {/* Tables de modificateurs */}
      <div className="modifiers-tables">
        <ModifiersTable
          title="Effets généraux sur la mine"
          tableId={miningGeneralTableId}
          modifiers={miningGeneralModifiers}
          onModifierUpdated={handleModifierUpdated}
        />

        <ModifiersTable
          title="Ingénierie"
          tableId={engineeringTableId}
          modifiers={engineeringModifiers}
          onModifierUpdated={handleModifierUpdated}
        />

        <ModifiersTable
          title="Technologies qui affectent la mine"
          tableId={miningTechTableId}
          modifiers={miningTechModifiers}
          onModifierUpdated={handleModifierUpdated}
        />
      </div>
    </div>
  );
}

// Connecter le composant à Redux tout en maintenant la compatibilité avec les props
export default withRedux(
  MiningTabRedux,
  {
    // Sélecteurs individuels pour chaque prop
    miningDataFromRedux: state => state.resources.mining,
    miningGeneralModifiers: selectMiningGeneralModifiers,
    engineeringModifiers: selectEngineeringModifiers,
    miningTechModifiers: selectMiningTechModifiers,
    miningStats: selectMiningStats,
    totals: selectMiningTotals,
    loading: selectMiningLoading
  },
  {
    // Actions Redux
    updateMiningDataRedux: setMiningData,
    fetchMiningModifiers,
    updateMiningStats
  }
);
