import React, { useState } from 'react';
import { useDispatch } from 'react-redux';
import './ModifiersTable.css';
import { addModifier, updateModifier, deleteModifier } from '../services/ModifiersService';
import { fetchChargesModifiers } from '../store/slices/chargesSlice';

const ModifiersTableAbsolute = ({ title = 'Modificateurs', tableId = 0, modifiers = [], onModifierUpdated = () => {} }) => {
  const dispatch = useDispatch();
  // État local pour stocker les modificateurs
  const [localModifiers, setLocalModifiers] = useState(modifiers || []);
  const [newModifier, setNewModifier] = useState({
    title: '',
    effect: 0,
    description: ''
  });
  const [editingModifier, setEditingModifier] = useState(null);
  const [showAddForm, setShowAddForm] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Synchroniser l'état local avec les props
  React.useEffect(() => {
    setLocalModifiers(modifiers || []);
  }, [modifiers]);

  // Format number with 2 decimal places
  const formatNumber = (value) => {
    return value.toFixed(2);
  };

  // Handle adding a new modifier
  const handleAddModifier = async () => {
    if (!newModifier.title || newModifier.effect === '') {
      alert('Veuillez remplir tous les champs obligatoires');
      return;
    }

    if (isSubmitting) {
      return; // Prevent multiple submissions
    }

    setIsSubmitting(true);

    try {
      const modifierData = {
        title: newModifier.title,
        effect: parseFloat(newModifier.effect), // Use absolute value, not percentage
        description: newModifier.description || ''
      };

      console.log(`ModifiersTableAbsolute: Ajout d'un modificateur à la table ${tableId}`, modifierData);

      // Utiliser directement le service pour garantir une mise à jour immédiate
      const result = await addModifier(tableId, modifierData);
      console.log(`ModifiersTableAbsolute: Résultat de l'ajout du modificateur:`, result);

      // Ajouter le modificateur à l'état local immédiatement
      const newModifierWithId = result.modifier;
      setLocalModifiers(prev => [...prev, newModifierWithId]);

      // Si c'est une table de charges, forcer un rechargement des données Redux
      if (tableId === 8) { // CHARGES_GLOBAL_TABLE_ID
        dispatch(fetchChargesModifiers());
      }

      // Reset the form
      setNewModifier({
        title: '',
        effect: 0,
        description: ''
      });

      // Hide the form
      setShowAddForm(false);

      // Pour les tables de charges, forcer un rechargement de la page et rediriger vers l'onglet charges
      if (tableId === 7 || tableId === 8) {
        console.log(`ModifiersTableAbsolute: Rechargement de la page pour la table de charges ${tableId}`);
        // Sauvegarder l'onglet actuel dans le localStorage
        localStorage.setItem('activeTab', 'charges');
        window.location.reload();
      } else {
        // Pour les autres tables, notifier le parent normalement
        console.log(`ModifiersTableAbsolute: Appel de onModifierUpdated`);
        onModifierUpdated();
      }
    } catch (error) {
      console.error('Erreur lors de l\'ajout du modificateur:', error);
      alert(`Échec de l'ajout du modificateur: ${error.message || 'Erreur inconnue'}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle editing a modifier
  const handleEditModifier = (modifier) => {
    setEditingModifier({
      ...modifier,
      effect: modifier.effect // Keep as absolute value
    });
  };

  // Handle saving edited modifier
  const handleSaveEdit = async () => {
    if (!editingModifier || !editingModifier.title || editingModifier.effect === '') {
      alert('Veuillez remplir tous les champs obligatoires');
      return;
    }

    if (isSubmitting) {
      return; // Prevent multiple submissions
    }

    setIsSubmitting(true);

    try {
      // Utiliser le service ModifiersService pour mettre à jour le modificateur
      const modifierData = {
        title: editingModifier.title,
        effect: parseFloat(editingModifier.effect), // Use absolute value, not percentage
        description: editingModifier.description || ''
      };

      console.log(`ModifiersTableAbsolute: Mise à jour du modificateur ${editingModifier.id}`, modifierData);

      await updateModifier(editingModifier.id, modifierData);
      console.log(`ModifiersTableAbsolute: Modificateur mis à jour avec succès`);

      // Reset editing state
      setEditingModifier(null);

      // Pour les tables de charges, forcer un rechargement de la page et rediriger vers l'onglet charges
      if (tableId === 7 || tableId === 8) {
        console.log(`ModifiersTableAbsolute: Rechargement de la page pour la table de charges ${tableId}`);
        // Sauvegarder l'onglet actuel dans le localStorage
        localStorage.setItem('activeTab', 'charges');
        window.location.reload();
      } else {
        // Pour les autres tables, notifier le parent normalement
        console.log(`ModifiersTableAbsolute: Appel de onModifierUpdated après modification`);
        onModifierUpdated();
      }
    } catch (error) {
      console.error('Erreur lors de la mise à jour du modificateur:', error);
      alert('Échec de la mise à jour du modificateur. Veuillez réessayer.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle canceling edit
  const handleCancelEdit = () => {
    setEditingModifier(null);
  };

  // Handle key press in edit form
  const handleEditKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleSaveEdit();
    } else if (e.key === 'Escape') {
      handleCancelEdit();
    }
  };

  // Handle key press in new modifier form
  const handleNewModifierKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleAddModifier();
    }
  };

  // Handle deleting a modifier
  const handleDeleteModifier = async (modifierId) => {
    if (isSubmitting) {
      return; // Prevent multiple submissions
    }

    setIsSubmitting(true);

    try {
      console.log(`ModifiersTableAbsolute: Suppression du modificateur ${modifierId}`);

      // Utiliser directement le service pour garantir une mise à jour immédiate
      try {
        const result = await deleteModifier(modifierId);
        console.log(`ModifiersTableAbsolute: Modificateur supprimé avec succès`, result);

        // Supprimer le modificateur de l'état local immédiatement
        setLocalModifiers(prev => prev.filter(mod => mod.id !== modifierId));
      } catch (error) {
        // Si le modificateur n'est pas trouvé, c'est qu'il a déjà été supprimé
        // On considère que c'est un succès
        if (error.message && error.message.includes('Modifier not found')) {
          console.log(`ModifiersTableAbsolute: Le modificateur ${modifierId} a déjà été supprimé`);
          // Supprimer quand même de l'état local
          setLocalModifiers(prev => prev.filter(mod => mod.id !== modifierId));
        } else {
          // Pour les autres erreurs, on les propage
          throw error;
        }
      }

      // Si c'est une table de charges, forcer un rechargement des données Redux
      if (tableId === 8) { // CHARGES_GLOBAL_TABLE_ID
        dispatch(fetchChargesModifiers());
      }

      // Pour les tables de charges, forcer un rechargement de la page et rediriger vers l'onglet charges
      if (tableId === 7 || tableId === 8) {
        console.log(`ModifiersTableAbsolute: Rechargement de la page pour la table de charges ${tableId}`);
        // Sauvegarder l'onglet actuel dans le localStorage
        localStorage.setItem('activeTab', 'charges');
        window.location.reload();
      } else {
        // Pour les autres tables, notifier le parent normalement
        console.log(`ModifiersTableAbsolute: Appel de onModifierUpdated après suppression`);
        onModifierUpdated();
      }
    } catch (error) {
      console.error('Erreur lors de la suppression du modificateur:', error);
      alert(`Échec de la suppression du modificateur: ${error.message || 'Erreur inconnue'}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Calculate total effect - vérifier que modifiers est un tableau et que les effets sont définis
  const totalEffect = Array.isArray(localModifiers)
    ? localModifiers.reduce((sum, mod) => {
        // Vérifier que mod et mod.effect sont définis
        if (!mod) return sum;
        const effect = mod.effect !== undefined ? mod.effect : 0;
        return sum + effect;
      }, 0)
    : 0;

  return (
    <div className="modifiers-table-container">
      <div className="modifiers-table-header">
        <h2>{title}</h2>
        <div className="total-effect">
          Total: <span>{formatNumber(totalEffect)} or/cycle</span>
        </div>
      </div>

      <table className="modifiers-table">
        <thead>
          <tr>
            <th>Intitulé</th>
            <th>Coût (or/cycle)</th>
            <th>Description</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {Array.isArray(localModifiers) && localModifiers.filter(mod => mod).map(modifier => (
            <tr key={modifier.id}>
              {editingModifier && editingModifier.id === modifier.id ? (
                // Edit mode
                <>
                  <td>
                    <input
                      type="text"
                      value={editingModifier.title}
                      onChange={e => setEditingModifier({...editingModifier, title: e.target.value})}
                      onKeyDown={handleEditKeyPress}
                      autoFocus
                    />
                  </td>
                  <td>
                    <input
                      type="number"
                      value={editingModifier.effect}
                      onChange={e => setEditingModifier({...editingModifier, effect: e.target.value})}
                      onKeyDown={handleEditKeyPress}
                      step="0.01"
                      style={{ width: '80px' }}
                    />
                  </td>
                  <td>
                    <input
                      type="text"
                      value={editingModifier.description || ''}
                      onChange={e => setEditingModifier({...editingModifier, description: e.target.value})}
                      onKeyDown={handleEditKeyPress}
                      style={{ width: '100%' }}
                    />
                  </td>
                  <td>
                    <button
                      className="save-btn"
                      onClick={handleSaveEdit}
                    >
                      Sauvegarder
                    </button>
                    <button
                      className="cancel-btn"
                      onClick={handleCancelEdit}
                    >
                      Annuler
                    </button>
                  </td>
                </>
              ) : (
                // View mode
                <>
                  <td>{modifier.title}</td>
                  <td>{formatNumber(modifier.effect)}</td>
                  <td>{modifier.description}</td>
                  <td>
                    <div className="action-buttons">
                      <button
                        className="edit-btn"
                        onClick={() => handleEditModifier(modifier)}
                      >
                        Modifier
                      </button>
                      <button
                        className="delete-btn"
                        onClick={() => handleDeleteModifier(modifier.id)}
                      >
                        Supprimer
                      </button>
                    </div>
                  </td>
                </>
              )}
            </tr>
          ))}
        </tbody>
      </table>

      {!showAddForm ? (
        <div className="add-modifier-button-container">
          <button
            className="add-btn"
            onClick={() => setShowAddForm(true)}
          >
            + Ajouter un coût
          </button>
        </div>
      ) : (
        <div className="add-modifier-form">
          <h3>Ajouter un coût</h3>
          <div className="form-row">
            <div className="form-group">
              <label>Intitulé:</label>
              <input
                type="text"
                value={newModifier.title}
                onChange={e => setNewModifier({...newModifier, title: e.target.value})}
                onKeyDown={handleNewModifierKeyPress}
                autoFocus
              />
            </div>
            <div className="form-group">
              <label>Coût (or/cycle):</label>
              <input
                type="number"
                value={newModifier.effect}
                onChange={e => setNewModifier({...newModifier, effect: e.target.value})}
                onKeyDown={handleNewModifierKeyPress}
                step="0.01"
              />
            </div>
          </div>
          <div className="form-group">
            <label>Description:</label>
            <input
              type="text"
              value={newModifier.description}
              onChange={e => setNewModifier({...newModifier, description: e.target.value})}
              onKeyDown={handleNewModifierKeyPress}
            />
          </div>
          <div className="form-buttons">
            <button
              className="add-btn"
              onClick={handleAddModifier}
            >
              Ajouter
            </button>
            <button
              className="cancel-btn"
              onClick={() => {
                setShowAddForm(false);
                setNewModifier({
                  title: '',
                  effect: 0,
                  description: ''
                });
              }}
            >
              Annuler
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ModifiersTableAbsolute;
