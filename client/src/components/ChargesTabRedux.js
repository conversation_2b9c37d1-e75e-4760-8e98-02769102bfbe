import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import ModifiersTable from './ModifiersTable';
import ModifiersTableAbsolute from './ModifiersTableAbsolute';
import withRedux from '../hoc/withRedux';
import './ChargesTab.css';
import {
  fetchChargesModifiers,
  updateChargesStats,
  selectChargesGeneralModifiers,
  selectChargesGlobalModifiers,
  selectChargesStats,
  selectChargesTotals,
  selectChargesLoading
} from '../store/slices/chargesSlice';

// Table IDs
const CHARGES_GENERAL_TABLE_ID = 7;  // Coûts généraux
const CHARGES_GLOBAL_TABLE_ID = 8;   // Modificateur global des charges

/**
 * Version Redux du composant ChargesTab
 * Cette version utilise Redux pour gérer son état
 *
 * @param {Object} props - Les propriétés du composant
 * @param {Object} props.gameState - L'état du jeu
 * @param {Function} props.updateChargesData - Fonction pour mettre à jour les données des charges dans le composant parent
 * @returns {JSX.Element} - Le composant ChargesTabRedux
 */
function ChargesTabRedux({ gameState, updateChargesData }) {
  const dispatch = useDispatch();

  // Sélectionner les données depuis le store Redux
  const chargesGeneralModifiers = useSelector(selectChargesGeneralModifiers);
  const chargesGlobalModifiers = useSelector(selectChargesGlobalModifiers);
  const chargesStats = useSelector(selectChargesStats);
  const totals = useSelector(selectChargesTotals);

  // Table IDs
  const chargesGeneralTableId = 7;  // Coûts généraux
  const chargesGlobalTableId = 8;   // Modificateurs généraux des charges

  // Charger les modificateurs au montage du composant et quand gameState change
  useEffect(() => {
    dispatch(fetchChargesModifiers());
  }, [dispatch, gameState?.modifierTables]);

  // Mettre à jour les statistiques des charges quand gameState change
  useEffect(() => {
    if (gameState) {
      console.log('ChargesTabRedux: Updating charges stats with jobs:', gameState.jobs);

      // Calculer directement les salaires pour vérification
      const totalSalaries = Array.isArray(gameState.jobs)
        ? gameState.jobs.reduce((sum, job) => {
            const jobNumber = job.number || 0;
            const jobFree = job.free || 0;
            const jobSalary = job.salary || 0;
            const jobTotal = (jobNumber - jobFree) * jobSalary;
            console.log(`Job ${job.name}: ${jobNumber} - ${jobFree} = ${jobNumber - jobFree} × ${jobSalary} = ${jobTotal}`);
            return sum + jobTotal;
          }, 0)
        : 0;

      console.log('ChargesTabRedux: Calculated total salaries:', totalSalaries);

      // Utiliser un objet plus complet pour la mise à jour
      dispatch(updateChargesStats({
        gameState: {
          jobs: gameState.jobs,
          gameState: gameState.gameState
        }
      }));
    }
  }, [dispatch, gameState, gameState?.jobs]); // Ajouter une dépendance explicite à gameState.jobs

  // Recharger les modificateurs lorsque les jobs changent
  // eslint-disable-next-line react-hooks/exhaustive-deps
  useEffect(() => {
    if (gameState?.jobs) {
      // Recharger les modificateurs pour s'assurer qu'ils sont à jour
      dispatch(fetchChargesModifiers());

      // Forcer une mise à jour des statistiques
      dispatch(updateChargesStats({
        gameState: {
          jobs: gameState.jobs,
          gameState: gameState.gameState
        }
      }));
    }
  }, [dispatch, gameState?.jobs]);

  // S'assurer que les statistiques sont calculées après le chargement des modificateurs
  useEffect(() => {
    if (chargesGeneralModifiers.length > 0 && chargesGlobalModifiers.length > 0 && gameState) {
      console.log('Recalculating charges stats after modifiers loaded');
      dispatch(updateChargesStats({
        gameState: {
          jobs: gameState.jobs,
          gameState: gameState.gameState
        }
      }));
    }
  }, [dispatch, chargesGeneralModifiers, chargesGlobalModifiers, gameState]);

  // Format number as percentage
  const formatPercent = (value) => {
    if (value === undefined || value === null || isNaN(value)) return '0.0%';
    return `${(value * 100).toFixed(1)}%`;
  };

  // Format number with 1 or 2 decimal places
  const formatNumber = (value, decimals = 1) => {
    if (value === undefined || value === null || isNaN(value)) return '0.0';
    return parseFloat(value).toFixed(decimals);
  };

  // Fonction pour recharger les modificateurs et mettre à jour les statistiques
  const handleModifierUpdated = async () => {
    console.log('ChargesTabRedux: handleModifierUpdated called');

    try {
      // Dispatch l'action pour recharger les modificateurs
      await dispatch(fetchChargesModifiers());
      console.log('ChargesTabRedux: Modifiers fetched successfully');

      // Mettre à jour les statistiques si gameState est disponible
      if (gameState) {
        console.log('ChargesTabRedux: Updating charges stats');
        dispatch(updateChargesStats({
          gameState: {
            jobs: gameState.jobs,
            gameState: gameState.gameState
          }
        }));
      }
    } catch (error) {
      console.error('ChargesTabRedux: Error updating modifiers:', error);
    }
  };

  // S'abonner aux changements de cache du ModifiersService
  React.useEffect(() => {
    // Fonction de callback appelée lorsque le cache est invalidé
    const handleCacheInvalidated = (tableId) => {
      console.log(`ChargesTabRedux: Cache invalidated for table ${tableId}`);

      // Si c'est une des tables de charges ou toutes les tables, recharger les modificateurs
      if (tableId === CHARGES_GENERAL_TABLE_ID || tableId === CHARGES_GLOBAL_TABLE_ID || tableId === 'all') {
        console.log('ChargesTabRedux: Reloading modifiers due to cache invalidation');
        dispatch(fetchChargesModifiers());

        // Mettre à jour les statistiques si gameState est disponible
        if (gameState) {
          dispatch(updateChargesStats({
            gameState: {
              jobs: gameState.jobs,
              gameState: gameState.gameState
            }
          }));
        }
      }
    };

    // S'abonner aux changements de cache
    let unsubscribe = null;
    if (window.modifiersService && typeof window.modifiersService.subscribe === 'function') {
      unsubscribe = window.modifiersService.subscribe(handleCacheInvalidated);
      console.log('ChargesTabRedux: Subscribed to cache changes');
    }

    // Se désabonner lorsque le composant est démonté
    return () => {
      if (unsubscribe) {
        unsubscribe();
        console.log('ChargesTabRedux: Unsubscribed from cache changes');
      }
    };
  }, [dispatch, gameState]);

  // Update charges data in parent component if the function is provided
  // (gardé pour la compatibilité avec l'approche par props)
  useEffect(() => {
    if (chargesStats && updateChargesData) {
      console.log('ChargesTabRedux: Updating parent component data:', chargesStats);
      const dataToUpdate = {
        salaries: chargesStats.salaries || 0,
        nonSalaryCharges: chargesStats.nonSalaryCharges || 0,
        totalCharges: chargesStats.totalCharges || 0,
        craftsmanEffect: chargesStats.craftsmanEffect || 0, // Ajouter cette propriété
        baseCharges: chargesStats.baseCharges || 0, // Ajouter cette propriété
        modifiers: {
          chargesPerCycle: totals.chargesPerCycle || 0,
          chargesGlobalModifier: totals.chargesGlobalModifier || 0
        }
      };

      updateChargesData(dataToUpdate);
    }
  }, [chargesStats, totals, updateChargesData]);

  return (
    <div className="charges-tab">
      <h2>Charges</h2>

      {/* Charges Stats Cards */}
      <div className="charges-stats-cards">
        <div className="charges-stat-card">
          <div className="charges-stat-icon">💰</div>
          <div className="charges-stat-title">Charges fixes</div>
          <div className="charges-stat-value">
            {formatNumber(totals && totals.chargesPerCycle !== undefined ?
              totals.chargesPerCycle : 0, 2)} or/cycle
          </div>
          <div className="charges-stat-description">Somme des coûts généraux</div>
        </div>

        <div className="charges-stat-card">
          <div className="charges-stat-icon">👨‍💼</div>
          <div className="charges-stat-title">Effet des artisans</div>
          <div className="charges-stat-value positive">
            {formatPercent(chargesStats && chargesStats.craftsmanEffect ?
              (1/(1+chargesStats.craftsmanEffect))-1 : 0)}
          </div>
          <div className="charges-stat-description">Réduction des charges non salariales (Max: -50%)</div>
        </div>

        <div className="charges-stat-card">
          <div className="charges-stat-icon">📊</div>
          <div className="charges-stat-title">Modificateurs généraux</div>
          <div className={`charges-stat-value ${(totals && totals.chargesGlobalModifier <= 0) ? 'positive' : 'negative'}`}>
            {formatPercent(totals && totals.chargesGlobalModifier !== undefined ?
              (1/(1+(totals.chargesGlobalModifier * -1)))-1 : 0)}
          </div>
          <div className="charges-stat-description">Réduction des charges non salariales</div>
        </div>

        <div className="charges-stat-card">
          <div className="charges-stat-icon">📝</div>
          <div className="charges-stat-title">Charges non salariales</div>
          <div className="charges-stat-value negative">
            {formatNumber(chargesStats && chargesStats.nonSalaryCharges !== undefined ?
              chargesStats.nonSalaryCharges : 0)} or/cycle
          </div>
          <div className="charges-stat-description">Autres dépenses de la mine</div>
        </div>

        <div className="charges-stat-card">
          <div className="charges-stat-icon">💵</div>
          <div className="charges-stat-title">Salaires</div>
          <div className="charges-stat-value negative">
            {formatNumber(chargesStats && chargesStats.salaries !== undefined ?
              chargesStats.salaries : 0)} or/cycle
          </div>
          <div className="charges-stat-description">Total des salaires à payer</div>
        </div>

        <div className="charges-stat-card">
          <div className="charges-stat-icon">📉</div>
          <div className="charges-stat-title">Charges totales</div>
          <div className="charges-stat-value negative">
            {formatNumber(chargesStats && chargesStats.totalCharges !== undefined ?
              chargesStats.totalCharges : 0)} or/cycle
          </div>
          <div className="charges-stat-description">Dépenses totales par cycle</div>
        </div>
      </div>

      {/* Tables de modificateurs */}
      <div className="modifiers-tables">
        <ModifiersTableAbsolute
          title="Coûts généraux"
          tableId={chargesGeneralTableId}
          modifiers={chargesGeneralModifiers}
          onModifierUpdated={handleModifierUpdated}
        />

        <ModifiersTable
          title="Modificateurs généraux"
          tableId={chargesGlobalTableId}
          modifiers={chargesGlobalModifiers}
          onModifierUpdated={handleModifierUpdated}
        />
      </div>
    </div>
  );
}

// Connecter le composant à Redux tout en maintenant la compatibilité avec les props
export default withRedux(
  ChargesTabRedux,
  (state) => ({
    // Mapper l'état Redux aux props
    chargesGeneralModifiers: selectChargesGeneralModifiers(state),
    chargesGlobalModifiers: selectChargesGlobalModifiers(state),
    chargesStats: selectChargesStats(state),
    totals: selectChargesTotals(state),
    loading: selectChargesLoading(state)
  }),
  (dispatch) => ({
    // Mapper les actions Redux aux props
    fetchChargesModifiers: () => dispatch(fetchChargesModifiers()),
    updateChargesStats: (gameState) => dispatch(updateChargesStats(gameState))
  })
);
