import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import ModifiersTable from './ModifiersTable';
import './TradingTab.css';
import {
  fetchTradingModifiers,
  updateTradingStats,
  selectTradingModifiers,
  selectTradingStats,
  selectTradingLoading
} from '../store/slices/tradingSlice';
import { withRedux } from '../hoc/withRedux';

function TradingTabRedux({ gameState, onTradingUpdated }) {
  const dispatch = useDispatch();

  // Sélectionner les données depuis le store Redux
  const tradingModifiers = useSelector(selectTradingModifiers);
  const tradingStats = useSelector(selectTradingStats);
  const loading = useSelector(selectTradingLoading);

  // Table ID for trading modifiers
  const tradingModifiersTableId = 18;

  // Charger les modificateurs au montage du composant
  useEffect(() => {
    dispatch(fetchTradingModifiers());
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Mettre à jour les statistiques de commerce quand gameState change
  useEffect(() => {
    if (gameState) {
      dispatch(updateTradingStats({ gameState }));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [gameState, tradingModifiers]);

  // Fonction pour recharger les modificateurs (utilisée par les tables de modificateurs)
  const handleModifierUpdated = async () => {
    try {
      // Attendre un court instant pour s'assurer que les modifications sont bien enregistrées côté serveur
      await new Promise(resolve => setTimeout(resolve, 100));

      // Récupérer les modificateurs mis à jour
      await dispatch(fetchTradingModifiers()).unwrap();

      // Mettre à jour les statistiques avec les nouveaux modificateurs
      if (gameState) {
        dispatch(updateTradingStats({ gameState }));
      }

      // Notifier le parent si nécessaire
      if (onTradingUpdated) {
        onTradingUpdated();
      }
    } catch (error) {
      console.error('Error updating trading modifiers:', error);
    }
  };

  // Format currency value
  const formatCurrency = (value) => {
    if (value === undefined || value === null) return '0.00 or';
    return `${value.toFixed(2)} or`;
  };

  // Format percentage value
  const formatPercent = (value) => {
    if (value === undefined || value === null) return '0.0%';
    return `${(value * 100).toFixed(1)}%`;
  };

  if (loading) {
    return <div className="loading">Loading trading data...</div>;
  }

  // Calculate active craftsmen
  const activeCraftsmen = tradingStats.activeCraftsmen;

  // Get trading modifier value
  const tradingModifier = tradingStats.tradingModifier;
  const tradingValue = tradingStats.tradingValue;
  const tradingRevenue = tradingStats.tradingRevenue;
  const revenuePerCraftsman = tradingStats.revenuePerCraftsman;

  return (
    <div className="trading-tab">
      <h2>Gestion du Commerce</h2>

      {/* Trading Stats Cards */}
      <div className="trading-stats-cards">
        <div className="trading-stat-card">
          <div className="trading-stat-icon">⚖️</div>
          <div className="trading-stat-title">Revenus par Artisan</div>
          <div className="trading-stat-value">{formatCurrency(revenuePerCraftsman)}</div>
          <div className="trading-stat-description">{tradingValue} or de base</div>
        </div>

        <div className="trading-stat-card">
          <div className="trading-stat-icon">🧵</div>
          <div className="trading-stat-title">Artisans Actifs</div>
          <div className="trading-stat-value">{activeCraftsmen}</div>
          <div className="trading-stat-description">Nombre d'artisans travaillant sur le commerce</div>
        </div>

        <div className="trading-stat-card">
          <div className="trading-stat-icon">📈</div>
          <div className="trading-stat-title">Effet des modificateurs</div>
          <div className={`trading-stat-value ${tradingModifier >= 0 ? 'positive' : 'negative'}`}>
            {formatPercent(tradingModifier)}
          </div>
          <div className="trading-stat-description">Impact des modificateurs sur le commerce</div>
        </div>

        <div className="trading-stat-card">
          <div className="trading-stat-icon">💰</div>
          <div className="trading-stat-title">Revenus du Commerce</div>
          <div className="trading-stat-value">{formatCurrency(tradingRevenue)}</div>
          <div className="trading-stat-description">Revenus générés par cycle</div>
        </div>
      </div>

      {/* Trading Information Card */}
      <div className="trading-info-card">
        <h3>Système de Commerce</h3>
        <p><strong>Formule de calcul des revenus :</strong> Artisans Actifs × MAX(0, 1 + Modificateur de Commerce) × {tradingValue.toFixed(2)} or</p>
        <p>Les artisans créent des biens qui sont vendus pour générer des revenus. Chaque artisan génère {tradingValue.toFixed(2)} or par cycle, multiplié par le modificateur de commerce.</p>
      </div>

      {/* Trading Modifiers Table */}
      <div className="modifiers-tables">
        <ModifiersTable
          title="Modificateurs de commerce"
          tableId={tradingModifiersTableId}
          modifiers={tradingModifiers}
          onModifierUpdated={handleModifierUpdated}
        />
      </div>
    </div>
  );
}

// Connecter le composant à Redux tout en maintenant la compatibilité avec les props
export default withRedux(
  TradingTabRedux,
  (state) => ({
    // Mapper l'état Redux aux props
    tradingModifiers: selectTradingModifiers(state),
    tradingStats: selectTradingStats(state),
    loading: selectTradingLoading(state)
  }),
  (dispatch) => ({
    // Mapper les actions Redux aux props
    fetchTradingModifiers: () => dispatch(fetchTradingModifiers()),
    updateTradingStats: (gameState) => dispatch(updateTradingStats(gameState))
  })
);
