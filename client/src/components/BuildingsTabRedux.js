import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { 
  fetchBuildings, 
  selectBuildings, 
  selectBuildingsLoading, 
  selectBuildingsError 
} from '../store/slices/buildingsSlice';
import './BuildingsTab.css';

const BuildingsTabRedux = () => {
  const dispatch = useDispatch();
  const buildings = useSelector(selectBuildings);
  const loading = useSelector(selectBuildingsLoading);
  const error = useSelector(selectBuildingsError);

  useEffect(() => {
    dispatch(fetchBuildings());
  }, [dispatch]);

  if (loading) {
    return <div className="loading">Chargement des bâtiments...</div>;
  }

  if (error) {
    return <div className="error">Erreur: {error}</div>;
  }

  return (
    <div className="buildings-tab">
      <h2>Bâtiments</h2>
      
      {buildings && buildings.length > 0 ? (
        <div className="buildings-grid">
          {buildings.map((building) => (
            <div key={building.id} className="building-card">
              <div className="building-header">
                <h3 className="building-name">{building.name}</h3>
                <div className="building-type">{building.type}</div>
              </div>
              <div className="building-details">
                <div className="building-stat">
                  <span className="stat-label">Niveau:</span>
                  <span className="stat-value">{building.level}</span>
                </div>
                <div className="building-stat">
                  <span className="stat-label">État:</span>
                  <span className="stat-value">{building.condition}%</span>
                </div>
                <div className="building-stat">
                  <span className="stat-label">Capacité:</span>
                  <span className="stat-value">{building.capacity}</span>
                </div>
              </div>
              <div className="building-description">{building.description}</div>
              <div className="building-actions">
                <button className="btn btn-primary">Améliorer</button>
                <button className="btn btn-secondary">Réparer</button>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="no-buildings">
          <p>Aucun bâtiment n'a encore été construit.</p>
          <p>Commencez par construire des bâtiments dans l'onglet Construction.</p>
        </div>
      )}
    </div>
  );
};

export default BuildingsTabRedux;
