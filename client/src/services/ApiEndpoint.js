/**
 * Centralized API Endpoint Service
 * 
 * Provides a single point of access for all API endpoints.
 * Implements request deduplication, caching, and error handling.
 * Standardizes API request patterns across the application.
 */
import { BaseService } from './BaseService';
import cacheService from './CacheService';
import serviceRegistry from './ServiceRegistry';

// Default cache duration in milliseconds
const DEFAULT_CACHE_DURATION = 5000; // 5 seconds

class ApiEndpoint extends BaseService {
  constructor() {
    super('api');
    
    // Request tracking
    this._pendingRequests = new Map();
    this._requestQueue = [];
    this._processingQueue = false;
    
    // Cache configuration
    this._cacheDuration = DEFAULT_CACHE_DURATION;
    this._cacheEnabled = true;
    
    // Request statistics
    this._stats = {
      totalRequests: 0,
      cachedRequests: 0,
      deduplicatedRequests: 0,
      failedRequests: 0,
      successfulRequests: 0
    };
  }
  
  /**
   * Get API request statistics
   * @returns {Object} - Request statistics
   */
  getStats() {
    return { ...this._stats };
  }
  
  /**
   * Reset API request statistics
   */
  resetStats() {
    this._stats = {
      totalRequests: 0,
      cachedRequests: 0,
      deduplicatedRequests: 0,
      failedRequests: 0,
      successfulRequests: 0
    };
  }
  
  /**
   * Set cache duration
   * @param {number} duration - Cache duration in milliseconds
   */
  setCacheDuration(duration) {
    this._cacheDuration = duration;
  }
  
  /**
   * Enable or disable caching
   * @param {boolean} enabled - Whether caching is enabled
   */
  setCacheEnabled(enabled) {
    this._cacheEnabled = enabled;
  }
  
  /**
   * Make an API request with deduplication and caching
   * @param {string} endpoint - API endpoint
   * @param {string} method - HTTP method
   * @param {Object} data - Request data
   * @param {Object} options - Request options
   * @param {boolean} options.useCache - Whether to use cache
   * @param {boolean} options.forceRefresh - Whether to force refresh
   * @param {number} options.priority - Request priority (higher = more important)
   * @param {Array} options.invalidateCache - Cache keys to invalidate on success
   * @returns {Promise<Object>} - Response data
   */
  async request(endpoint, method = 'GET', data = null, options = {}) {
    const {
      useCache = this._cacheEnabled,
      forceRefresh = false,
      priority = 1,
      invalidateCache = [],
      ...fetchOptions
    } = options;
    
    // Generate request key
    const requestKey = this._getRequestKey(endpoint, method, data);
    
    // Update statistics
    this._stats.totalRequests++;
    
    // Check if request is already in progress
    if (this._pendingRequests.has(requestKey) && !forceRefresh) {
      console.log(`ApiEndpoint: Request already in progress for ${endpoint}, reusing promise`);
      this._stats.deduplicatedRequests++;
      return this._pendingRequests.get(requestKey);
    }
    
    // Check cache for GET requests
    if (method === 'GET' && useCache && !forceRefresh) {
      const cacheKey = `API_${requestKey}`;
      if (cacheService.isValid(cacheKey, null, this._cacheDuration)) {
        console.log(`ApiEndpoint: Using cache for ${endpoint}`);
        this._stats.cachedRequests++;
        return cacheService.get(cacheKey);
      }
    }
    
    // Add request to queue
    const requestPromise = new Promise((resolve, reject) => {
      this._requestQueue.push({
        endpoint,
        method,
        data,
        options: fetchOptions,
        priority,
        resolve,
        reject,
        invalidateCache,
        requestKey
      });
    });
    
    // Store pending request
    this._pendingRequests.set(requestKey, requestPromise);
    
    // Process queue
    this._processQueue();
    
    return requestPromise;
  }
  
  /**
   * Process request queue
   * @private
   */
  async _processQueue() {
    // Prevent concurrent queue processing
    if (this._processingQueue) {
      return;
    }
    
    this._processingQueue = true;
    
    try {
      // Sort queue by priority (higher = more important)
      this._requestQueue.sort((a, b) => b.priority - a.priority);
      
      // Process requests one by one
      while (this._requestQueue.length > 0) {
        const request = this._requestQueue.shift();
        
        try {
          // Make request
          let response;
          if (request.method === 'GET') {
            response = await super.get(request.endpoint, request.options);
          } else if (request.method === 'POST') {
            response = await super.post(request.endpoint, request.data, request.options);
          } else if (request.method === 'PUT') {
            response = await super.put(request.endpoint, request.data, request.options);
          } else if (request.method === 'DELETE') {
            response = await super.delete(request.endpoint, request.options);
          } else {
            throw new Error(`Unsupported method: ${request.method}`);
          }
          
          // Update cache for GET requests
          if (request.method === 'GET' && this._cacheEnabled) {
            const cacheKey = `API_${request.requestKey}`;
            cacheService.update(cacheKey, response);
          }
          
          // Invalidate cache if specified
          if (request.invalidateCache && request.invalidateCache.length > 0) {
            request.invalidateCache.forEach(key => {
              cacheService.invalidate(`API_${key}`);
            });
          }
          
          // Resolve promise
          request.resolve(response);
          this._stats.successfulRequests++;
        } catch (error) {
          // Reject promise
          request.reject(error);
          this._stats.failedRequests++;
        } finally {
          // Remove from pending requests
          this._pendingRequests.delete(request.requestKey);
        }
      }
    } finally {
      this._processingQueue = false;
    }
  }
  
  /**
   * Generate request key
   * @param {string} endpoint - API endpoint
   * @param {string} method - HTTP method
   * @param {Object} data - Request data
   * @returns {string} - Request key
   * @private
   */
  _getRequestKey(endpoint, method, data) {
    return `${method}-${endpoint}-${data ? JSON.stringify(data) : ''}`;
  }
  
  /**
   * Make a GET request
   * @param {string} endpoint - API endpoint
   * @param {Object} options - Request options
   * @returns {Promise<Object>} - Response data
   */
  async get(endpoint, options = {}) {
    return this.request(endpoint, 'GET', null, options);
  }
  
  /**
   * Make a POST request
   * @param {string} endpoint - API endpoint
   * @param {Object} data - Request data
   * @param {Object} options - Request options
   * @returns {Promise<Object>} - Response data
   */
  async post(endpoint, data, options = {}) {
    return this.request(endpoint, 'POST', data, options);
  }
  
  /**
   * Make a PUT request
   * @param {string} endpoint - API endpoint
   * @param {Object} data - Request data
   * @param {Object} options - Request options
   * @returns {Promise<Object>} - Response data
   */
  async put(endpoint, data, options = {}) {
    return this.request(endpoint, 'PUT', data, options);
  }
  
  /**
   * Make a DELETE request
   * @param {string} endpoint - API endpoint
   * @param {Object} options - Request options
   * @returns {Promise<Object>} - Response data
   */
  async delete(endpoint, options = {}) {
    return this.request(endpoint, 'DELETE', null, options);
  }
  
  /**
   * Clear all caches
   */
  clearCaches() {
    cacheService.clear();
  }
}

// Create singleton instance
const apiEndpoint = new ApiEndpoint();

// Register with service registry
if (serviceRegistry.has('api')) {
  console.log('ApiEndpoint: Service registry already has an API service, not registering');
} else {
  serviceRegistry.register('api', apiEndpoint);
}

// Export singleton instance
export default apiEndpoint;
