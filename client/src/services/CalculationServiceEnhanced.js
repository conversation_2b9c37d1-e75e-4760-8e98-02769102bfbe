/**
 * Enhanced Calculation Service
 * 
 * Provides standardized calculation functions for all game mechanics.
 * Ensures consistent calculations between client and server.
 * Implements memoization for expensive calculations.
 */
import { BaseService } from './BaseService';
import { memoize, memoizeWithExpiration } from '../utils/memoize';
import calculationService, {
  MINING_VALUE,
  FOOD_PER_FARMER,
  MATERIALS_PER_WORKER,
  MATERIALS_PER_MINER,
  TRADING_VALUE
} from './calculationService';

class CalculationServiceEnhanced extends BaseService {
  constructor() {
    super('calculation');
    
    // Wrap original calculation service methods with memoization
    this.calculateMiningProduction = memoize(calculationService.calculateMiningProduction.bind(calculationService));
    this.calculateFoodProduction = memoize(calculationService.calculateFoodProduction.bind(calculationService));
    this.calculateMaterialsProduction = memoize(calculationService.calculateMaterialsProduction.bind(calculationService));
    this.calculateResearchStats = memoize(calculationService.calculateResearchStats.bind(calculationService));
    this.calculateFoodStats = memoizeWithExpiration(calculationService.calculateFoodStats.bind(calculationService), 2000);
    
    // Memoize simple calculations
    this.calculateMoralModifier = memoize(calculationService.calculateMoralModifier.bind(calculationService));
    this.calculateEngineeringMultiplier = memoize(calculationService.calculateEngineeringMultiplier.bind(calculationService));
    this.calculateTotalBonus = memoize(calculationService.calculateTotalBonus.bind(calculationService));
    
    // Pass through other methods
    this.calculateRevenuePerMiner = calculationService.calculateRevenuePerMiner.bind(calculationService);
    this.calculateNormalDistribution = calculationService.calculateNormalDistribution.bind(calculationService);
    this.calculateFoodConsumption = calculationService.calculateFoodConsumption.bind(calculationService);
    this.calculateSalaries = calculationService.calculateSalaries.bind(calculationService);
    this.calculateNonSalaryCharges = calculationService.calculateNonSalaryCharges.bind(calculationService);
    this.calculateTotalCharges = calculationService.calculateTotalCharges.bind(calculationService);
    this.calculateSickProbability = calculationService.calculateSickProbability.bind(calculationService);
    this.calculateResearchProbability = calculationService.calculateResearchProbability.bind(calculationService);
    this.calculateTradingRevenue = calculationService.calculateTradingRevenue.bind(calculationService);
    this.calculateCrimeLevel = calculationService.calculateCrimeLevel.bind(calculationService);
    this.calculateDefenseLevel = calculationService.calculateDefenseLevel.bind(calculationService);
    this.calculateRenownLevel = calculationService.calculateRenownLevel.bind(calculationService);
    this.calculateArmyStrength = calculationService.calculateArmyStrength.bind(calculationService);
    this.calculateMoralStats = calculationService.calculateMoralStats.bind(calculationService);
    this.calculateHealthStats = calculationService.calculateHealthStats.bind(calculationService);
    this.getAllStats = calculationService.getAllStats.bind(calculationService);
  }
  
  /**
   * Clear all memoization caches
   */
  clearCaches() {
    // This would require modifying the memoize function to expose a way to clear caches
    console.log('CalculationServiceEnhanced: Clearing calculation caches');
    // For now, we'll just replace the memoized functions with newly memoized versions
    this.calculateMiningProduction = memoize(calculationService.calculateMiningProduction.bind(calculationService));
    this.calculateFoodProduction = memoize(calculationService.calculateFoodProduction.bind(calculationService));
    this.calculateMaterialsProduction = memoize(calculationService.calculateMaterialsProduction.bind(calculationService));
    this.calculateResearchStats = memoize(calculationService.calculateResearchStats.bind(calculationService));
    this.calculateFoodStats = memoizeWithExpiration(calculationService.calculateFoodStats.bind(calculationService), 2000);
    this.calculateMoralModifier = memoize(calculationService.calculateMoralModifier.bind(calculationService));
    this.calculateEngineeringMultiplier = memoize(calculationService.calculateEngineeringMultiplier.bind(calculationService));
    this.calculateTotalBonus = memoize(calculationService.calculateTotalBonus.bind(calculationService));
  }
}

// Create a singleton instance
const calculationServiceEnhanced = new CalculationServiceEnhanced();

// Export the singleton instance
export default calculationServiceEnhanced;

// Export constants for compatibility
export {
  MINING_VALUE,
  FOOD_PER_FARMER,
  MATERIALS_PER_WORKER,
  MATERIALS_PER_MINER,
  TRADING_VALUE
};
