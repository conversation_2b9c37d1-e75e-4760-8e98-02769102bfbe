/**
 * Service centralisé pour tous les appels API
 * Fournit des méthodes pour interagir avec l'API du serveur
 *
 * IMPORTANT: This file is being phased out. Use ApiEndpoint or individual services directly.
 * This service is maintained for backward compatibility only.
 */
import { BaseService } from './BaseService';
import apiEndpoint from './ApiEndpoint';
import GameService from './GameService';
import ModifiersService from './ModifiersService';
import InhabitantsService from './InhabitantsService';

class ApiService extends BaseService {
  constructor() {
    super('api');

    // Initialiser les services spécifiques
    this.gameService = GameService;
    this.modifiersService = ModifiersService;
    this.inhabitantsService = InhabitantsService;
  }

  /**
   * Récupère l'état du jeu
   * @returns {Promise<Object>} - État du jeu
   */
  async getGameState() {
    return this.gameService.getGameState();
  }

  /**
   * Récupère les événements du jeu
   * @returns {Promise<Array>} - Liste des événements
   */
  async getEvents() {
    return this.gameService.getEvents();
  }

  /**
   * Exécute un cycle de jeu
   * @returns {Promise<Object>} - Résultat du cycle
   */
  async processNextCycle() {
    return this.gameService.processNextCycle();
  }

  /**
   * Change le nombre de travailleurs pour un métier
   * @param {number} jobId - ID du métier
   * @param {number} change - Changement à appliquer
   * @returns {Promise<Object>} - Résultat de l'opération
   */
  async changeJobNumber(jobId, change) {
    return this.gameService.changeJobNumber(jobId, change);
  }

  /**
   * Change le nombre total de travailleurs
   * @param {number} change - Changement à appliquer
   * @returns {Promise<Object>} - Résultat de l'opération
   */
  async changeWorkerNumber(change) {
    return this.gameService.changeWorkerNumber(change);
  }

  /**
   * Change le nombre de travailleurs libres pour un métier
   * @param {number} jobId - ID du métier
   * @param {number} change - Changement à appliquer
   * @returns {Promise<Object>} - Résultat de l'opération
   */
  async changeFreeWorkers(jobId, change) {
    return this.gameService.changeFreeWorkers(jobId, change);
  }

  /**
   * Transfère des travailleurs d'un métier à un autre
   * @param {number} fromJobId - ID du métier source
   * @param {number} toJobId - ID du métier destination
   * @param {number} count - Nombre de travailleurs à transférer
   * @returns {Promise<Object>} - Résultat de l'opération
   */
  async transferJob(fromJobId, toJobId, count) {
    return this.gameService.transferJob(fromJobId, toJobId, count);
  }

  /**
   * Récupère les modificateurs pour une table
   * @param {number} tableId - ID de la table
   * @returns {Promise<Array>} - Liste des modificateurs
   */
  async getModifiersByTable(tableId) {
    return this.modifiersService.getModifiersByTable(tableId);
  }

  /**
   * Récupère tous les modificateurs
   * @returns {Promise<Object>} - Tous les modificateurs
   */
  async getAllModifiers() {
    return this.modifiersService.getAllModifiers();
  }

  /**
   * Récupère les moyens de défense
   * @returns {Promise<Array>} - Liste des moyens de défense
   */
  async getDefenseMeans() {
    return this.modifiersService.getDefenseMeans();
  }

  /**
   * Ajoute un moyen de défense
   * @param {Object} defenseMean - Moyen de défense à ajouter
   * @returns {Promise<Object>} - Résultat de l'opération
   */
  async addDefenseMean(defenseMean) {
    return this.modifiersService.addDefenseMean(defenseMean);
  }

  /**
   * Supprime un moyen de défense
   * @param {number} id - ID du moyen de défense
   * @returns {Promise<Object>} - Résultat de l'opération
   */
  async deleteDefenseMean(id) {
    return this.modifiersService.deleteDefenseMean(id);
  }

  /**
   * Ajoute un modificateur
   * @param {number} tableId - ID de la table
   * @param {Object} modifier - Modificateur à ajouter
   * @returns {Promise<Object>} - Résultat de l'opération
   */
  async addModifier(tableId, modifier) {
    return this.modifiersService.addModifier(tableId, modifier);
  }

  /**
   * Met à jour un modificateur
   * @param {number} id - ID du modificateur
   * @param {Object} modifier - Nouvelles valeurs du modificateur
   * @returns {Promise<Object>} - Résultat de l'opération
   */
  async updateModifier(id, modifier) {
    return this.modifiersService.updateModifier(id, modifier);
  }

  /**
   * Supprime un modificateur
   * @param {number} id - ID du modificateur
   * @returns {Promise<Object>} - Résultat de l'opération
   */
  async deleteModifier(id) {
    return this.modifiersService.deleteModifier(id);
  }

  /**
   * Récupère les données de moral
   * @returns {Promise<Object>} - Données de moral
   */
  async getMoralData() {
    return this.modifiersService.getMoralData();
  }

  /**
   * Récupère les données de minage
   * @returns {Promise<Object>} - Données de minage
   */
  async getMiningData() {
    return this.modifiersService.getMiningData();
  }

  /**
   * Récupère les données de nourriture
   * @returns {Promise<Object>} - Données de nourriture
   */
  async getFoodData() {
    return this.modifiersService.getFoodData();
  }

  /**
   * Récupère les données de matériaux
   * @returns {Promise<Object>} - Données de matériaux
   */
  async getMaterialsData() {
    return this.modifiersService.getMaterialsData();
  }

  /**
   * Récupère les données de charges
   * @returns {Promise<Object>} - Données de charges
   */
  async getChargesData() {
    return this.modifiersService.getChargesData();
  }

  /**
   * Récupère les données de santé
   * @returns {Promise<Object>} - Données de santé
   */
  async getHealthData() {
    return this.modifiersService.getHealthData();
  }

  /**
   * Récupère les données de recherche
   * @returns {Promise<Object>} - Données de recherche
   */
  async getResearchData() {
    return this.modifiersService.getResearchData();
  }

  /**
   * Récupère les données de commerce
   * @returns {Promise<Object>} - Données de commerce
   */
  async getTradingData() {
    return this.modifiersService.getTradingData();
  }

  /**
   * Récupère les données de justice et défense
   * @returns {Promise<Object>} - Données de justice et défense
   */
  async getJusticeDefenseData() {
    return this.modifiersService.getJusticeDefenseData();
  }

  /**
   * Récupère les données de renommée
   * @returns {Promise<Object>} - Données de renommée
   */
  async getRenownData() {
    return this.modifiersService.getRenownData();
  }

  /**
   * Récupère la liste des habitants
   * @param {Object} params - Paramètres de filtrage
   * @returns {Promise<Object>} - Liste des habitants et métadonnées
   */
  async getInhabitants(params = {}) {
    return this.inhabitantsService.getInhabitants(params);
  }

  /**
   * Récupère un habitant par son ID
   * @param {number} id - ID de l'habitant
   * @returns {Promise<Object>} - Habitant
   */
  async getInhabitant(id) {
    return this.inhabitantsService.getInhabitant(id);
  }

  /**
   * Crée un nouvel habitant
   * @param {Object} inhabitant - Données de l'habitant
   * @returns {Promise<Object>} - Résultat de l'opération
   */
  async createInhabitant(inhabitant) {
    return this.inhabitantsService.createInhabitant(inhabitant);
  }

  /**
   * Met à jour un habitant
   * @param {number} id - ID de l'habitant
   * @param {Object} inhabitant - Nouvelles données de l'habitant
   * @returns {Promise<Object>} - Résultat de l'opération
   */
  async updateInhabitant(id, inhabitant) {
    return this.inhabitantsService.updateInhabitant(id, inhabitant);
  }

  /**
   * Supprime un habitant
   * @param {number} id - ID de l'habitant
   * @returns {Promise<Object>} - Résultat de l'opération
   */
  async deleteInhabitant(id) {
    return this.inhabitantsService.deleteInhabitant(id);
  }

  /**
   * Synchronise les habitants avec les métiers
   * @returns {Promise<Object>} - Résultat de l'opération
   */
  async syncInhabitantsWithJobs() {
    return this.inhabitantsService.syncInhabitantsWithJobs();
  }

  /**
   * Récupère les statistiques de population
   * @returns {Promise<Object>} - Statistiques de population
   */
  async getPopulationStats() {
    return this.inhabitantsService.getPopulationStats();
  }
}

// Créer une instance du service
const apiService = new ApiService();

// Exporter l'instance par défaut
export default apiService;

// Exporter les objets de compatibilité pour maintenir la compatibilité avec le code existant
export const gameApi = {
  getGameState: apiService.getGameState.bind(apiService),
  getEvents: apiService.getEvents.bind(apiService),
  processNextCycle: apiService.processNextCycle.bind(apiService),
  changeJobNumber: apiService.changeJobNumber.bind(apiService),
  changeWorkerNumber: apiService.changeWorkerNumber.bind(apiService),
  changeFreeWorkers: apiService.changeFreeWorkers.bind(apiService),
  transferJob: apiService.transferJob.bind(apiService)
};

export const modifiersApi = {
  getModifiersByTable: apiService.getModifiersByTable.bind(apiService),
  getAllModifiers: apiService.getAllModifiers.bind(apiService),
  getDefenseMeans: apiService.getDefenseMeans.bind(apiService),
  addDefenseMean: apiService.addDefenseMean.bind(apiService),
  deleteDefenseMean: apiService.deleteDefenseMean.bind(apiService),
  addModifier: apiService.addModifier.bind(apiService),
  updateModifier: apiService.updateModifier.bind(apiService),
  deleteModifier: apiService.deleteModifier.bind(apiService),
  getMoralData: apiService.getMoralData.bind(apiService),
  getMiningData: apiService.getMiningData.bind(apiService),
  getFoodData: apiService.getFoodData.bind(apiService),
  getMaterialsData: apiService.getMaterialsData.bind(apiService),
  getChargesData: apiService.getChargesData.bind(apiService),
  getHealthData: apiService.getHealthData.bind(apiService),
  getResearchData: apiService.getResearchData.bind(apiService),
  getTradingData: apiService.getTradingData.bind(apiService),
  getJusticeDefenseData: apiService.getJusticeDefenseData.bind(apiService),
  getRenownData: apiService.getRenownData.bind(apiService)
};

export const inhabitantsApi = {
  getInhabitants: apiService.getInhabitants.bind(apiService),
  getInhabitant: apiService.getInhabitant.bind(apiService),
  createInhabitant: apiService.createInhabitant.bind(apiService),
  updateInhabitant: apiService.updateInhabitant.bind(apiService),
  deleteInhabitant: apiService.deleteInhabitant.bind(apiService),
  syncInhabitantsWithJobs: apiService.syncInhabitantsWithJobs.bind(apiService),
  getPopulationStats: apiService.getPopulationStats.bind(apiService)
};

// Exporter un objet nommé pour éviter l'erreur ESLint
export const api = {
  game: gameApi,
  modifiers: modifiersApi,
  inhabitants: inhabitantsApi
};
