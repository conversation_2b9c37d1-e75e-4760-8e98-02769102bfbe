/**
 * Service pour les opérations liées aux bâtiments
 */
import { BaseService } from './BaseService';

class BuildingsService extends BaseService {
  constructor() {
    super('buildings');
  }

  /**
   * Récupère tous les bâtiments
   * @returns {Promise<Array>} - Liste des bâtiments
   */
  async getBuildings() {
    return this.get();
  }

  /**
   * Récupère un bâtiment par son ID
   * @param {number} id - ID du bâtiment
   * @returns {Promise<Object>} - Données du bâtiment
   */
  async getBuilding(id) {
    return this.get(`${id}`);
  }

  /**
   * Crée un nouveau bâtiment
   * @param {Object} building - Données du bâtiment
   * @returns {Promise<Object>} - Résultat de l'opération
   */
  async createBuilding(building) {
    return this.post('', building);
  }

  /**
   * Met à jour un bâtiment
   * @param {number} id - ID du bâtiment
   * @param {Object} building - Nouvelles données du bâtiment
   * @returns {Promise<Object>} - Résultat de l'opération
   */
  async updateBuilding(id, building) {
    return this.put(`${id}`, building);
  }

  /**
   * Supprime un bâtiment
   * @param {number} id - ID du bâtiment
   * @returns {Promise<Object>} - Résultat de l'opération
   */
  async deleteBuilding(id) {
    return this.delete(`${id}`);
  }

  /**
   * Construit un bâtiment
   * @param {number} id - ID du bâtiment
   * @returns {Promise<Object>} - Résultat de l'opération
   */
  async buildBuilding(id) {
    return this.post(`${id}/build`);
  }

  /**
   * Récupère les types de bâtiments
   * @returns {Promise<Array>} - Liste des types de bâtiments
   */
  async getBuildingTypes() {
    return this.get('types');
  }
}

// Créer une instance du service
const buildingsService = new BuildingsService();

// Exporter les méthodes individuelles pour maintenir la compatibilité avec le code existant
export const getBuildings = buildingsService.getBuildings.bind(buildingsService);
export const getBuilding = buildingsService.getBuilding.bind(buildingsService);
export const createBuilding = buildingsService.createBuilding.bind(buildingsService);
export const updateBuilding = buildingsService.updateBuilding.bind(buildingsService);
export const deleteBuilding = buildingsService.deleteBuilding.bind(buildingsService);
export const buildBuilding = buildingsService.buildBuilding.bind(buildingsService);
export const getBuildingTypes = buildingsService.getBuildingTypes.bind(buildingsService);

// Exporter également l'instance du service
export default buildingsService;
