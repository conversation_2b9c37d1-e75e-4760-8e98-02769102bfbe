/**
 * Service de base pour tous les services API
 * Fournit des méthodes communes pour les requêtes HTTP
 * et implémente la déduplication des requêtes, le cache et la gestion des erreurs
 */
import config from '../config';
import cacheService from './CacheService';
import {
  handleApiError,
  handleNetworkError,
  ERROR_TYPES,
  ERROR_SEVERITY
} from '../utils/errorHandler';

// Default cache duration in milliseconds
const DEFAULT_CACHE_DURATION = 5000; // 5 seconds

export class BaseService {
  /**
   * Constructeur du service de base
   * @param {string} endpoint - Point d'entrée de l'API (ex: 'game', 'modifiers', etc.)
   */
  constructor(endpoint) {
    this.endpoint = endpoint;
    this.baseUrl = config.API_BASE_URL;
    this.pendingRequests = new Map();
    this.cacheEnabled = true;
    this.cacheDuration = DEFAULT_CACHE_DURATION;
    this._requestsInProgress = {};
  }

  /**
   * Enable or disable caching for this service
   * @param {boolean} enabled - Whether caching is enabled
   */
  setCacheEnabled(enabled) {
    this.cacheEnabled = enabled;
  }

  /**
   * Set cache duration for this service
   * @param {number} duration - Cache duration in milliseconds
   */
  setCacheDuration(duration) {
    this.cacheDuration = duration;
  }

  /**
   * Effectue une requête GET avec support de cache
   * @param {string} path - Chemin relatif à l'endpoint
   * @param {Object} options - Options de la requête
   * @param {boolean} options.useCache - Utiliser le cache (par défaut: true)
   * @param {boolean} options.forceRefresh - Forcer le rafraîchissement du cache (par défaut: false)
   * @param {number} options.cacheDuration - Durée de validité du cache en millisecondes
   * @returns {Promise<Object>} - Réponse de l'API
   */
  async get(path = '', options = {}) {
    const fullPath = `${this.endpoint}${path ? `/${path}` : ''}`;
    const cacheKey = `GET-${fullPath}`;
    const {
      useCache = this.cacheEnabled,
      forceRefresh = false,
      cacheDuration = this.cacheDuration,
      ...fetchOptions
    } = options;

    // Check if request is already in progress
    if (this._requestsInProgress[cacheKey]) {
      console.log(`BaseService: Request already in progress for ${fullPath}, reusing promise`);
      return this._requestsInProgress[cacheKey];
    }

    // Use cache if enabled and valid
    if (useCache && !forceRefresh && cacheService.isValid(cacheKey, null, cacheDuration)) {
      console.log(`BaseService: Using cache for ${fullPath}`);
      return cacheService.get(cacheKey);
    }

    // Create request promise
    const requestPromise = (async () => {
      try {
        const data = await this.request(fullPath, {
          ...fetchOptions,
          method: 'GET'
        });

        // Update cache if caching is enabled
        if (useCache) {
          cacheService.update(cacheKey, data);
        }

        return data;
      } finally {
        // Remove from in-progress requests
        delete this._requestsInProgress[cacheKey];
      }
    })();

    // Store request promise
    this._requestsInProgress[cacheKey] = requestPromise;

    return requestPromise;
  }

  /**
   * Effectue une requête POST et invalide le cache associé
   * @param {string} path - Chemin relatif à l'endpoint
   * @param {Object} data - Données à envoyer
   * @param {Object} options - Options de la requête
   * @param {Array} options.invalidateCache - Liste des clés de cache à invalider
   * @returns {Promise<Object>} - Réponse de l'API
   */
  async post(path = '', data = {}, options = {}) {
    const fullPath = `${this.endpoint}${path ? `/${path}` : ''}`;
    const { invalidateCache = [], ...fetchOptions } = options;

    try {
      const result = await this.request(fullPath, {
        ...fetchOptions,
        method: 'POST',
        headers: {
          ...fetchOptions.headers,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
      });

      // Invalidate specified cache keys
      this.invalidateCache(invalidateCache);

      return result;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Invalide les clés de cache spécifiées
   * @param {Array|string} keys - Clé(s) de cache à invalider
   */
  invalidateCache(keys) {
    if (!keys || (Array.isArray(keys) && keys.length === 0)) {
      return;
    }

    const keysToInvalidate = Array.isArray(keys) ? keys : [keys];

    keysToInvalidate.forEach(key => {
      if (key === '*') {
        // Invalider tout le cache
        console.log('BaseService: Invalidating all cache');
        cacheService.invalidateAll();
      } else {
        // Invalider une clé spécifique
        console.log(`BaseService: Invalidating cache for ${key}`);
        cacheService.invalidate(key);

        // Invalider également la clé avec le préfixe GET
        const getKey = key.startsWith('GET-') ? key : `GET-${this.endpoint}${key ? `/${key}` : ''}`;
        cacheService.invalidate(getKey);
      }
    });
  }

  /**
   * Effectue une requête PUT et invalide le cache associé
   * @param {string} path - Chemin relatif à l'endpoint
   * @param {Object} data - Données à envoyer
   * @param {Object} options - Options de la requête
   * @param {Array} options.invalidateCache - Liste des clés de cache à invalider
   * @returns {Promise<Object>} - Réponse de l'API
   */
  async put(path = '', data = {}, options = {}) {
    const fullPath = `${this.endpoint}${path ? `/${path}` : ''}`;
    const { invalidateCache = [], ...fetchOptions } = options;

    try {
      const result = await this.request(fullPath, {
        ...fetchOptions,
        method: 'PUT',
        headers: {
          ...fetchOptions.headers,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
      });

      // Invalidate specified cache keys
      this.invalidateCache(invalidateCache);

      return result;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Effectue une requête DELETE et invalide le cache associé
   * @param {string} path - Chemin relatif à l'endpoint
   * @param {Object} options - Options de la requête
   * @param {Array} options.invalidateCache - Liste des clés de cache à invalider
   * @returns {Promise<Object>} - Réponse de l'API
   */
  async delete(path = '', options = {}) {
    const fullPath = `${this.endpoint}${path ? `/${path}` : ''}`;
    const { invalidateCache = [], ...fetchOptions } = options;

    try {
      const result = await this.request(fullPath, {
        ...fetchOptions,
        method: 'DELETE'
      });

      // Invalidate specified cache keys
      this.invalidateCache(invalidateCache);

      return result;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Effectue une requête HTTP générique avec déduplication
   * @param {string} path - Chemin de la requête
   * @param {Object} options - Options de la requête
   * @returns {Promise<Object>} - Réponse de l'API
   */
  async request(path, options = {}) {
    const url = `${this.baseUrl}/${path}`;
    const requestKey = `${options.method || 'GET'}-${url}-${options.body || ''}`;

    // Déduplication des requêtes
    if (this.pendingRequests.has(requestKey)) {
      return this.pendingRequests.get(requestKey);
    }

    // Créer une nouvelle promesse pour cette requête avec timeout
    const requestPromise = (async () => {
      // Créer un contrôleur d'abandon pour pouvoir annuler la requête en cas de timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => {
        controller.abort();
        console.error(`BaseService.request: Timeout for ${url}`);
      }, 10000); // 10 secondes de timeout

      try {
        // Ajouter le signal d'abandon aux options
        const fetchOptions = {
          ...options,
          signal: controller.signal
        };

        // Effectuer la requête avec un try/catch pour gérer les erreurs réseau
        let response;
        try {
          response = await fetch(url, fetchOptions);
        } catch (networkError) {
          // Utiliser le gestionnaire d'erreurs réseau standardisé
          const formattedError = handleNetworkError({
            message: networkError.name === 'AbortError'
              ? `Timeout de la requête après 10 secondes: ${url}`
              : `Erreur réseau: ${networkError.message}`,
            originalError: networkError,
            url,
            method: options.method || 'GET',
            severity: ERROR_SEVERITY.ERROR
          });

          throw formattedError;
        }

        // Lire la réponse en texte d'abord
        let responseText;
        try {
          responseText = await response.text();
        } catch (textError) {
          console.error(`BaseService.request: Error reading response text from ${url}:`, textError);
          throw new Error(`Erreur lors de la lecture de la réponse: ${textError.message}`);
        }

        // Essayer de parser la réponse en JSON
        let data;
        try {
          // Vérifier si la réponse est vide
          if (!responseText.trim()) {
            data = {}; // Utiliser un objet vide comme fallback
          } else {
            data = JSON.parse(responseText);
          }
        } catch (parseError) {
          console.error(`BaseService.request: API Error (${path}): Impossible de parser la réponse JSON:`, responseText);

          // Essayer de retourner un objet avec le texte brut si le parsing échoue
          return {
            rawText: responseText,
            parseError: parseError.message,
            status: response.status,
            ok: response.ok
          };
        }

        if (!response.ok) {
          throw new Error(data.error || `API error: ${response.status} ${response.statusText}`);
        }

        return data;
      } catch (error) {
        // Utiliser le gestionnaire d'erreurs API standardisé
        const formattedError = handleApiError({
          message: error.message,
          originalError: error,
          url,
          path,
          method: options.method || 'GET',
          severity: error.status >= 500 ? ERROR_SEVERITY.ERROR : ERROR_SEVERITY.WARNING,
          status: error.status,
          timestamp: new Date().toISOString()
        });

        throw formattedError;
      } finally {
        // Annuler le timeout
        clearTimeout(timeoutId);

        // Supprimer la requête de la liste des requêtes en cours
        this.pendingRequests.delete(requestKey);
      }
    })();

    // Ajouter la requête à la liste des requêtes en cours
    this.pendingRequests.set(requestKey, requestPromise);

    return requestPromise;
  }
}

export default BaseService;
