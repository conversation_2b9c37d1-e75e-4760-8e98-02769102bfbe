/**
 * Service pour les opérations liées au calendrier
 */
import { BaseService } from './BaseService';

class CalendarService extends BaseService {
  constructor() {
    super('calendar');
  }

  /**
   * Récupère les données du calendrier
   * @returns {Promise<Object>} - Donn<PERSON> du calendrier
   */
  async getCalendarData() {
    return this.get();
  }

  /**
   * Récupère les mois du calendrier
   * @returns {Promise<Array>} - Liste des mois
   */
  async getMonths() {
    return this.get('months');
  }

  /**
   * Récupère un mois par son ID
   * @param {number} id - ID du mois
   * @returns {Promise<Object>} - Données du mois
   */
  async getMonth(id) {
    return this.get(`months/${id}`);
  }

  /**
   * Récupère le mois actuel
   * @returns {Promise<Object>} - Données du mois actuel
   */
  async getCurrentMonth() {
    return this.get('current-month');
  }

  /**
   * Récupère l'année actuelle
   * @returns {Promise<Object>} - Données de l'année actuelle
   */
  async getCurrentYear() {
    return this.get('current-year');
  }

  /**
   * Récupère le cycle actuel
   * @returns {Promise<Object>} - Données du cycle actuel
   */
  async getCurrentCycle() {
    return this.get('current-cycle');
  }
}

// Créer une instance du service
const calendarService = new CalendarService();

// Exporter les méthodes individuelles pour maintenir la compatibilité avec le code existant
export const getCalendarData = calendarService.getCalendarData.bind(calendarService);
export const getMonths = calendarService.getMonths.bind(calendarService);
export const getMonth = calendarService.getMonth.bind(calendarService);
export const getCurrentMonth = calendarService.getCurrentMonth.bind(calendarService);
export const getCurrentYear = calendarService.getCurrentYear.bind(calendarService);
export const getCurrentCycle = calendarService.getCurrentCycle.bind(calendarService);

// Exporter également l'instance du service
export default calendarService;
