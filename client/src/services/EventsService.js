/**
 * Service pour les opérations liées aux événements
 */
import { BaseService } from './BaseService';

class EventsService extends BaseService {
  constructor() {
    super('events');
  }

  /**
   * Récupère tous les événements
   * @returns {Promise<Array>} - Liste des événements
   */
  async getEvents() {
    return this.get();
  }

  /**
   * Récupère un événement par son ID
   * @param {number} id - ID de l'événement
   * @returns {Promise<Object>} - Données de l'événement
   */
  async getEvent(id) {
    return this.get(`${id}`);
  }

  /**
   * Crée un nouvel événement
   * @param {Object} event - Données de l'événement
   * @returns {Promise<Object>} - Résultat de l'opération
   */
  async createEvent(event) {
    return this.post('', event);
  }

  /**
   * Met à jour un événement
   * @param {number} id - ID de l'événement
   * @param {Object} event - Nouvelles données de l'événement
   * @returns {Promise<Object>} - Résultat de l'opération
   */
  async updateEvent(id, event) {
    return this.put(`${id}`, event);
  }

  /**
   * Supprime un événement
   * @param {number} id - ID de l'événement
   * @returns {Promise<Object>} - Résultat de l'opération
   */
  async deleteEvent(id) {
    return this.delete(`${id}`);
  }

  /**
   * Marque un événement comme lu
   * @param {number} id - ID de l'événement
   * @returns {Promise<Object>} - Résultat de l'opération
   */
  async markEventAsRead(id) {
    return this.put(`${id}/read`, { read: true });
  }

  /**
   * Marque tous les événements comme lus
   * @returns {Promise<Object>} - Résultat de l'opération
   */
  async markAllEventsAsRead() {
    return this.put('read-all');
  }
}

// Créer une instance du service
const eventsService = new EventsService();

// Exporter les méthodes individuelles pour maintenir la compatibilité avec le code existant
export const getEvents = eventsService.getEvents.bind(eventsService);
export const getEvent = eventsService.getEvent.bind(eventsService);
export const createEvent = eventsService.createEvent.bind(eventsService);
export const updateEvent = eventsService.updateEvent.bind(eventsService);
export const deleteEvent = eventsService.deleteEvent.bind(eventsService);
export const markEventAsRead = eventsService.markEventAsRead.bind(eventsService);
export const markAllEventsAsRead = eventsService.markAllEventsAsRead.bind(eventsService);

// Exporter également l'instance du service
export default eventsService;
