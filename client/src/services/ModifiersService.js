/**
 * Service pour les opérations liées aux modificateurs
 */
import { BaseService } from './BaseService';

class ModifiersService extends BaseService {
  constructor() {
    super('modifiers');
  }

  /**
   * Récupère les modificateurs pour une table spécifique
   * @param {number} tableId - ID de la table
   * @returns {Promise<Array>} - Liste des modificateurs
   */
  async getModifiersByTable(tableId) {
    try {
      // Utiliser le cache pour éviter les requêtes répétées
      const cacheKey = `modifiers_table_${tableId}`;
      if (!this._modifiersCache) this._modifiersCache = {};

      if (this._modifiersCache[cacheKey]) {
        const cacheEntry = this._modifiersCache[cacheKey];
        const now = Date.now();

        // Utiliser le cache si moins de 1 seconde s'est écoulée
        if (now - cacheEntry.timestamp < 1000) {
          console.log(`Using cached modifiers for table ${tableId}`);
          return cacheEntry.data;
        }
      }

      const data = await this.get(`table/${tableId}`);
      console.log(`API: Modificateurs récupérés pour la table ${tableId}:`, data);

      // Vérifier si les données sont valides
      if (!data || !data.modifiers) {
        console.error(`API Error: Données invalides pour la table ${tableId}:`, data);
        return [];
      }

      let result;
      // Traitement spécial pour la table Moral (ID 10)
      if (tableId === 10) {
        // Pour la table Moral, s'assurer que les effets sont des entiers
        result = data.modifiers.map(mod => ({
          ...mod,
          effect: typeof mod.effect === 'number' ? mod.effect : parseInt(mod.effect) || 0
        }));
      } else {
        // Retourner directement le tableau de modificateurs pour maintenir la compatibilité
        result = data.modifiers;
      }

      // Mettre en cache les résultats
      this._modifiersCache[cacheKey] = {
        data: result,
        timestamp: Date.now()
      };

      return result;
    } catch (error) {
      console.error(`API Error: Échec de la récupération des modificateurs pour la table ${tableId}:`, error);
      // En cas d'erreur, retourner un tableau vide pour éviter les erreurs en cascade
      return [];
    }
  }

  /**
   * Récupère les données de nourriture
   * @returns {Promise<Object>} - Données de nourriture
   */
  async getFoodModifiers() {
    try {
      // Utiliser le cache pour éviter les requêtes répétées
      if (this._foodCache) {
        const now = Date.now();

        // Utiliser le cache si moins de 5 secondes se sont écoulées
        if (now - this._foodCache.timestamp < 5000) {
          console.log('Using cached food modifiers');
          return this._foodCache.data;
        }
      }

      const data = await this.get('food');
      if (data.success) {
        // Mettre en cache les résultats
        this._foodCache = {
          data: data.foodData,
          timestamp: Date.now()
        };

        return data.foodData;
      } else {
        throw new Error(data.error || 'Failed to fetch food modifiers');
      }
    } catch (error) {
      console.error('Error fetching food modifiers:', error);
      // En cas d'erreur, retourner un objet vide pour éviter les erreurs en cascade
      return {};
    }
  }

  /**
   * Récupère les données de santé
   * @returns {Promise<Object>} - Données de santé
   */
  async getHealthData() {
    try {
      // Utiliser le cache pour éviter les requêtes répétées
      if (this._healthCache) {
        const now = Date.now();

        // Utiliser le cache si moins de 5 secondes se sont écoulées
        if (now - this._healthCache.timestamp < 5000) {
          console.log('Using cached health data');
          return this._healthCache.data;
        }
      }

      const data = await this.get('health');
      if (data.success) {
        // Mettre en cache les résultats
        this._healthCache = {
          data: data.healthData,
          timestamp: Date.now()
        };

        return data.healthData;
      } else {
        throw new Error(data.error || 'Failed to fetch health data');
      }
    } catch (error) {
      console.error('Error fetching health data:', error);
      // En cas d'erreur, retourner un objet vide pour éviter les erreurs en cascade
      return {};
    }
  }

  /**
   * Récupère les données de recherche
   * @returns {Promise<Object>} - Données de recherche
   */
  async getResearchData() {
    try {
      // Utiliser le cache pour éviter les requêtes répétées
      if (this._researchCache) {
        const now = Date.now();

        // Utiliser le cache si moins de 5 secondes se sont écoulées
        if (now - this._researchCache.timestamp < 5000) {
          console.log('Using cached research data');
          return this._researchCache.data;
        }
      }

      const data = await this.get('research');
      console.log('Research data received from server:', data.researchData);

      if (data.success) {
        // Mettre en cache les résultats
        this._researchCache = {
          data: data.researchData,
          timestamp: Date.now()
        };

        return data.researchData;
      } else {
        throw new Error(data.error || 'Failed to fetch research data');
      }
    } catch (error) {
      console.error('Error fetching research data:', error);
      // En cas d'erreur, retourner un objet vide pour éviter les erreurs en cascade
      return {};
    }
  }

  /**
   * Récupère les données de commerce
   * @returns {Promise<Object>} - Données de commerce
   */
  async getTradingData() {
    try {
      // Utiliser le cache pour éviter les requêtes répétées
      if (this._tradingCache) {
        const now = Date.now();

        // Utiliser le cache si moins de 5 secondes se sont écoulées
        if (now - this._tradingCache.timestamp < 5000) {
          console.log('Using cached trading data');
          return this._tradingCache.data;
        }
      }

      const data = await this.get('trading');
      console.log('Trading data received from server:', data.tradingData);

      if (data.success) {
        // Mettre en cache les résultats
        this._tradingCache = {
          data: data.tradingData,
          timestamp: Date.now()
        };

        return data.tradingData;
      } else {
        throw new Error(data.error || 'Failed to fetch trading data');
      }
    } catch (error) {
      console.error('Error fetching trading data:', error);
      // En cas d'erreur, retourner un objet vide pour éviter les erreurs en cascade
      return {};
    }
  }

  /**
   * Récupère les données de justice et défense
   * @returns {Promise<Object>} - Données de justice et défense
   */
  async getJusticeDefenseData() {
    try {
      // Utiliser le cache pour éviter les requêtes répétées
      if (this._justiceDefenseCache) {
        const now = Date.now();

        // Utiliser le cache si moins de 5 secondes se sont écoulées
        if (now - this._justiceDefenseCache.timestamp < 5000) {
          console.log('Using cached justice and defense data');
          return this._justiceDefenseCache.data;
        }
      }

      const data = await this.get('justice-defense');
      console.log('Justice and defense data received from server:', data.justiceDefenseData);

      if (data.success) {
        // Mettre en cache les résultats
        this._justiceDefenseCache = {
          data: data.justiceDefenseData,
          timestamp: Date.now()
        };

        return data.justiceDefenseData;
      } else {
        throw new Error(data.error || 'Failed to fetch justice and defense data');
      }
    } catch (error) {
      console.error('Error fetching justice and defense data:', error);
      // En cas d'erreur, retourner un objet vide pour éviter les erreurs en cascade
      return {};
    }
  }

  /**
   * Récupère les moyens de défense
   * @returns {Promise<Array>} - Liste des moyens de défense
   */
  async getDefenseMeans() {
    try {
      // Utiliser le cache pour éviter les requêtes répétées
      if (this._defenseMeansCache) {
        console.log('Using cached defense means');
        return this._defenseMeansCache;
      }

      const data = await this.get('defense-means');
      console.log('Defense means received from server:', data);

      if (data.success) {
        // Mettre en cache les résultats
        this._defenseMeansCache = data.defenseMeans;
        return data.defenseMeans;
      } else {
        throw new Error(data.error || 'Failed to fetch defense means');
      }
    } catch (error) {
      console.error('Error fetching defense means:', error);
      // En cas d'erreur, retourner un tableau vide pour éviter les erreurs en cascade
      return [];
    }
  }

  /**
   * Ajoute un moyen de défense
   * @param {Object} defenseMean - Moyen de défense à ajouter
   * @returns {Promise<Object>} - Résultat de l'opération
   */
  async addDefenseMean(defenseMean) {
    try {
      const data = await this.post('defense-means', defenseMean);
      if (data.success) {
        return data;
      } else {
        throw new Error(data.error || 'Failed to add defense mean');
      }
    } catch (error) {
      console.error('Error adding defense mean:', error);
      throw error;
    }
  }

  /**
   * Supprime un moyen de défense
   * @param {number} id - ID du moyen de défense
   * @returns {Promise<Object>} - Résultat de l'opération
   */
  async deleteDefenseMean(id) {
    try {
      const data = await this.delete(`defense-means/${id}`);
      if (data.success) {
        return data;
      } else {
        throw new Error(data.error || 'Failed to delete defense mean');
      }
    } catch (error) {
      console.error(`Error deleting defense mean ${id}:`, error);
      throw error;
    }
  }

  /**
   * Force le recalcul de la probabilité de recherche
   * @returns {Promise<void>}
   */
  async recalculateResearchProbability() {
    try {
      const data = await this.post('research/recalculate');
      if (!data.success) {
        throw new Error(data.error || 'Failed to recalculate research probability');
      }
    } catch (error) {
      console.error('Error recalculating research probability:', error);
      throw error;
    }
  }

  /**
   * Ajoute un modificateur à une table
   * @param {number} tableId - ID de la table
   * @param {Object} modifier - Modificateur à ajouter
   * @returns {Promise<Object>} - Résultat de l'opération
   */
  async addModifier(tableId, modifier) {
    try {
      const data = await this.post(`table/${tableId}`, modifier);
      if (data.success) {
        // Pour les tables de nourriture (1, 2, 3), forcer un rechargement complet
        if (tableId === 1 || tableId === 2 || tableId === 3) {
          console.log(`ModifiersService: Invalidation de tout le cache pour la table de nourriture ${tableId}`);
          // Vider complètement le cache
          this._modifiersCache = {};
          // Notifier tous les abonnés
          this._notifySubscribers('all');
        } else {
          // Pour les autres tables, invalider seulement le cache de cette table
          this.invalidateTableCache(tableId);
        }
        return data;
      } else {
        throw new Error(data.error || 'Failed to add modifier');
      }
    } catch (error) {
      console.error('Error adding modifier:', error);
      throw error;
    }
  }

  /**
   * Invalide le cache pour une table spécifique
   * @param {number} tableId - ID de la table
   */
  invalidateTableCache(tableId) {
    console.log(`ModifiersService: Invalidation du cache pour la table ${tableId}`);
    if (this._modifiersCache) {
      const cacheKey = `modifiers_table_${tableId}`;
      delete this._modifiersCache[cacheKey];

      // Invalider également le cache global pour s'assurer que toutes les données sont rechargées
      if (this._modifiersCache['modifiers_all']) {
        delete this._modifiersCache['modifiers_all'];
      }

      // Pour les tables de nourriture (1, 2, 3), invalider toutes les tables
      // pour garantir une mise à jour immédiate
      if (tableId === 1 || tableId === 2 || tableId === 3) {
        this._modifiersCache = {}; // Vider tout le cache
      }

      // Notifier les abonnés que le cache a été invalidé
      this._notifySubscribers(tableId);
    }
  }

  /**
   * Met à jour un modificateur
   * @param {number} modifierId - ID du modificateur
   * @param {Object} modifier - Nouvelles données du modificateur
   * @returns {Promise<Object>} - Résultat de l'opération
   */
  async updateModifier(modifierId, modifier) {
    try {
      const data = await this.put(`${modifierId}`, modifier);
      if (data.success) {
        // Invalider le cache pour toutes les tables car nous ne savons pas à quelle table appartient ce modificateur
        this.invalidateAllCache();
        return data;
      } else {
        throw new Error(data.error || 'Failed to update modifier');
      }
    } catch (error) {
      console.error('Error updating modifier:', error);
      throw error;
    }
  }

  /**
   * Invalide tout le cache des modificateurs
   */
  invalidateAllCache() {
    console.log('ModifiersService: Invalidation de tout le cache des modificateurs');
    this._modifiersCache = {};

    // Notifier les abonnés que tout le cache a été invalidé
    this._notifySubscribers('all');
  }

  /**
   * Liste des abonnés aux changements de cache
   * @type {Array<Function>}
   * @private
   */
  _subscribers = [];

  /**
   * S'abonner aux changements de cache
   * @param {Function} callback - Fonction à appeler lorsque le cache est invalidé
   * @returns {Function} - Fonction pour se désabonner
   */
  subscribe(callback) {
    if (typeof callback !== 'function') {
      console.error('ModifiersService: subscribe requires a function as parameter');
      return () => {};
    }

    this._subscribers.push(callback);

    // Retourner une fonction pour se désabonner
    return () => {
      this._subscribers = this._subscribers.filter(cb => cb !== callback);
    };
  }

  /**
   * Notifier les abonnés que le cache a été invalidé
   * @param {string|number} tableId - ID de la table ou 'all' pour tout le cache
   * @private
   */
  _notifySubscribers(tableId) {
    this._subscribers.forEach(callback => {
      try {
        callback(tableId);
      } catch (error) {
        console.error('ModifiersService: Error notifying subscriber', error);
      }
    });
  }

  /**
   * Supprime un modificateur
   * @param {number} modifierId - ID du modificateur
   * @returns {Promise<Object>} - Résultat de l'opération
   */
  async deleteModifier(modifierId) {
    try {
      // Récupérer d'abord le modificateur pour connaître sa table
      let tableId = null;
      try {
        const modifierData = await this.get(`${modifierId}`);
        if (modifierData && modifierData.modifier && modifierData.modifier.table_id) {
          tableId = modifierData.modifier.table_id;
          console.log(`ModifiersService: Le modificateur ${modifierId} appartient à la table ${tableId}`);
        }
      } catch (error) {
        console.error('Erreur lors de la récupération du modificateur:', error);
        // Continuer même si on ne peut pas récupérer la table
      }

      const data = await this.delete(`${modifierId}`);
      if (data.success) {
        // Pour les tables de nourriture (1, 2, 3), forcer un rechargement complet
        if (tableId === 1 || tableId === 2 || tableId === 3) {
          console.log(`ModifiersService: Invalidation de tout le cache pour la table de nourriture ${tableId}`);
          // Vider complètement le cache
          this._modifiersCache = {};
          // Notifier tous les abonnés
          this._notifySubscribers('all');
        } else {
          // Pour les autres tables, invalider tout le cache comme avant
          this.invalidateAllCache();
        }
        return data;
      } else {
        throw new Error(data.error || 'Failed to delete modifier');
      }
    } catch (error) {
      console.error('Error deleting modifier:', error);
      throw error;
    }
  }

  /**
   * Récupère tous les modificateurs en une seule requête
   * @returns {Promise<Object>} - Tous les modificateurs
   */
  async getAllModifiers() {
    return this.get('all');
  }
}

// Créer une instance du service
const modifiersService = new ModifiersService();

// Rendre l'instance disponible globalement pour le débogage et l'accès depuis d'autres composants
if (typeof window !== 'undefined') {
  window.modifiersService = modifiersService;
}

// Exporter les méthodes individuelles pour maintenir la compatibilité avec le code existant
export const getModifiersByTable = modifiersService.getModifiersByTable.bind(modifiersService);
export const getFoodModifiers = modifiersService.getFoodModifiers.bind(modifiersService);
export const getHealthData = modifiersService.getHealthData.bind(modifiersService);
export const getResearchData = modifiersService.getResearchData.bind(modifiersService);
export const getTradingData = modifiersService.getTradingData.bind(modifiersService);
export const getJusticeDefenseData = modifiersService.getJusticeDefenseData.bind(modifiersService);
export const getDefenseMeans = modifiersService.getDefenseMeans.bind(modifiersService);
export const addDefenseMean = modifiersService.addDefenseMean.bind(modifiersService);
export const deleteDefenseMean = modifiersService.deleteDefenseMean.bind(modifiersService);
export const recalculateResearchProbability = modifiersService.recalculateResearchProbability.bind(modifiersService);
export const addModifier = modifiersService.addModifier.bind(modifiersService);
export const updateModifier = modifiersService.updateModifier.bind(modifiersService);
export const deleteModifier = modifiersService.deleteModifier.bind(modifiersService);

// Exporter également l'instance du service
export default modifiersService;
