/**
 * Service for handling charges-related API calls
 */
import apiEndpoint from './ApiEndpoint';

export const getChargesData = async () => {
  try {
    const data = await apiEndpoint.get('modifiers/charges');

    console.log('ChargesService: Fetched charges data:', data.chargesData);
    return data.chargesData;
  } catch (error) {
    console.error('Error fetching charges data:', error);
    throw error;
  }
};

/**
 * Calculate charges based on game state and modifiers
 */
export const calculateCharges = (gameState, jobs, modifierTables) => {
  if (!gameState || !jobs || !modifierTables) return 0;

  // Get base charges from game config
  const baseCharges = gameState.config?.find(c => c.name === 'BASE_CHARGES')?.value || 50;

  // Get modifiers from tables
  const chargesGeneralTable = modifierTables.find(t => t.id === 7);
  const chargesGlobalModifierTable = modifierTables.find(t => t.id === 8);

  // Get values or default to 0
  // For chargesGeneralTable, we use the absolute value directly (not a modifier)
  const chargesPerCycle = chargesGeneralTable?.current_value || 0;
  const chargesGlobalModifier = chargesGlobalModifierTable?.current_value || 0;

  // Calculate craftsman effect - 3% par artisan, effet max de 2 (divise les coûts par 2)
  const craftsman = jobs.find(j => j.name === 'Craftsman') || { number: 0, sick: 0, free: 0 };
  const activeCraftsmen = craftsman.number - craftsman.sick;
  const craftsmanEffect = Math.min(1, (0.03 * activeCraftsmen)); // Max 1 (ce qui donne un diviseur de 2 dans la formule)

  // Calculate salaries
  const salaries = jobs.reduce((sum, job) => sum + ((job.number - (job.free || 0)) * job.salary), 0);

  // Calculate non-salary charges
  // Formula simplifiée: FixedCharges * (1 + ChargesGlobalModifier) / (1 + CraftsmanEffect)
  // Note: chargesPerCycle is now the sum of all costs in the general charges table
  const fixedCharges = chargesPerCycle; // Use the sum directly, no need to add baseCharges

  // Nous n'utilisons plus la technologie ni le moral pour les charges
  // L'effet des artisans est limité à 1 (diviseur max de 2)
  const nonSalaryCharges = fixedCharges * (1 + chargesGlobalModifier) / (1 + craftsmanEffect);

  // Total charges
  const totalCharges = salaries + nonSalaryCharges;

  return {
    salaries,
    nonSalaryCharges,
    totalCharges,
    craftsmanEffect,
    baseCharges: fixedCharges,
    modifiers: {
      chargesPerCycle,
      chargesGlobalModifier
    }
  };
};

/**
 * Get all charges-related modifiers and stats directly
 * This is useful for updating the UI after modifiers change
 */
export const fetchAndCalculateChargesData = async (gameState) => {
  try {
    // Fetch the latest charges data from the server
    const chargesData = await getChargesData();

    // If we have gameState, calculate the charges
    if (gameState) {
      // Calculate charges
      const charges = calculateCharges(
        gameState.gameState,
        gameState.jobs,
        gameState.modifierTables
      );

      return {
        ...chargesData,
        stats: {
          ...chargesData.stats,
          ...charges
        }
      };
    }

    return chargesData;
  } catch (error) {
    console.error('Error fetching and calculating charges data:', error);
    throw error;
  }
};
