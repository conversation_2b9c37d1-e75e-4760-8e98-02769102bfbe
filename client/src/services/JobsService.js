/**
 * Service pour les opérations liées aux métiers
 */
import { BaseService } from './BaseService';

class JobsService extends BaseService {
  constructor() {
    super('jobs');
  }

  /**
   * Récupère tous les métiers
   * @returns {Promise<Array>} - Liste des métiers
   */
  async getJobs() {
    return this.get();
  }

  /**
   * Récupère un métier par son ID
   * @param {number} id - ID du métier
   * @returns {Promise<Object>} - Donn<PERSON> du métier
   */
  async getJob(id) {
    return this.get(`${id}`);
  }

  /**
   * Met à jour un métier
   * @param {number} id - ID du métier
   * @param {Object} job - Nouvelles données du métier
   * @returns {Promise<Object>} - Résultat de l'opération
   */
  async updateJob(id, job) {
    return this.put(`${id}`, job);
  }

  /**
   * Change le nombre de travailleurs pour un métier
   * @param {number} jobId - ID du métier
   * @param {number} change - Changement à appliquer
   * @returns {Promise<Object>} - Résultat de l'opération
   */
  async changeJobNumber(jobId, change) {
    return this.post('change', { jobId, change });
  }

  /**
   * Change le nombre de travailleurs libres
   * @param {number} jobId - ID du métier
   * @param {number} change - Changement à appliquer
   * @returns {Promise<Object>} - Résultat de l'opération
   */
  async changeFreeWorkers(jobId, change) {
    return this.post('free', { jobId, change });
  }

  /**
   * Transfère des travailleurs d'un métier à un autre
   * @param {number} fromJobId - ID du métier source
   * @param {number} toJobId - ID du métier destination
   * @param {number} count - Nombre de travailleurs à transférer
   * @returns {Promise<Object>} - Résultat de l'opération
   */
  async transferJob(fromJobId, toJobId, count) {
    return this.post('transfer', { fromJobId, toJobId, count });
  }
}

// Créer une instance du service
const jobsService = new JobsService();

// Exporter les méthodes individuelles pour maintenir la compatibilité avec le code existant
export const getJobs = jobsService.getJobs.bind(jobsService);
export const getJob = jobsService.getJob.bind(jobsService);
export const updateJob = jobsService.updateJob.bind(jobsService);
export const changeJobNumber = jobsService.changeJobNumber.bind(jobsService);
export const changeFreeWorkers = jobsService.changeFreeWorkers.bind(jobsService);
export const transferJob = jobsService.transferJob.bind(jobsService);

// Exporter également l'instance du service
export default jobsService;
