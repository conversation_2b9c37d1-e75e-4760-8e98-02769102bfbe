/**
 * Service pour centraliser toutes les fonctions de récupération de données
 * Fournit des méthodes pour récupérer et mettre en cache les données du jeu
 *
 * IMPORTANT: This service is being phased out. Use ApiEndpoint or individual services directly.
 * This service is maintained for backward compatibility only.
 */
import { BaseService } from './BaseService';
import apiEndpoint from './ApiEndpoint';
import calculationServiceEnhanced from './CalculationServiceEnhanced';
import cacheService from './CacheService';

// Durée de validité du cache en millisecondes
const CACHE_DURATION = 5000; // 5 secondes

class DataService extends BaseService {
  constructor() {
    super('data');
  }

  /**
   * Vérifie si le cache est valide pour une clé donnée
   * @param {string} key - Clé du cache
   * @param {string} subKey - Sous-clé du cache (optionnel)
   * @returns {boolean} - true si le cache est valide, false sinon
   */
  isCacheValid(key, subKey = null) {
    return cacheService.isValid(key, subKey, CACHE_DURATION);
  }

  /**
   * Met à jour le cache pour une clé donnée
   * @param {string} key - Clé du cache
   * @param {*} data - Données à mettre en cache
   * @param {string} subKey - Sous-clé du cache (optionnel)
   */
  updateCache(key, data, subKey = null) {
    cacheService.update(key, data, subKey);
  }

  /**
   * Récupère l'état du jeu
   * @param {boolean} forceRefresh - Forcer le rafraîchissement du cache
   * @returns {Promise<Object>} - État du jeu
   */
  async getGameState(forceRefresh = false) {
    if (!forceRefresh && this.isCacheValid('gameState')) {
      return cacheService.get('gameState');
    }

    const data = await apiEndpoint.get('game/state');
    this.updateCache('gameState', data);

    return data;
  }

  /**
   * Récupère les événements du jeu
   * @param {boolean} forceRefresh - Forcer le rafraîchissement du cache
   * @returns {Promise<Array>} - Liste des événements
   */
  async getEvents(forceRefresh = false) {
    if (!forceRefresh && this.isCacheValid('events')) {
      return cacheService.get('events');
    }

    const data = await apiEndpoint.get('game/events');
    this.updateCache('events', data);

    return data;
  }

  /**
   * Récupère les modificateurs pour une table
   * @param {number} tableId - ID de la table
   * @param {boolean} forceRefresh - Forcer le rafraîchissement du cache
   * @returns {Promise<Array>} - Liste des modificateurs
   */
  async getModifiersByTable(tableId, forceRefresh = false) {
    if (!forceRefresh && this.isCacheValid('modifiers', tableId)) {
      return cacheService.get('modifiers', tableId);
    }

    const data = await apiEndpoint.get(`modifiers/table/${tableId}`);
    this.updateCache('modifiers', data, tableId);

    return data;
  }

  /**
   * Récupère tous les modificateurs
   * @param {boolean} forceRefresh - Forcer le rafraîchissement du cache
   * @returns {Promise<Object>} - Tous les modificateurs
   */
  async getAllModifiers(forceRefresh = false) {
    if (!forceRefresh && this.isCacheValid('modifiers', 'all')) {
      return cacheService.get('modifiers', 'all');
    }

    const data = await apiEndpoint.get('modifiers/all');
    this.updateCache('modifiers', data, 'all');

    return data;
  }

  /**
   * Récupère la liste des habitants
   * @param {Object} params - Paramètres de filtrage
   * @param {boolean} forceRefresh - Forcer le rafraîchissement du cache
   * @returns {Promise<Object>} - Liste des habitants et métadonnées
   */
  async getInhabitants(params = {}, forceRefresh = false) {
    // Ne pas mettre en cache les requêtes avec des paramètres de filtrage
    if (Object.keys(params).length > 0 || forceRefresh || !this.isCacheValid('inhabitants')) {
      const data = await apiEndpoint.get('inhabitants', { params });

      // Ne mettre en cache que si aucun paramètre de filtrage n'est spécifié
      if (Object.keys(params).length === 0) {
        this.updateCache('inhabitants', data);
      }

      return data;
    }

    return cacheService.get('inhabitants');
  }

  /**
   * Récupère toutes les statistiques du jeu
   * @param {boolean} forceRefresh - Forcer le rafraîchissement du cache
   * @returns {Promise<Object>} - Toutes les statistiques
   */
  async getAllStats(forceRefresh = false) {
    if (!forceRefresh && this.isCacheValid('stats')) {
      return cacheService.get('stats');
    }

    const data = await calculationServiceEnhanced.getAllStats();
    this.updateCache('stats', data);

    return data;
  }

  /**
   * Invalide le cache pour une clé donnée
   * @param {string} key - Clé du cache
   * @param {string} subKey - Sous-clé du cache (optionnel)
   */
  invalidateCache(key, subKey = null) {
    cacheService.invalidate(key, subKey);
  }

  /**
   * Invalide tout le cache
   */
  invalidateAllCache() {
    cacheService.invalidateAll();
  }

  /**
   * Charge toutes les données initiales du jeu
   * @returns {Promise<Object>} - Toutes les données du jeu
   */
  async loadInitialData() {
    const [gameState, events, modifiers, stats] = await Promise.all([
      this.getGameState(true),
      this.getEvents(true),
      this.getAllModifiers(true),
      this.getAllStats(true)
    ]);

    return {
      gameState,
      events,
      modifiers,
      stats
    };
  }
}

// Créer une instance du service
const dataService = new DataService();

// Exporter l'instance par défaut
export default dataService;
