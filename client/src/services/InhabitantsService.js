/**
 * Service pour les opérations liées aux habitants
 */
import { BaseService } from './BaseService';

class InhabitantsService extends BaseService {
  constructor() {
    super('inhabitants');
  }

  /**
   * Récupère la liste des habitants avec filtrage optionnel
   * @param {Object} params - Paramètres de filtrage
   * @returns {Promise<Object>} - Liste des habitants et métadonnées
   */
  async getInhabitants(params = {}) {
    // Construire les paramètres de requête
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        queryParams.append(key, value);
      }
    });
    
    // Effectuer la requête
    return this.get(`?${queryParams}`);
  }

  /**
   * Récupère un habitant par son ID
   * @param {number} id - ID de l'habitant
   * @returns {Promise<Object>} - Données de l'habitant
   */
  async getInhabitant(id) {
    return this.get(`${id}`);
  }

  /**
   * Crée un nouvel habitant
   * @param {Object} inhabitant - Donn<PERSON> de l'habitant
   * @returns {Promise<Object>} - Résultat de l'opération
   */
  async createInhabitant(inhabitant) {
    return this.post('', inhabitant);
  }

  /**
   * Met à jour un habitant
   * @param {number} id - ID de l'habitant
   * @param {Object} inhabitant - Nouvelles données de l'habitant
   * @returns {Promise<Object>} - Résultat de l'opération
   */
  async updateInhabitant(id, inhabitant) {
    console.log('API: Updating inhabitant', { id, inhabitant });

    // Si le job_id est modifié, utiliser une approche spéciale
    if (inhabitant.job_id !== undefined) {
      console.log('API: Detected job change, using direct API call');
      return this.put(`${id}`, inhabitant);
    }

    // Sinon, utiliser l'approche normale
    return this.put(`${id}`, inhabitant);
  }

  /**
   * Supprime un habitant
   * @param {number} id - ID de l'habitant
   * @returns {Promise<Object>} - Résultat de l'opération
   */
  async deleteInhabitant(id) {
    return this.delete(`${id}`);
  }

  /**
   * Synchronise les habitants avec les métiers
   * @returns {Promise<Object>} - Résultat de l'opération
   */
  async syncInhabitantsWithJobs() {
    return this.put('sync-jobs');
  }

  /**
   * Récupère les statistiques de population
   * @returns {Promise<Object>} - Statistiques de population
   */
  async getPopulationStats() {
    return this.get('stats');
  }
}

// Créer une instance du service
const inhabitantsService = new InhabitantsService();

// Exporter les méthodes individuelles pour maintenir la compatibilité avec le code existant
export const getInhabitants = inhabitantsService.getInhabitants.bind(inhabitantsService);
export const getInhabitant = inhabitantsService.getInhabitant.bind(inhabitantsService);
export const createInhabitant = inhabitantsService.createInhabitant.bind(inhabitantsService);
export const updateInhabitant = inhabitantsService.updateInhabitant.bind(inhabitantsService);
export const deleteInhabitant = inhabitantsService.deleteInhabitant.bind(inhabitantsService);
export const syncInhabitantsWithJobs = inhabitantsService.syncInhabitantsWithJobs.bind(inhabitantsService);
export const getPopulationStats = inhabitantsService.getPopulationStats.bind(inhabitantsService);

// Exporter également l'instance du service
export default inhabitantsService;
