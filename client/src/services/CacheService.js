/**
 * Service pour centraliser la gestion du cache
 * Fournit des méthodes pour mettre en cache et récupérer des données
 */

// Durée de validité du cache en millisecondes
const DEFAULT_CACHE_DURATION = 5000; // 5 secondes

class CacheService {
  constructor() {
    // Initialiser le cache
    this.cache = {};
    this.lastUpdated = {};
  }

  /**
   * Vérifie si le cache est valide pour une clé donnée
   * @param {string} key - Clé du cache
   * @param {string} subKey - Sous-clé du cache (optionnel)
   * @param {number} duration - Durée de validité du cache en millisecondes (optionnel)
   * @returns {boolean} - true si le cache est valide, false sinon
   */
  isValid(key, subKey = null, duration = DEFAULT_CACHE_DURATION) {
    const now = Date.now();

    if (subKey) {
      return (
        this.lastUpdated[key] &&
        this.lastUpdated[key][subKey] &&
        now - this.lastUpdated[key][subKey] < duration
      );
    }

    return (
      this.lastUpdated[key] &&
      now - this.lastUpdated[key] < duration
    );
  }

  /**
   * Met à jour le cache pour une clé donnée
   * @param {string} key - Clé du cache
   * @param {*} data - Données à mettre en cache
   * @param {string} subKey - Sous-clé du cache (optionnel)
   */
  update(key, data, subKey = null) {
    const now = Date.now();

    if (subKey) {
      if (!this.cache[key]) {
        this.cache[key] = {};
      }

      if (!this.lastUpdated[key]) {
        this.lastUpdated[key] = {};
      }

      this.cache[key][subKey] = data;
      this.lastUpdated[key][subKey] = now;
    } else {
      this.cache[key] = data;
      this.lastUpdated[key] = now;
    }
  }

  /**
   * Récupère les données du cache pour une clé donnée
   * @param {string} key - Clé du cache
   * @param {string} subKey - Sous-clé du cache (optionnel)
   * @returns {*} - Données du cache
   */
  get(key, subKey = null) {
    if (subKey) {
      return this.cache[key] && this.cache[key][subKey];
    }

    return this.cache[key];
  }

  /**
   * Invalide le cache pour une clé donnée
   * @param {string} key - Clé du cache
   * @param {string} subKey - Sous-clé du cache (optionnel)
   */
  invalidate(key, subKey = null) {
    console.log(`CacheService: Invalidation du cache pour ${key}${subKey ? '/' + subKey : ''}`);

    if (subKey) {
      if (this.lastUpdated[key]) {
        // Supprimer complètement la sous-clé au lieu de la mettre à null
        delete this.lastUpdated[key][subKey];

        if (this.cache[key]) {
          delete this.cache[key][subKey];
        }
      }
    } else {
      // Supprimer complètement la clé au lieu de la mettre à null
      delete this.lastUpdated[key];
      delete this.cache[key];
    }
  }

  /**
   * Invalide tout le cache
   */
  invalidateAll() {
    console.log('CacheService: Invalidation de tout le cache');

    // Vider complètement le cache au lieu de mettre les entrées à null
    this.cache = {};
    this.lastUpdated = {};
  }

  /**
   * Supprime une entrée du cache
   * @param {string} key - Clé du cache
   * @param {string} subKey - Sous-clé du cache (optionnel)
   */
  remove(key, subKey = null) {
    if (subKey) {
      if (this.cache[key]) {
        delete this.cache[key][subKey];
      }

      if (this.lastUpdated[key]) {
        delete this.lastUpdated[key][subKey];
      }
    } else {
      delete this.cache[key];
      delete this.lastUpdated[key];
    }
  }

  /**
   * Vide tout le cache
   */
  clear() {
    this.cache = {};
    this.lastUpdated = {};
  }
}

// Créer une instance du service
const cacheService = new CacheService();

// Exporter l'instance par défaut
export default cacheService;
