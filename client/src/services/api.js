/**
 * API Service for handling all API calls
 * This centralizes all API calls and provides consistent error handling
 *
 * Note: Ce fichier est maintenu pour la compatibilité avec le code existant.
 * Les nouveaux services standardisés doivent être utilisés pour les nouvelles fonctionnalités.
 *
 * IMPORTANT: This file is being phased out. Use ApiEndpoint or individual services directly.
 */

// Import ApiEndpoint for future migration
import apiEndpoint from './ApiEndpoint';

// Import des services standardisés
import GameService from './GameService';
import ModifiersService from './ModifiersService';
import InhabitantsService from './InhabitantsService';

/**
 * Game API endpoints
 */
export const gameApi = {
  // Get game state
  getGameState: () => GameService.getGameState(),

  // Get events
  getEvents: () => GameService.getEvents(),

  // Process next cycle
  processNextCycle: () => GameService.processNextCycle(),

  // Change job numbers
  changeJobNumber: (jobId, change) => GameService.changeJobNumber(jobId, change),

  // Change worker numbers
  changeWorkerNumber: (change) => GameService.changeWorkerNumber(change),

  // Change free workers
  changeFreeWorkers: (jobId, change) => GameService.changeFreeWorkers(jobId, change),

  // Transfer job
  transferJob: (fromJobId, toJobId, count) => GameService.transferJob(fromJobId, toJobId, count)
};

/**
 * Modifiers API endpoints
 */
export const modifiersApi = {
  // Get modifiers by table
  getModifiersByTable: (tableId) => ModifiersService.getModifiersByTable(tableId),

  // Get defense means
  getDefenseMeans: () => ModifiersService.getDefenseMeans(),

  // Add defense mean
  addDefenseMean: (defenseMean) => ModifiersService.addDefenseMean(defenseMean),

  // Delete defense mean
  deleteDefenseMean: (id) => ModifiersService.deleteDefenseMean(id),

  // Add modifier
  addModifier: (tableId, modifier) => ModifiersService.addModifier(tableId, modifier),

  // Update modifier
  updateModifier: (id, modifier) => ModifiersService.updateModifier(id, modifier),

  // Delete modifier
  deleteModifier: (id) => ModifiersService.deleteModifier(id),

  // Get moral data
  getMoralData: () => ModifiersService.getMoralData(),

  // Get mining data
  getMiningData: () => ModifiersService.getMiningData(),

  // Get food data
  getFoodData: () => ModifiersService.getFoodData(),

  // Get materials data
  getMaterialsData: () => ModifiersService.getMaterialsData(),

  // Get charges data
  getChargesData: () => ModifiersService.getChargesData(),

  // Get health data
  getHealthData: () => ModifiersService.getHealthData(),

  // Get research data
  getResearchData: () => ModifiersService.getResearchData(),

  // Get trading data
  getTradingData: () => ModifiersService.getTradingData(),

  // Get justice and defense data
  getJusticeDefenseData: () => ModifiersService.getJusticeDefenseData(),

  // Get renown data
  getRenownData: () => ModifiersService.getRenownData()
};

/**
 * Inhabitants API endpoints
 */
export const inhabitantsApi = {
  // Get inhabitants
  getInhabitants: (params = {}) => InhabitantsService.getInhabitants(params),

  // Get inhabitant by ID
  getInhabitant: (id) => InhabitantsService.getInhabitant(id),

  // Create inhabitant
  createInhabitant: (inhabitant) => InhabitantsService.createInhabitant(inhabitant),

  // Update inhabitant
  updateInhabitant: (id, inhabitant) => InhabitantsService.updateInhabitant(id, inhabitant),

  // Delete inhabitant
  deleteInhabitant: (id) => InhabitantsService.deleteInhabitant(id),

  // Sync inhabitants with jobs
  syncInhabitantsWithJobs: () => InhabitantsService.syncInhabitantsWithJobs(),

  // Get population stats
  getPopulationStats: () => InhabitantsService.getPopulationStats()
};

// Exporter un objet nommé pour éviter l'erreur ESLint
const api = {
  game: gameApi,
  modifiers: modifiersApi,
  inhabitants: inhabitantsApi
};

export default api;
