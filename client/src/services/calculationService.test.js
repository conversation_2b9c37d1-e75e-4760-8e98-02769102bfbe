import {
  calculateMiningProduction,
  calculateRevenuePerMiner,
  calculateEngineeringMultiplier,
  calculateTotalBonus,
  calculateMoralModifier,
  MINING_VALUE
} from './CalculationService';

describe('Calculation Service', () => {
  describe('calculateMiningProduction', () => {
    test('should calculate mining production correctly', () => {
      const activeMiner = 10;
      const generalEffectsTotal = 0.1;
      const techEffectsTotal = 0.2;
      const moralModifier = 0.1;
      const engineeringMultiplier = 0.12;

      const expected = activeMiner * MINING_VALUE * (1 + generalEffectsTotal + techEffectsTotal + moralModifier) * (1 + engineeringMultiplier);
      const result = calculateMiningProduction(activeMiner, generalEffectsTotal, techEffectsTotal, moralModifier, engineeringMultiplier);

      expect(result).toEqual(expected);
    });

    test('should return 0 when activeMiner is 0', () => {
      const activeMiner = 0;
      const generalEffectsTotal = 0.1;
      const techEffectsTotal = 0.2;
      const moralModifier = 0.1;
      const engineeringMultiplier = 0.12;

      const result = calculateMiningProduction(activeMiner, generalEffectsTotal, techEffectsTotal, moralModifier, engineeringMultiplier);

      expect(result).toEqual(0);
    });
  });

  describe('calculateRevenuePerMiner', () => {
    test('should calculate revenue per miner correctly when activeMiner > 0', () => {
      const activeMiner = 10;
      const miningProduction = 400;
      const generalEffectsTotal = 0.1;
      const techEffectsTotal = 0.2;
      const moralModifier = 0.1;
      const engineeringMultiplier = 0.12;

      const expected = miningProduction / activeMiner;
      const result = calculateRevenuePerMiner(activeMiner, miningProduction, generalEffectsTotal, techEffectsTotal, moralModifier, engineeringMultiplier);

      expect(result).toEqual(expected);
    });

    test('should calculate theoretical revenue per miner when activeMiner is 0', () => {
      const activeMiner = 0;
      const miningProduction = 0;
      const generalEffectsTotal = 0.1;
      const techEffectsTotal = 0.2;
      const moralModifier = 0.1;
      const engineeringMultiplier = 0.12;

      const expected = MINING_VALUE * (1 + generalEffectsTotal + techEffectsTotal + moralModifier) * (1 + engineeringMultiplier);
      const result = calculateRevenuePerMiner(activeMiner, miningProduction, generalEffectsTotal, techEffectsTotal, moralModifier, engineeringMultiplier);

      expect(result).toEqual(expected);
    });
  });

  describe('calculateEngineeringMultiplier', () => {
    test('should calculate engineering multiplier correctly', () => {
      const activeEngineers = 5;
      const engineeringEffectsTotal = 0.2;

      const expected = activeEngineers * 0.03 * (1 + engineeringEffectsTotal);
      const result = calculateEngineeringMultiplier(activeEngineers, engineeringEffectsTotal);

      expect(result).toEqual(expected);
    });

    test('should return 0 when activeEngineers is 0', () => {
      const activeEngineers = 0;
      const engineeringEffectsTotal = 0.2;

      const result = calculateEngineeringMultiplier(activeEngineers, engineeringEffectsTotal);

      expect(result).toEqual(0);
    });
  });

  describe('calculateTotalBonus', () => {
    test('should calculate total bonus correctly', () => {
      const generalEffectsTotal = 0.1;
      const techEffectsTotal = 0.2;
      const moralModifier = 0.1;
      const engineeringMultiplier = 0.12;

      const expected = (1 + generalEffectsTotal + techEffectsTotal + moralModifier) * (1 + engineeringMultiplier) - 1;
      const result = calculateTotalBonus(generalEffectsTotal, techEffectsTotal, moralModifier, engineeringMultiplier);

      expect(result).toEqual(expected);
    });
  });

  describe('calculateMoralModifier', () => {
    test('should calculate moral modifier correctly', () => {
      const moralValue = 1.1;

      const expected = 0.1;
      const result = calculateMoralModifier(moralValue);

      // Utiliser toBeCloseTo au lieu de toEqual pour les nombres à virgule flottante
      expect(result).toBeCloseTo(expected, 10);
    });

    test('should return 0 when moralValue is 1.0', () => {
      const moralValue = 1.0;

      const result = calculateMoralModifier(moralValue);

      expect(result).toEqual(0);
    });

    test('should return negative value when moralValue is less than 1.0', () => {
      const moralValue = 0.9;

      const expected = -0.1;
      const result = calculateMoralModifier(moralValue);

      // Utiliser toBeCloseTo au lieu de toEqual pour les nombres à virgule flottante
      expect(result).toBeCloseTo(expected, 10);
    });
  });
});
