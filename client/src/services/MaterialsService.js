/**
 * Service for handling materials-related API calls
 */
import apiEndpoint from './ApiEndpoint';

export const getMaterialsData = async () => {
    try {
        const data = await apiEndpoint.get('modifiers/materials');
        return data.materialsData;
    } catch (error) {
        console.error('Error fetching materials data:', error);
        throw error;
    }
};

/**
 * Calculate materials production based on workers, miners, and modifiers
 * @param {Object} gameState - Current game state
 * @param {Array} jobs - Array of jobs
 * @param {Array} modifierTables - Array of modifier tables
 * @param {Number} moralModifier - Moral modifier value
 * @returns {Number} - Materials production value
 */
export const calculateMaterialsProduction = (gameState, jobs, modifierTables, moralModifier) => {
    try {
        // Get workers
        const workers = jobs.find(j => j.name === 'Worker') || { number: 0, sick: 0 };
        const activeWorkers = workers.number - workers.sick;

        // Get miners
        const miners = jobs.find(j => j.name === 'Miner') || { number: 0, sick: 0 };
        const activeMiners = miners.number - miners.sick;

        // Get modifier values
        const materialsGeneralModifier = getModifierValue(modifierTables, 11);
        const materialsTechModifier = getModifierValue(modifierTables, 12);

        // Constants
        const MATERIALS_PER_WORKER = 4;
        const MATERIALS_PER_MINER = 2;

        // Calculate materials production
        const materialsProduction =
            (activeWorkers * MATERIALS_PER_WORKER +
             activeMiners * MATERIALS_PER_MINER) *
            (1 + materialsGeneralModifier + materialsTechModifier + moralModifier);

        return materialsProduction;
    } catch (error) {
        console.error('Error calculating materials production:', error);
        return 0; // Default to 0 if calculation fails
    }
};

/**
 * Get a modifier value from the modifier tables
 * @param {Array} modifierTables - Array of modifier tables
 * @param {Number} tableId - ID of the table to get the value from
 * @returns {Number} - Modifier value
 */
const getModifierValue = (modifierTables, tableId) => {
    const table = modifierTables.find(t => t.id === tableId);
    return table ? table.current_value : 0;
};
