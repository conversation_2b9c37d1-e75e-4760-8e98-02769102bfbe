/**
 * Service pour les opérations liées au jeu
 */
import { BaseService } from './BaseService';
import cacheService from './CacheService';

// Constantes pour les clés de cache
const CACHE_KEYS = {
  GAME_STATE: 'gameState',
  EVENTS: 'events',
  DASHBOARD: 'dashboard'
};

// Durée de validité du cache en millisecondes
const CACHE_DURATION = {
  GAME_STATE: 2000, // 2 secondes
  EVENTS: 5000,     // 5 secondes
  DASHBOARD: 3000   // 3 secondes
};

class GameService extends BaseService {
  constructor() {
    super('game');
    this._requestsInProgress = {};
  }

  /**
   * Récupère l'état du jeu avec mise en cache
   * @param {boolean} force - Forcer la récupération même si le cache est valide
   * @returns {Promise<Object>} - État du jeu
   */
  async getGameState(force = false) {
    // Si une requête est déjà en cours, attendre son résultat
    if (this._requestsInProgress[CACHE_KEYS.GAME_STATE]) {
      return this._requestsInProgress[CACHE_KEYS.GAME_STATE];
    }

    try {
      // Si le cache est valide et qu'on ne force pas la récupération, utiliser le cache
      if (!force && cacheService.isValid(CACHE_KEYS.GAME_STATE, null, CACHE_DURATION.GAME_STATE)) {
        const cachedData = cacheService.get(CACHE_KEYS.GAME_STATE);

        // Vérifier que les données du cache sont valides
        if (cachedData && typeof cachedData === 'object') {
          return cachedData;
        } else {
          // Invalider le cache
          cacheService.invalidate(CACHE_KEYS.GAME_STATE);
        }
      }

      // Créer une promesse avec timeout
      const fetchPromise = this.get('state');
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Timeout lors de la récupération des données du jeu')), 10000);
      });

      // Utiliser Promise.race pour implémenter un timeout
      this._requestsInProgress[CACHE_KEYS.GAME_STATE] = Promise.race([fetchPromise, timeoutPromise]);

      // Attendre le résultat
      const result = await this._requestsInProgress[CACHE_KEYS.GAME_STATE];

      // Vérifier que le résultat est valide
      if (!result || typeof result !== 'object') {
        throw new Error('Résultat invalide reçu du serveur');
      }

      // Mettre en cache le résultat
      cacheService.update(CACHE_KEYS.GAME_STATE, result);

      return result;
    } catch (error) {
      console.error('GameService.getGameState: Erreur lors de la récupération', error);

      // Essayer de récupérer les données du cache même si elles sont expirées
      const cachedData = cacheService.get(CACHE_KEYS.GAME_STATE);
      if (cachedData && typeof cachedData === 'object') {
        return cachedData;
      }

      throw error;
    } finally {
      // Supprimer la référence à la requête en cours
      this._requestsInProgress[CACHE_KEYS.GAME_STATE] = null;
    }
  }

  /**
   * Récupère les événements du jeu avec mise en cache
   * @param {boolean} force - Forcer la récupération même si le cache est valide
   * @returns {Promise<Array>} - Liste des événements
   */
  async getEvents(force = false) {
    // Si une requête est déjà en cours, attendre son résultat
    if (this._requestsInProgress[CACHE_KEYS.EVENTS]) {
      console.log('GameService: Une requête getEvents est déjà en cours, attente du résultat');
      return this._requestsInProgress[CACHE_KEYS.EVENTS];
    }

    try {
      // Si le cache est valide et qu'on ne force pas la récupération, utiliser le cache
      if (!force && cacheService.isValid(CACHE_KEYS.EVENTS, null, CACHE_DURATION.EVENTS)) {
        console.log('GameService: Utilisation du cache pour getEvents');
        return cacheService.get(CACHE_KEYS.EVENTS);
      }

      // Sinon, faire la requête et mettre en cache le résultat
      console.log('GameService: Récupération des événements depuis le serveur');

      // Créer une promesse pour la requête en cours
      this._requestsInProgress[CACHE_KEYS.EVENTS] = this.get('events');

      // Attendre le résultat
      const result = await this._requestsInProgress[CACHE_KEYS.EVENTS];

      // Mettre en cache le résultat
      cacheService.update(CACHE_KEYS.EVENTS, result);

      return result;
    } finally {
      // Supprimer la référence à la requête en cours
      this._requestsInProgress[CACHE_KEYS.EVENTS] = null;
    }
  }

  /**
   * Exécute un cycle de jeu
   * @returns {Promise<Object>} - Résultat du cycle
   */
  async processNextCycle() {
    try {
      const result = await this.post('cycle');

      // Invalider tous les caches après un cycle
      cacheService.invalidateAll();

      return result;
    } catch (error) {
      console.error('GameService: Erreur lors de l\'exécution du cycle', error);
      throw error;
    }
  }

  /**
   * Change le nombre de travailleurs pour un métier
   * @param {number} jobId - ID du métier
   * @param {number} change - Changement à appliquer
   * @returns {Promise<Object>} - Résultat de l'opération
   */
  async changeJobNumber(jobId, change) {
    try {
      const result = await this.post('jobs/change', { jobId, change });

      // Invalider le cache de l'état du jeu
      cacheService.invalidate(CACHE_KEYS.GAME_STATE);

      return result;
    } catch (error) {
      console.error(`GameService: Erreur lors du changement du nombre de travailleurs pour le métier ${jobId}`, error);
      throw error;
    }
  }

  /**
   * Change le nombre de travailleurs libres
   * @param {number} change - Changement à appliquer
   * @returns {Promise<Object>} - Résultat de l'opération
   */
  async changeWorkerNumber(change) {
    try {
      const result = await this.post('workers/change', { change });

      // Invalider le cache de l'état du jeu
      cacheService.invalidate(CACHE_KEYS.GAME_STATE);

      return result;
    } catch (error) {
      console.error('GameService: Erreur lors du changement du nombre de travailleurs libres', error);
      throw error;
    }
  }

  /**
   * Change le nombre de travailleurs libres pour un métier
   * @param {number} jobId - ID du métier
   * @param {number} change - Changement à appliquer
   * @returns {Promise<Object>} - Résultat de l'opération
   */
  async changeFreeWorkers(jobId, change) {
    try {
      const result = await this.post('jobs/free', { jobId, change });

      // Invalider le cache de l'état du jeu
      cacheService.invalidate(CACHE_KEYS.GAME_STATE);

      return result;
    } catch (error) {
      console.error(`GameService: Erreur lors du changement du nombre de travailleurs libres pour le métier ${jobId}`, error);
      throw error;
    }
  }

  /**
   * Transfère des travailleurs d'un métier à un autre
   * @param {number} fromJobId - ID du métier source
   * @param {number} toJobId - ID du métier destination
   * @param {number} count - Nombre de travailleurs à transférer
   * @returns {Promise<Object>} - Résultat de l'opération
   */
  async transferJob(fromJobId, toJobId, count) {
    try {
      const result = await this.post('job-transfer', { fromJobId, toJobId, count });

      // Invalider le cache de l'état du jeu
      cacheService.invalidate(CACHE_KEYS.GAME_STATE);

      return result;
    } catch (error) {
      console.error(`GameService: Erreur lors du transfert de travailleurs de ${fromJobId} à ${toJobId}`, error);
      throw error;
    }
  }

  /**
   * Charge toutes les données du tableau de bord en une seule requête
   * @param {boolean} force - Forcer la récupération même si le cache est valide
   * @returns {Promise<Object>} - Données du tableau de bord
   */
  async getDashboardData(force = false) {
    // Si une requête est déjà en cours, attendre son résultat
    if (this._requestsInProgress[CACHE_KEYS.DASHBOARD]) {
      console.log('GameService: Une requête getDashboardData est déjà en cours, attente du résultat');
      return this._requestsInProgress[CACHE_KEYS.DASHBOARD];
    }

    try {
      // Si le cache est valide et qu'on ne force pas la récupération, utiliser le cache
      if (!force && cacheService.isValid(CACHE_KEYS.DASHBOARD, null, CACHE_DURATION.DASHBOARD)) {
        console.log('GameService: Utilisation du cache pour getDashboardData');
        return cacheService.get(CACHE_KEYS.DASHBOARD);
      }

      // Sinon, faire la requête et mettre en cache le résultat
      console.log('GameService: Récupération des données du tableau de bord depuis le serveur');

      // Créer une promesse pour la requête en cours
      this._requestsInProgress[CACHE_KEYS.DASHBOARD] = this.get('dashboard');

      // Attendre le résultat
      const result = await this._requestsInProgress[CACHE_KEYS.DASHBOARD];

      // Mettre en cache le résultat
      cacheService.update(CACHE_KEYS.DASHBOARD, result);

      return result;
    } catch (error) {
      console.error('GameService: Erreur lors de la récupération des données du tableau de bord', error);
      throw error;
    } finally {
      // Supprimer la référence à la requête en cours
      this._requestsInProgress[CACHE_KEYS.DASHBOARD] = null;
    }
  }
}

export default new GameService();
