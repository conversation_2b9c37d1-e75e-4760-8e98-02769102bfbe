/**
 * Service pour les calculs et statistiques du jeu
 * Ce service centralise tous les calculs complexes pour éviter la duplication de code
 */
import { BaseService } from './BaseService';

class StatsService extends BaseService {
  constructor() {
    super('stats');
  }

  /**
   * Constantes utilisées dans les calculs
   */
  static CONSTANTS = {
    FOOD_PER_FARMER: 4,
    MATERIALS_PER_WORKER: 1,
    MATERIALS_PER_MINER: 0.5,
    MINING_VALUE: 30,
    TRADING_VALUE: 25
  };

  /**
   * Calcule la production de nourriture
   * @param {Number} activeFarmers - Nombre de fermiers actifs
   * @param {Number} seasonFactor - Facteur saisonnier
   * @param {Number} generalEffectsTotal - Total des effets généraux
   * @param {Number} techEffectsTotal - Total des effets technologiques
   * @param {Number} moralModifier - Modificateur de moral
   * @returns {Number} - Production de nourriture
   */
  calculateFoodProduction(activeFarmers, seasonFactor, generalEffectsTotal, techEffectsTotal, moralModifier) {
    // Normaliser les valeurs
    const farmers = typeof activeFarmers === 'number' ? activeFarmers : parseFloat(activeFarmers) || 0;
    const season = typeof seasonFactor === 'number' ? seasonFactor : parseFloat(seasonFactor) || 1.0;
    const generalEffects = typeof generalEffectsTotal === 'number' ? generalEffectsTotal : parseFloat(generalEffectsTotal) || 0;
    const techEffects = typeof techEffectsTotal === 'number' ? techEffectsTotal : parseFloat(techEffectsTotal) || 0;
    const moral = typeof moralModifier === 'number' ? moralModifier : parseFloat(moralModifier) || 0;

    // Calculer la production de base
    const baseProduction = farmers * StatsService.CONSTANTS.FOOD_PER_FARMER;

    // Appliquer le facteur saisonnier
    const seasonalProduction = baseProduction * season;

    // Appliquer les modificateurs
    const modifierMultiplier = 1 + generalEffects + techEffects + moral;

    // Calculer la production finale
    const foodProduction = seasonalProduction * modifierMultiplier;

    // Arrondir à 1 décimale
    return Math.round(foodProduction * 10) / 10;
  }

  /**
   * Calcule la consommation de nourriture
   * @param {Array} jobs - Tableau des métiers
   * @returns {Number} - Consommation de nourriture
   */
  calculateFoodConsumption(jobs) {
    // Chaque habitant consomme 1 unité de nourriture par cycle
    const totalInhabitants = jobs.reduce((sum, job) => sum + job.number, 0) || 0;
    return Math.round(totalInhabitants * 10) / 10;
  }

  /**
   * Calcule les statistiques de nourriture
   * @param {Array} jobs - Tableau des métiers
   * @param {Number} seasonFactor - Facteur saisonnier
   * @param {Number} currentReserves - Réserves actuelles
   * @param {Number} generalEffectsTotal - Total des effets généraux
   * @param {Number} techEffectsTotal - Total des effets technologiques
   * @param {Number} perishableFactor - Facteur de péremption
   * @param {Number} moralModifier - Modificateur de moral
   * @returns {Object} - Statistiques de nourriture
   */
  calculateFoodStats(
    jobs,
    seasonFactor,
    currentReserves,
    generalEffectsTotal,
    techEffectsTotal,
    perishableFactor,
    moralModifier
  ) {
    // S'assurer que jobs est un tableau
    const jobsArray = Array.isArray(jobs) ? jobs : [];

    // Obtenir le nombre de fermiers
    let farmers = jobsArray.find(j => j.name === 'Farmer');

    // Si aucun fermier n'est trouvé, essayer de trouver par ID 7 (ID du fermier dans la base de données)
    if (!farmers) {
      farmers = jobsArray.find(j => j.id === 7);
    }

    // Valeurs par défaut si toujours pas trouvé
    if (!farmers) {
      farmers = { number: 0, sick: 0 };
    }

    // S'assurer que nous avons des nombres valides
    const farmersSick = parseInt(farmers.sick || 0, 10);
    const farmersNumber = parseInt(farmers.number || 0, 10);
    const effectiveFarmers = farmersNumber - farmersSick;

    // Obtenir le nombre total d'habitants pour la consommation
    const totalInhabitants = jobsArray.reduce((sum, job) => sum + (job.number || 0), 0) || 0;

    // S'assurer que toutes les valeurs sont des nombres
    const season = typeof seasonFactor === 'number' ? seasonFactor : parseFloat(seasonFactor) || 1.0;
    const reserves = typeof currentReserves === 'number' ? currentReserves : parseFloat(currentReserves) || 0;
    const generalEffects = typeof generalEffectsTotal === 'number' ? generalEffectsTotal : parseFloat(generalEffectsTotal) || 0;
    const techEffects = typeof techEffectsTotal === 'number' ? techEffectsTotal : parseFloat(techEffectsTotal) || 0;
    const perishable = typeof perishableFactor === 'number' ? perishableFactor : parseFloat(perishableFactor) || 0;
    const moral = typeof moralModifier === 'number' ? moralModifier : parseFloat(moralModifier) || 0;

    // Calculer la production
    const production = this.calculateFoodProduction(
      effectiveFarmers,
      season,
      generalEffects,
      techEffects,
      moral
    );

    // Calculer la consommation (1 unité par habitant)
    const consumption = Math.round(totalInhabitants * 10) / 10;

    // Calculer le changement net de nourriture
    const net = Math.round((production - consumption) * 10) / 10;

    // Calculer la perte due à la péremption
    // S'assurer que le facteur de péremption est d'au moins 0,2 (20%) s'il est 0 ou non défini
    const effectivePerishableFactor = perishable === 0 ? 0.2 : perishable;
    const perishableLoss = Math.round((reserves * effectivePerishableFactor) * 10) / 10;

    // Calculer le net après péremption
    const netAfterPerishable = Math.round((net - perishableLoss) * 10) / 10;

    return {
      farmers: effectiveFarmers,
      farmersSick,
      production,
      consumption,
      net,
      currentReserves: reserves,
      perishableLoss,
      netAfterPerishable,
      seasonFactor: season
    };
  }

  /**
   * Calcule les statistiques de moral
   * @param {Number} moralValue - Valeur de moral
   * @returns {Object} - Statistiques de moral
   */
  calculateMoralStats(moralValue) {
    // Normaliser la valeur de moral
    const moral = typeof moralValue === 'number' ? moralValue : parseFloat(moralValue) || 1.0;

    // Calculer le modificateur (moral - 1.0)
    const modifier = moral - 1.0;

    // Déterminer le titre en fonction de la valeur de moral
    let title = 'Neutre';
    if (moral <= 0.5) title = 'Rébellion';
    else if (moral <= 0.7) title = 'En colère';
    else if (moral <= 0.8) title = 'Triste';
    else if (moral <= 0.9) title = 'Mécontent';
    else if (moral <= 1.1) title = 'Neutre';
    else if (moral <= 1.2) title = 'Content';
    else if (moral <= 1.3) title = 'Epanoui';
    else if (moral <= 1.5) title = 'Heureux';
    else title = 'Ecstatique';

    return {
      value: moral,
      modifier,
      title
    };
  }

  /**
   * Calcule les statistiques de santé
   * @param {Array} jobs - Tableau des métiers
   * @param {Number} healthFactor - Facteur de santé
   * @param {Number} moralModifier - Modificateur de moral
   * @returns {Object} - Statistiques de santé
   */
  calculateHealthStats(jobs, healthFactor, moralModifier) {
    // Normaliser les valeurs
    const health = typeof healthFactor === 'number' ? healthFactor : parseFloat(healthFactor) || 0;
    const moral = typeof moralModifier === 'number' ? moralModifier : parseFloat(moralModifier) || 0;

    // S'assurer que jobs est un tableau
    const jobsArray = Array.isArray(jobs) ? jobs : [];

    // Obtenir le nombre total d'habitants
    const totalInhabitants = jobsArray.reduce((sum, job) => sum + (job.number || 0), 0) || 0;

    // Obtenir le nombre total de malades
    const totalSick = jobsArray.reduce((sum, job) => sum + (job.sick || 0), 0) || 0;

    // Calculer le pourcentage de malades
    const sickPercentage = totalInhabitants > 0 ? (totalSick / totalInhabitants) : 0;

    // Calculer la probabilité de tomber malade
    // Formule: 1 - (0.98 + healthFactor + moralModifier)
    const baseProbability = 0.02; // 2% de base
    const adjustedProbability = Math.max(0, baseProbability - health - moral);
    const sicknessProbability = Math.min(1, Math.max(0, adjustedProbability));

    return {
      totalInhabitants,
      totalSick,
      sickPercentage,
      sicknessProbability,
      healthFactor: health,
      moralEffect: moral
    };
  }

  /**
   * Calcule les statistiques de recherche
   * @param {Array} jobs - Tableau des métiers
   * @param {Number} researchFactor - Facteur de recherche
   * @param {Number} moralModifier - Modificateur de moral
   * @param {Number} techLevel - Niveau technologique
   * @returns {Object} - Statistiques de recherche
   */
  calculateResearchStats(jobs, researchFactor, moralModifier, techLevel) {
    // Normaliser les valeurs
    const research = typeof researchFactor === 'number' ? researchFactor : parseFloat(researchFactor) || 0;
    const moral = typeof moralModifier === 'number' ? moralModifier : parseFloat(moralModifier) || 0;
    const tech = typeof techLevel === 'number' ? techLevel : parseInt(techLevel) || 0;

    // S'assurer que jobs est un tableau
    const jobsArray = Array.isArray(jobs) ? jobs : [];

    // Obtenir le nombre de chercheurs
    const researchers = jobsArray.find(j => j.name === 'Researcher') || { number: 0, sick: 0 };
    const activeResearchers = (researchers.number || 0) - (researchers.sick || 0);

    // Calculer les points de recherche par cycle
    // Formule: activeResearchers * (1 + researchFactor + moralModifier)
    const researchPoints = activeResearchers * (1 + research + moral);

    // Calculer le coût du prochain niveau technologique
    // Formule: 100 * (techLevel + 1)^2
    const nextLevelCost = 100 * Math.pow(tech + 1, 2);

    // Calculer le nombre de cycles restants pour le prochain niveau
    const cyclesRemaining = researchPoints > 0 ? Math.ceil(nextLevelCost / researchPoints) : Infinity;

    return {
      activeResearchers,
      researchPoints,
      techLevel: tech,
      nextLevelCost,
      cyclesRemaining,
      researchFactor: research,
      moralEffect: moral
    };
  }

  /**
   * Récupère toutes les statistiques du jeu
   * @returns {Promise<Object>} - Toutes les statistiques
   */
  async getAllStats() {
    try {
      // Récupérer les données du jeu depuis l'API
      const response = await this.get('all');
      return response;
    } catch (error) {
      console.error('Error getting all stats:', error);
      throw error;
    }
  }
}

// Créer une instance du service
const statsService = new StatsService();

// Exporter les méthodes individuelles pour maintenir la compatibilité avec le code existant
export const calculateFoodProduction = statsService.calculateFoodProduction.bind(statsService);
export const calculateFoodConsumption = statsService.calculateFoodConsumption.bind(statsService);
export const calculateFoodStats = statsService.calculateFoodStats.bind(statsService);
export const calculateMoralStats = statsService.calculateMoralStats.bind(statsService);
export const calculateHealthStats = statsService.calculateHealthStats.bind(statsService);
export const calculateResearchStats = statsService.calculateResearchStats.bind(statsService);
export const getAllStats = statsService.getAllStats.bind(statsService);

// Exporter également l'instance du service
export default statsService;
