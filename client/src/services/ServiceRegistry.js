/**
 * Service Registry
 *
 * Centralizes access to all services in the application.
 * Ensures services are properly initialized and configured.
 * Provides a single point of access for all services.
 */

// Import service classes
import GameService from './GameService';
import ModifiersService from './ModifiersService';
import InhabitantsService from './InhabitantsService';
import calculationService from './calculationService';
import CacheService from './CacheService';

class ServiceRegistry {
  constructor() {
    this._services = new Map();
    this._initialized = false;
  }

  /**
   * Initialize all services
   */
  initialize() {
    if (this._initialized) {
      console.warn('ServiceRegistry: Services already initialized');
      return;
    }

    // Register core services
    this.register('cache', CacheService);

    // Register domain services
    this.register('game', GameService);
    this.register('modifiers', ModifiersService);
    this.register('inhabitants', InhabitantsService);
    this.register('calculation', calculationService);

    this._initialized = true;
    console.log('ServiceRegistry: All services initialized');
  }

  /**
   * Register a service
   * @param {string} name - Service name
   * @param {Object} service - Service instance
   */
  register(name, service) {
    if (this._services.has(name)) {
      console.warn(`ServiceRegistry: Service '${name}' already registered`);
      return;
    }

    this._services.set(name, service);
    console.log(`ServiceRegistry: Service '${name}' registered`);
  }

  /**
   * Get a service by name
   * @param {string} name - Service name
   * @returns {Object} - Service instance
   */
  get(name) {
    if (!this._services.has(name)) {
      throw new Error(`ServiceRegistry: Service '${name}' not found`);
    }

    return this._services.get(name);
  }

  /**
   * Check if a service is registered
   * @param {string} name - Service name
   * @returns {boolean} - True if service is registered
   */
  has(name) {
    return this._services.has(name);
  }

  /**
   * Get all registered services
   * @returns {Map} - Map of all registered services
   */
  getAll() {
    return this._services;
  }
}

// Create a singleton instance
const serviceRegistry = new ServiceRegistry();

// Initialize services
serviceRegistry.initialize();

// Export the singleton instance
export default serviceRegistry;
