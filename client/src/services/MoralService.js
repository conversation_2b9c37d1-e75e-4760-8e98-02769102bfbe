/**
 * Service pour gérer les données liées au moral
 */
import apiEndpoint from './ApiEndpoint';

class MoralService {
    /**
     * Récupère toutes les données liées au moral
     * @returns {Promise<Object>} - Données de moral
     */
    static async getMoralData() {
        try {
            const data = await apiEndpoint.get('modifiers/moral');
            return data.moralData;
        } catch (error) {
            console.error('Error fetching moral data:', error);
            throw error;
        }
    }

    /**
     * Récupère les modificateurs de moral
     * @returns {Promise<Array>} - Liste des modificateurs de moral
     */
    static async getMoralModifiers() {
        try {
            const moralData = await this.getMoralData();
            return moralData.modifiers || [];
        } catch (error) {
            console.error('Error fetching moral modifiers:', error);
            return [];
        }
    }

    /**
     * Ajoute un nouveau modificateur de moral
     * @param {Object} modifier - Modificateur à ajouter
     * @returns {Promise<Object>} - Résultat de l'opération
     */
    static async addMoralModifier(modifier) {
        try {
            const response = await fetch('http://localhost:3001/api/modifiers/table/10', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(modifier),
            });

            const data = await response.json();

            if (!data.success) {
                throw new Error(data.error || 'Failed to add moral modifier');
            }

            return data;
        } catch (error) {
            console.error('Error adding moral modifier:', error);
            throw error;
        }
    }

    /**
     * Met à jour un modificateur de moral existant
     * @param {number} id - ID du modificateur à mettre à jour
     * @param {Object} modifier - Nouvelles valeurs du modificateur
     * @returns {Promise<Object>} - Résultat de l'opération
     */
    static async updateMoralModifier(id, modifier) {
        try {
            const response = await fetch(`http://localhost:3001/api/modifiers/${id}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(modifier),
            });

            const data = await response.json();

            if (!data.success) {
                throw new Error(data.error || 'Failed to update moral modifier');
            }

            return data;
        } catch (error) {
            console.error('Error updating moral modifier:', error);
            throw error;
        }
    }

    /**
     * Supprime un modificateur de moral
     * @param {number} id - ID du modificateur à supprimer
     * @returns {Promise<Object>} - Résultat de l'opération
     */
    static async deleteMoralModifier(id) {
        try {
            const response = await fetch(`http://localhost:3001/api/modifiers/${id}`, {
                method: 'DELETE',
            });

            const data = await response.json();

            if (!data.success) {
                throw new Error(data.error || 'Failed to delete moral modifier');
            }

            return data;
        } catch (error) {
            console.error('Error deleting moral modifier:', error);
            throw error;
        }
    }
}

export default MoralService;
