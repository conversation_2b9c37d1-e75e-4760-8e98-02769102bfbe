/**
 * Service pour centraliser tous les calculs de l'application
 * Fournit des méthodes pour calculer les différentes valeurs du jeu
 */
import { BaseService } from './BaseService';

// Constantes
export const MINING_VALUE = 30; // Valeur de base de la production minière par mineur
export const FOOD_PER_FARMER = 4; // Nourriture produite par fermier
export const MATERIALS_PER_WORKER = 4; // Matériaux produits par ouvrier
export const MATERIALS_PER_MINER = 2; // Matériaux produits par mineur
export const TRADING_VALUE = 25; // Valeur de base du commerce par marchand

class CalculationService extends BaseService {
  constructor() {
    super('stats');
  }

  /**
   * Calcule la production minière
   * @param {number} activeMiner - Nombre de mineurs actifs
   * @param {number} generalEffectsTotal - Somme des effets généraux
   * @param {number} techEffectsTotal - Somme des effets technologiques
   * @param {number} moralModifier - Modificateur de moral
   * @param {number} engineeringMultiplier - Multiplicateur d'ingénierie
   * @returns {number} - Production minière
   */
  calculateMiningProduction(
    activeMiner,
    generalEffectsTotal,
    techEffectsTotal,
    moralModifier,
    engineeringMultiplier
  ) {
    return (
      activeMiner *
      MINING_VALUE *
      (1 + generalEffectsTotal + techEffectsTotal + moralModifier) *
      (1 + engineeringMultiplier)
    );
  }

  /**
   * Calcule le revenu par mineur
   * @param {number} activeMiner - Nombre de mineurs actifs
   * @param {number} miningProduction - Production minière totale
   * @param {number} generalEffectsTotal - Somme des effets généraux
   * @param {number} techEffectsTotal - Somme des effets technologiques
   * @param {number} moralModifier - Modificateur de moral
   * @param {number} engineeringMultiplier - Multiplicateur d'ingénierie
   * @returns {number} - Revenu par mineur
   */
  calculateRevenuePerMiner(
    activeMiner,
    miningProduction,
    generalEffectsTotal,
    techEffectsTotal,
    moralModifier,
    engineeringMultiplier
  ) {
    if (activeMiner > 0) {
      return miningProduction / activeMiner;
    } else {
      return (
        MINING_VALUE *
        (1 + generalEffectsTotal + techEffectsTotal + moralModifier) *
        (1 + engineeringMultiplier)
      );
    }
  }

  /**
   * Calcule le multiplicateur d'ingénierie
   * @param {number} activeEngineers - Nombre d'ingénieurs actifs
   * @param {number} engineeringEffectsTotal - Somme des effets d'ingénierie
   * @returns {number} - Multiplicateur d'ingénierie
   */
  calculateEngineeringMultiplier(
    activeEngineers,
    engineeringEffectsTotal
  ) {
    return activeEngineers * 0.03 * (1 + engineeringEffectsTotal);
  }

  /**
   * Calcule le bonus total
   * @param {number} generalEffectsTotal - Somme des effets généraux
   * @param {number} techEffectsTotal - Somme des effets technologiques
   * @param {number} moralModifier - Modificateur de moral
   * @param {number} engineeringMultiplier - Multiplicateur d'ingénierie
   * @returns {number} - Bonus total
   */
  calculateTotalBonus(
    generalEffectsTotal,
    techEffectsTotal,
    moralModifier,
    engineeringMultiplier
  ) {
    return (
      (1 + generalEffectsTotal + techEffectsTotal + moralModifier) *
      (1 + engineeringMultiplier) -
      1
    );
  }

  /**
   * Calcule la distribution normale
   * @param {number} mean - Moyenne
   * @param {number} stdDev - Écart-type
   * @param {number} value - Valeur
   * @returns {number} - Probabilité
   */
  calculateNormalDistribution(mean, stdDev, value) {
    const exponent = -Math.pow(value - mean, 2) / (2 * Math.pow(stdDev, 2));
    return (1 / (stdDev * Math.sqrt(2 * Math.PI))) * Math.exp(exponent);
  }

  /**
   * Calcule le modificateur de moral à partir de la valeur de moral
   * @param {number} moralValue - Valeur de moral
   * @returns {number} - Modificateur de moral
   */
  calculateMoralModifier(moralValue) {
    // Utiliser parseFloat et toFixed pour éviter les problèmes de précision
    return parseFloat((moralValue - 1.0).toFixed(10));
  }

  /**
   * Calcule la production alimentaire
   * @param {number} activeFarmers - Nombre de fermiers actifs
   * @param {number} foodPerFarmer - Nourriture produite par fermier (par défaut: FOOD_PER_FARMER)
   * @param {number} seasonFactor - Facteur de saison
   * @param {number} foodGeneralModifier - Modificateur général de nourriture
   * @param {number} foodTechModifier - Modificateur technologique de nourriture
   * @param {number} moralModifier - Modificateur de moral
   * @returns {number} - Production alimentaire
   */
  calculateFoodProduction(
    activeFarmers,
    foodPerFarmer = FOOD_PER_FARMER,
    seasonFactor,
    foodGeneralModifier,
    foodTechModifier,
    moralModifier
  ) {
    const productionBeforeRounding = activeFarmers *
      foodPerFarmer *
      seasonFactor *
      (1 + foodGeneralModifier + foodTechModifier + moralModifier);

    // Round to 1 decimal place for consistency with UI display
    return Math.round(productionBeforeRounding * 10) / 10;
  }

  /**
   * Calcule la consommation alimentaire
   * @param {Array} jobs - Liste des métiers
   * @returns {number} - Consommation alimentaire
   */
  calculateFoodConsumption(jobs) {
    const totalInhabitants = jobs.reduce((sum, job) => sum + job.number, 0);
    return Math.round(totalInhabitants * 10) / 10;
  }

  /**
   * Calcule la production de matériaux
   * @param {number} activeWorkers - Nombre d'ouvriers actifs
   * @param {number} activeMiners - Nombre de mineurs actifs
   * @param {number} materialsPerWorker - Matériaux produits par ouvrier (par défaut: MATERIALS_PER_WORKER)
   * @param {number} materialsPerMiner - Matériaux produits par mineur (par défaut: MATERIALS_PER_MINER)
   * @param {number} materialsGeneralModifier - Modificateur général de matériaux
   * @param {number} materialsTechModifier - Modificateur technologique de matériaux
   * @param {number} moralModifier - Modificateur de moral
   * @returns {number} - Production de matériaux
   */
  calculateMaterialsProduction(
    activeWorkers,
    activeMiners,
    materialsPerWorker = MATERIALS_PER_WORKER,
    materialsPerMiner = MATERIALS_PER_MINER,
    materialsGeneralModifier,
    materialsTechModifier,
    moralModifier
  ) {
    return (
      (activeWorkers * materialsPerWorker + activeMiners * materialsPerMiner) *
      (1 + materialsGeneralModifier + materialsTechModifier + moralModifier)
    );
  }

  /**
   * Calcule les charges salariales
   * @param {Array} jobs - Liste des métiers
   * @returns {number} - Charges salariales
   */
  calculateSalaries(jobs) {
    return jobs.reduce(
      (sum, job) => sum + (job.number - (job.free || 0)) * job.salary,
      0
    );
  }

  /**
   * Calcule les charges non salariales
   * @param {number} totalInhabitants - Nombre total d'habitants
   * @param {number} chargesPerInhabitant - Charges par habitant
   * @param {number} chargesModifier - Modificateur de charges
   * @returns {number} - Charges non salariales
   */
  calculateNonSalaryCharges(
    totalInhabitants,
    chargesPerInhabitant,
    chargesModifier
  ) {
    return totalInhabitants * chargesPerInhabitant * (1 + chargesModifier);
  }

  /**
   * Calcule les charges totales
   * @param {number} salaries - Charges salariales
   * @param {number} nonSalaryCharges - Charges non salariales
   * @returns {number} - Charges totales
   */
  calculateTotalCharges(salaries, nonSalaryCharges) {
    return salaries + nonSalaryCharges;
  }

  /**
   * Calcule la probabilité de maladie
   * @param {number} totalInhabitants - Nombre total d'habitants
   * @param {number} activeHealers - Nombre de guérisseurs actifs
   * @param {number} healthModifier - Modificateur de santé
   * @returns {number} - Probabilité de maladie
   */
  calculateSickProbability(
    totalInhabitants,
    activeHealers,
    healthModifier
  ) {
    return Math.max(
      0,
      0.25 /
        (1 +
          Math.exp(
            -0.1 * (totalInhabitants - 18 * activeHealers * (1 + healthModifier))
          ))
    );
  }

  /**
   * Calcule la probabilité de recherche
   * @param {number} activeScholars - Nombre d'érudits actifs
   * @param {number} researchModifier - Modificateur de recherche
   * @param {number} techLevel - Niveau technologique
   * @returns {number} - Probabilité de recherche
   */
  calculateResearchProbability(
    activeScholars,
    researchModifier,
    techLevel = 0
  ) {
    // Terme 1: activeScholars * 0.05 * (1 + researchModifier)
    const term1 = activeScholars * 0.05 * (1 + researchModifier);

    // Terme 2: 0.3 - (0.03 * techLevel)
    const term2 = Math.max(0.05, 0.3 - (0.03 * techLevel));

    // Probabilité finale: min(0.95, term1 + term2)
    return Math.min(0.95, term1 + term2);
  }

  /**
   * Calcule les revenus commerciaux
   * @param {number} activeMerchants - Nombre de marchands actifs
   * @param {number} revenuePerMerchant - Revenus par marchand (par défaut: TRADING_VALUE)
   * @param {number} tradingModifier - Modificateur commercial
   * @param {number} moralModifier - Modificateur de moral
   * @returns {number} - Revenus commerciaux
   */
  calculateTradingRevenue(
    activeMerchants,
    revenuePerMerchant = TRADING_VALUE,
    tradingModifier,
    moralModifier
  ) {
    return (
      activeMerchants *
      revenuePerMerchant *
      (1 + tradingModifier + moralModifier)
    );
  }

  /**
   * Calcule le niveau de criminalité
   * @param {number} totalInhabitants - Nombre total d'habitants
   * @param {number} activeProtectors - Nombre de protecteurs actifs
   * @param {number} activeSoldiers - Nombre de soldats actifs
   * @param {number} justiceModifier - Modificateur de justice
   * @returns {number} - Niveau de criminalité
   */
  calculateCrimeLevel(
    totalInhabitants,
    activeProtectors,
    activeSoldiers,
    justiceModifier
  ) {
    // Nouvelle formule qui prend en compte les soldats
    const term = -0.1 * (15 * activeProtectors * (1 + justiceModifier) - totalInhabitants + 5 * activeSoldiers);
    return Math.max(0, 0.3 / (1 + Math.exp(term)));
  }

  /**
   * Calcule le niveau de défense
   * @param {number} activeSoldiers - Nombre de soldats actifs
   * @param {number} defenseModifier - Modificateur de défense
   * @returns {number} - Niveau de défense
   */
  calculateDefenseLevel(activeSoldiers, defenseModifier) {
    return activeSoldiers * 5 * (1 + defenseModifier);
  }

  /**
   * Calcule le niveau de renommée
   * @param {number} totalInhabitants - Nombre total d'habitants
   * @param {number} techLevel - Niveau technologique
   * @param {number} defenseLevel - Niveau de défense
   * @param {number} crimeLevel - Niveau de criminalité
   * @returns {number} - Niveau de renommée
   */
  calculateRenownLevel(
    totalInhabitants,
    techLevel,
    defenseLevel,
    crimeLevel
  ) {
    return (
      totalInhabitants * 0.1 +
      techLevel * 10 +
      defenseLevel * 0.5 -
      crimeLevel * 100
    );
  }

  /**
   * Calcule la force militaire
   * @param {number} totalInhabitants - Nombre total d'habitants
   * @param {number} activeSoldiers - Nombre de soldats actifs
   * @param {number} armyModifier - Modificateur militaire
   * @returns {number} - Force militaire
   */
  calculateArmyStrength(
    totalInhabitants,
    activeSoldiers,
    armyModifier
  ) {
    // Chaque soldat vaut 0.5 point de force
    const soldierStrength = activeSoldiers * 0.5 * (1 + armyModifier);

    // Chaque autre habitant vaut 0.125 point de force
    const otherInhabitants = totalInhabitants - activeSoldiers;
    const otherStrength = otherInhabitants * 0.125;

    return soldierStrength + otherStrength;
  }

  /**
   * Calcule les statistiques de moral
   * @param {number} moralValue - Valeur de moral
   * @param {number} moralModifier - Modificateur de moral
   * @returns {Object} - Statistiques de moral
   */
  calculateMoralStats(moralValue, moralModifier) {
    return {
      value: moralValue,
      modifier: moralModifier,
      productionEffect: `${(moralModifier >= 0 ? '+' : '')}${Math.round(moralModifier * 100)}%`
    };
  }

  /**
   * Calcule les statistiques de santé
   * @param {Array} jobs - Liste des métiers
   * @param {number} sickProbability - Probabilité de maladie
   * @param {number} healthModifier - Modificateur de santé
   * @returns {Object} - Statistiques de santé
   */
  calculateHealthStats(jobs, sickProbability, healthModifier) {
    const totalPopulation = jobs.reduce((sum, job) => sum + job.number, 0);
    const totalSick = jobs.reduce((sum, job) => sum + job.sick, 0);
    const sickPercentage = totalPopulation > 0 ? (totalSick / totalPopulation) * 100 : 0;

    const healers = jobs.find(job => job.name === 'Healer') || { number: 0, sick: 0 };
    const activeHealers = healers.number - healers.sick;

    return {
      totalPopulation,
      totalSick,
      sickPercentage,
      sickProbability,
      activeHealers,
      healthModifier
    };
  }

  /**
   * Calcule les statistiques de recherche
   * @param {Array} jobs - Liste des métiers
   * @param {number} techLevel - Niveau technologique
   * @param {number} researchFactor - Facteur de recherche
   * @param {number} moralModifier - Modificateur de moral
   * @returns {Object} - Statistiques de recherche
   */
  calculateResearchStats(jobs, techLevel, researchFactor, moralModifier) {
    // Obtenir le nombre de chercheurs
    const researchers = jobs.find(j => j.name === 'Researcher') || { number: 0, sick: 0 };
    const activeResearchers = researchers.number - researchers.sick;

    // Calculer les points de recherche par cycle
    // Formule: activeResearchers * (1 + researchFactor + moralModifier)
    const researchPoints = activeResearchers * (1 + researchFactor + moralModifier);

    // Calculer le coût du prochain niveau technologique
    // Formule: 100 * (techLevel + 1)^2
    const nextLevelCost = 100 * Math.pow(techLevel + 1, 2);

    // Calculer le nombre de cycles restants pour le prochain niveau
    const cyclesRemaining = researchPoints > 0 ? Math.ceil(nextLevelCost / researchPoints) : Infinity;

    return {
      activeResearchers,
      researchPoints,
      techLevel,
      nextLevelCost,
      cyclesRemaining,
      researchFactor,
      moralEffect: moralModifier
    };
  }

  /**
   * Calcule les statistiques de nourriture
   * @param {Array} jobs - Liste des métiers
   * @param {number} seasonFactor - Facteur saisonnier
   * @param {number} currentReserves - Réserves actuelles
   * @param {number} generalEffectsTotal - Somme des effets généraux
   * @param {number} techEffectsTotal - Somme des effets technologiques
   * @param {number} perishableFactor - Facteur de péremption
   * @param {number} moralModifier - Modificateur de moral
   * @param {Object} serverFoodData - Données de nourriture calculées par le serveur
   * @returns {Object} - Statistiques de nourriture
   */
  calculateFoodStats(
    jobs,
    seasonFactor,
    currentReserves,
    generalEffectsTotal,
    techEffectsTotal,
    perishableFactor,
    moralModifier,
    serverFoodData = null
  ) {
    // Si nous avons des données calculées par le serveur, les utiliser directement
    if (serverFoodData) {
      console.log('calculateFoodStats: Using server-calculated values:', serverFoodData);

      // Obtenir le nombre de fermiers pour l'affichage
      let farmers = jobs.find(j => j.name === 'Farmer');
      if (!farmers) {
        farmers = jobs.find(j => j.id === 7);
      }
      if (!farmers) {
        farmers = { number: 0, sick: 0 };
      }

      const farmersSick = parseInt(farmers.sick || 0, 10);
      const farmersNumber = parseInt(farmers.number || 0, 10);
      const effectiveFarmers = farmersNumber - farmersSick;

      // Vérifier si nous avons des informations sur la prochaine saison
      const nextSeasonFactor = serverFoodData.nextSeasonFactor;
      console.log('calculateFoodStats: Next season factor from server:', nextSeasonFactor);

      // Si nous avons le facteur de la prochaine saison, l'utiliser pour la prédiction
      if (nextSeasonFactor !== undefined) {
        // Calculer la production avec le facteur de la prochaine saison
        const nextProduction = this.calculateFoodProduction(
          effectiveFarmers,
          FOOD_PER_FARMER,
          nextSeasonFactor,
          generalEffectsTotal,
          techEffectsTotal,
          moralModifier
        );

        // Calculer le net avec le facteur de la prochaine saison
        const nextNet = Math.round((nextProduction - serverFoodData.consumption) * 10) / 10;

        // Calculer la perte due à la péremption (cela ne change pas avec la saison)
        const perishableLoss = serverFoodData.perishableLoss;

        // Calculer le net après péremption avec le facteur de la prochaine saison
        const nextNetAfterPerishable = Math.round((nextNet - perishableLoss) * 10) / 10;

        console.log('calculateFoodStats: Prediction with next season factor:', {
          currentProduction: serverFoodData.production,
          nextProduction,
          currentNet: serverFoodData.net,
          nextNet,
          currentNetAfterPerishable: serverFoodData.netAfterPerishable,
          nextNetAfterPerishable
        });

        // Utiliser les valeurs avec le facteur de la prochaine saison pour la prédiction
        return {
          farmers: effectiveFarmers,
          farmersSick,
          production: nextProduction,
          consumption: serverFoodData.consumption,
          net: nextNet,
          currentReserves: currentReserves,
          perishableLoss: perishableLoss,
          netAfterPerishable: nextNetAfterPerishable,
          seasonFactor: nextSeasonFactor, // Utiliser le facteur de la prochaine saison
          isNextSeasonPrediction: true
        };
      }

      // Utiliser les valeurs du serveur telles quelles si pas de facteur de prochaine saison
      return {
        farmers: effectiveFarmers,
        farmersSick,
        production: serverFoodData.production,
        consumption: serverFoodData.consumption,
        net: serverFoodData.net,
        currentReserves: currentReserves,
        perishableLoss: serverFoodData.perishableLoss,
        netAfterPerishable: serverFoodData.netAfterPerishable,
        seasonFactor
      };
    }

    // Sinon, calculer les valeurs localement
    let farmers = jobs.find(j => j.name === 'Farmer');
    if (!farmers) {
      farmers = jobs.find(j => j.id === 7);
    }
    if (!farmers) {
      farmers = { number: 0, sick: 0 };
    }

    const farmersSick = parseInt(farmers.sick || 0, 10);
    const farmersNumber = parseInt(farmers.number || 0, 10);
    const effectiveFarmers = farmersNumber - farmersSick;

    const production = this.calculateFoodProduction(
      effectiveFarmers,
      FOOD_PER_FARMER,
      seasonFactor,
      generalEffectsTotal,
      techEffectsTotal,
      moralModifier
    );

    const consumption = this.calculateFoodConsumption(jobs);
    const net = Math.round((production - consumption) * 10) / 10;
    const perishableLoss = Math.round((currentReserves * perishableFactor) * 10) / 10;
    const netAfterPerishable = Math.round((net - perishableLoss) * 10) / 10;

    return {
      farmers: effectiveFarmers,
      farmersSick,
      production,
      consumption,
      net,
      currentReserves,
      perishableLoss,
      netAfterPerishable,
      seasonFactor
    };
  }

  /**
   * Récupère toutes les statistiques du jeu
   * @returns {Promise<Object>} - Toutes les statistiques
   */
  async getAllStats() {
    try {
      // Récupérer les données du jeu depuis l'API
      const response = await this.get('all');
      return response;
    } catch (error) {
      console.error('Error getting all stats:', error);
      throw error;
    }
  }
}

// Créer une instance du service
const calculationService = new CalculationService();

// Exporter l'instance par défaut
export default calculationService;

// Exporter les méthodes individuelles pour maintenir la compatibilité avec le code existant
export const calculateMiningProduction = calculationService.calculateMiningProduction.bind(calculationService);
export const calculateRevenuePerMiner = calculationService.calculateRevenuePerMiner.bind(calculationService);
export const calculateEngineeringMultiplier = calculationService.calculateEngineeringMultiplier.bind(calculationService);
export const calculateTotalBonus = calculationService.calculateTotalBonus.bind(calculationService);
export const calculateNormalDistribution = calculationService.calculateNormalDistribution.bind(calculationService);
export const calculateMoralModifier = calculationService.calculateMoralModifier.bind(calculationService);
export const calculateFoodProduction = calculationService.calculateFoodProduction.bind(calculationService);
export const calculateFoodConsumption = calculationService.calculateFoodConsumption.bind(calculationService);
export const calculateFoodStats = calculationService.calculateFoodStats.bind(calculationService);
export const calculateMaterialsProduction = calculationService.calculateMaterialsProduction.bind(calculationService);
export const calculateSalaries = calculationService.calculateSalaries.bind(calculationService);
export const calculateNonSalaryCharges = calculationService.calculateNonSalaryCharges.bind(calculationService);
export const calculateTotalCharges = calculationService.calculateTotalCharges.bind(calculationService);
export const calculateSickProbability = calculationService.calculateSickProbability.bind(calculationService);
export const calculateResearchProbability = calculationService.calculateResearchProbability.bind(calculationService);
export const calculateTradingRevenue = calculationService.calculateTradingRevenue.bind(calculationService);
export const calculateCrimeLevel = calculationService.calculateCrimeLevel.bind(calculationService);
export const calculateDefenseLevel = calculationService.calculateDefenseLevel.bind(calculationService);
export const calculateRenownLevel = calculationService.calculateRenownLevel.bind(calculationService);
export const calculateArmyStrength = calculationService.calculateArmyStrength.bind(calculationService);
export const calculateMoralStats = calculationService.calculateMoralStats.bind(calculationService);
export const calculateHealthStats = calculationService.calculateHealthStats.bind(calculationService);
export const calculateResearchStats = calculationService.calculateResearchStats.bind(calculationService);
export const getAllStats = calculationService.getAllStats.bind(calculationService);
