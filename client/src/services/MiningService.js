/**
 * Service for handling mining-related API calls
 */
import apiEndpoint from './ApiEndpoint';

class MiningService {
  /**
   * Get all mining-related data
   * @returns {Promise<Object>} - Mining data
   */
  static async getMiningData() {
    try {
      const data = await apiEndpoint.get('modifiers/mining');
      return data.miningData;
    } catch (error) {
      console.error('Error fetching mining data:', error);
      throw error;
    }
  }

  /**
   * Add a new mining modifier
   * @param {number} tableId - The ID of the modifier table
   * @param {Object} modifier - The modifier to add
   * @returns {Promise<Object>} - The response data
   */
  static async addMiningModifier(tableId, modifier) {
    try {
      const response = await fetch(`http://localhost:3001/api/modifiers/table/${tableId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(modifier),
      });

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || 'Failed to add mining modifier');
      }

      return data;
    } catch (error) {
      console.error('Error adding mining modifier:', error);
      throw error;
    }
  }

  /**
   * Update an existing mining modifier
   * @param {number} id - ID of the modifier to update
   * @param {Object} modifier - The updated modifier data
   * @returns {Promise<Object>} - The response data
   */
  static async updateMiningModifier(id, modifier) {
    try {
      const response = await fetch(`http://localhost:3001/api/modifiers/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(modifier),
      });

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || 'Failed to update mining modifier');
      }

      return data;
    } catch (error) {
      console.error('Error updating mining modifier:', error);
      throw error;
    }
  }

  /**
   * Delete a mining modifier
   * @param {number} id - ID of the modifier to delete
   * @returns {Promise<Object>} - The response data
   */
  static async deleteMiningModifier(id) {
    try {
      const response = await fetch(`http://localhost:3001/api/modifiers/${id}`, {
        method: 'DELETE',
      });

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || 'Failed to delete mining modifier');
      }

      return data;
    } catch (error) {
      console.error('Error deleting mining modifier:', error);
      throw error;
    }
  }
}

export default MiningService;
