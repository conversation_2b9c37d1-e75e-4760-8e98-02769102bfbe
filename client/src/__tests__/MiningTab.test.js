import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import App from '../App';

// Mock fetch pour simuler les appels API
global.fetch = jest.fn();

// Helper pour configurer les mocks de fetch
const mockFetchResponse = (data) => {
  global.fetch.mockResolvedValueOnce({
    ok: true,
    json: async () => data
  });
};

describe('Mining Tab', () => {
  beforeEach(() => {
    // Réinitialiser les mocks
    global.fetch.mockClear();
    
    // Mock des données de l'API pour fetchGameState
    const mockGameState = {
      gameState: {
        cycle_number: 1,
        month: 'Tanzanite',
        year: 1349,
        season: 'Hiver',
        season_factor: 0.5,
        treasure: 100,
        materials: 50,
        food_reserves: 100
      },
      jobs: [
        { id: 1, name: 'Worker', number: 10, free: 0, salary: 1 },
        { id: 2, name: 'Miner', number: 5, free: 1, salary: 2 },
        { id: 3, name: 'Engineer', number: 3, free: 0, salary: 3 }
      ],
      events: []
    };
    
    // Mock pour fetchGameState
    mockFetchResponse(mockGameState);
    
    // Mock pour les modifiers
    mockFetchResponse({
      modifiers: [
        { id: 1, table_id: 4, value: 0.1, title: 'Tech Level', description: 'Bonus from technology' },
        { id: 2, table_id: 4, value: 0.2, title: 'Equipment', description: 'Better mining equipment' }
      ]
    });
    
    // Mock pour les autres appels API
    mockFetchResponse({ success: true });
  });

  test('navigates to mining tab and displays mining information', async () => {
    render(<App />);
    
    // Attendre que le dashboard soit chargé
    await waitFor(() => {
      expect(screen.getByText('Gestion de la Mine')).toBeInTheDocument();
    });
    
    // Cliquer sur l'onglet Minage
    fireEvent.click(screen.getByText('Minage'));
    
    // Vérifier que l'onglet Minage est affiché
    expect(screen.getByText('Minage')).toBeInTheDocument();
    
    // Vérifier que les informations de minage sont affichées
    await waitFor(() => {
      // Vérifier la présence des mineurs
      expect(screen.getByText(/Mineurs actifs/i)).toBeInTheDocument();
      
      // Vérifier la présence des ingénieurs
      expect(screen.getByText(/Ingénieurs/i)).toBeInTheDocument();
      
      // Vérifier la présence de la production
      expect(screen.getByText(/Production/i)).toBeInTheDocument();
    });
  });

  test('displays mining modifiers correctly', async () => {
    render(<App />);
    
    // Attendre que le dashboard soit chargé
    await waitFor(() => {
      expect(screen.getByText('Gestion de la Mine')).toBeInTheDocument();
    });
    
    // Cliquer sur l'onglet Minage
    fireEvent.click(screen.getByText('Minage'));
    
    // Vérifier que les modificateurs de minage sont affichés
    await waitFor(() => {
      expect(screen.getByText('Tech Level')).toBeInTheDocument();
      expect(screen.getByText('Equipment')).toBeInTheDocument();
      
      // Vérifier les valeurs des modificateurs
      expect(screen.getByText('10.0%')).toBeInTheDocument();
      expect(screen.getByText('20.0%')).toBeInTheDocument();
    });
  });

  test('mining production calculation is displayed correctly', async () => {
    render(<App />);
    
    // Attendre que le dashboard soit chargé
    await waitFor(() => {
      expect(screen.getByText('Gestion de la Mine')).toBeInTheDocument();
    });
    
    // Cliquer sur l'onglet Minage
    fireEvent.click(screen.getByText('Minage'));
    
    // Vérifier que le calcul de production est affiché
    await waitFor(() => {
      // Vérifier les composants du calcul
      expect(screen.getByText(/Mineurs actifs/i)).toBeInTheDocument();
      expect(screen.getByText(/Production de base/i)).toBeInTheDocument();
      expect(screen.getByText(/Bonus des ingénieurs/i)).toBeInTheDocument();
      expect(screen.getByText(/Bonus du moral/i)).toBeInTheDocument();
      
      // Vérifier la production totale
      expect(screen.getByText(/Production totale/i)).toBeInTheDocument();
    });
  });
});
