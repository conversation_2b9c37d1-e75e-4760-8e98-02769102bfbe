import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import App from '../App';

// Mock fetch
beforeEach(() => {
  global.fetch = jest.fn(() =>
    Promise.resolve({
      ok: true,
      json: () => Promise.resolve({
        gameState: {
          cycle_number: 1,
          month: 'Tanzanite',
          year: 1349,
          season: 'Hiver',
          season_factor: 0.5,
          treasure: 100,
          materials: 50,
          food_reserves: 100,
          moral_value: 1000
        },
        jobs: [
          { id: 1, name: 'Worker', number: 10, free: 0, salary: 1 },
          { id: 2, name: 'Miner', number: 5, free: 1, salary: 2 },
          { id: 3, name: 'Farmer', number: 5, free: 1, salary: 2 }
        ],
        events: []
      })
    })
  );
});

describe('App Component', () => {
  test('renders loading state initially', () => {
    render(<App />);
    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  test('renders dashboard after loading', async () => {
    render(<App />);

    // Attendre que le dashboard soit chargé
    await waitFor(() => {
      expect(screen.getByText('Gestion de la Mine')).toBeInTheDocument();
    });

    // Vérifier que les informations du jeu sont affichées
    expect(screen.getByText(/Cycle:/i)).toBeInTheDocument();
    // Utiliser getAllByText pour les éléments qui apparaissent plusieurs fois
    expect(screen.getAllByText(/Mois:/i).length).toBeGreaterThan(0);
    expect(screen.getAllByText(/Année:/i).length).toBeGreaterThan(0);
  });
});
