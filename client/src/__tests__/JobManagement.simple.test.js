import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import App from '../App';

// Mock fetch
beforeEach(() => {
  global.fetch = jest.fn(() =>
    Promise.resolve({
      ok: true,
      json: () => Promise.resolve({
        gameState: {
          cycle_number: 1,
          month: 'Tanzanite',
          year: 1349,
          season: 'Hiver',
          season_factor: 0.5,
          treasure: 100,
          materials: 50,
          food_reserves: 100,
          moral_value: 1000
        },
        jobs: [
          { id: 1, name: 'Worker', number: 10, free: 0, salary: 1 },
          { id: 2, name: 'Miner', number: 5, free: 1, salary: 2 },
          { id: 3, name: 'Farmer', number: 5, free: 1, salary: 2 }
        ],
        events: []
      })
    })
  );
});

describe('Job Management', () => {
  test('navigates to jobs tab', async () => {
    render(<App />);

    // Attendre que le dashboard soit chargé
    await waitFor(() => {
      expect(screen.getByText('Gestion de la Mine')).toBeInTheDocument();
    });

    // Cliquer sur l'onglet Emplois (utiliser getAllByText et prendre le premier élément)
    const emploisTab = screen.getAllByText('Emplois')[0];
    fireEvent.click(emploisTab);

    // Vérifier que l'onglet Emplois est affiché
    await waitFor(() => {
      expect(screen.getByText('Liste des emplois')).toBeInTheDocument();
    });
  });

  test('shift key changes button text', async () => {
    render(<App />);

    // Attendre que le dashboard soit chargé
    await waitFor(() => {
      expect(screen.getByText('Gestion de la Mine')).toBeInTheDocument();
    });

    // Cliquer sur l'onglet Emplois (utiliser getAllByText et prendre le premier élément)
    const emploisTab = screen.getAllByText('Emplois')[0];
    fireEvent.click(emploisTab);

    // Attendre que l'onglet Emplois soit chargé
    await waitFor(() => {
      expect(screen.getByText('Liste des emplois')).toBeInTheDocument();
    });

    // Vérifier que les boutons affichent + et - par défaut
    const plusButtons = screen.getAllByText('+');
    const minusButtons = screen.getAllByText('-');
    expect(plusButtons.length).toBeGreaterThan(0);
    expect(minusButtons.length).toBeGreaterThan(0);

    // Simuler l'appui sur la touche Shift
    fireEvent.keyDown(document, { key: 'Shift' });

    // Vérifier que les boutons affichent +5 et -5 lorsque Shift est enfoncé
    const plus5Buttons = screen.getAllByText('+5');
    const minus5Buttons = screen.getAllByText('-5');
    expect(plus5Buttons.length).toBeGreaterThan(0);
    expect(minus5Buttons.length).toBeGreaterThan(0);

    // Simuler le relâchement de la touche Shift
    fireEvent.keyUp(document, { key: 'Shift' });

    // Vérifier que les boutons affichent à nouveau + et -
    const plusButtonsAfter = screen.getAllByText('+');
    const minusButtonsAfter = screen.getAllByText('-');
    expect(plusButtonsAfter.length).toBeGreaterThan(0);
    expect(minusButtonsAfter.length).toBeGreaterThan(0);
  });
});
