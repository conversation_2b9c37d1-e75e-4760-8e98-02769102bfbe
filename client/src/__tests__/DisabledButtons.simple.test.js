import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';

// Composant simple pour tester les boutons désactivés
const DisabledButtonsComponent = () => {
  const [count, setCount] = React.useState(0);
  const maxCount = 5;
  const minCount = 0;

  const increment = () => {
    if (count < maxCount) {
      setCount(count + 1);
    }
  };

  const decrement = () => {
    if (count > minCount) {
      setCount(count - 1);
    }
  };

  return (
    <div>
      <h1>Disabled Buttons Test</h1>
      <p>Count: {count}</p>
      <button onClick={increment} disabled={count >= maxCount}>+</button>
      <button onClick={decrement} disabled={count <= minCount}>-</button>
    </div>
  );
};

describe('Disabled Buttons Component', () => {
  test('decrement button is disabled when count is 0', () => {
    render(<DisabledButtonsComponent />);
    
    const decrementButton = screen.getByText('-');
    
    expect(decrementButton).toBeDisabled();
  });

  test('increment button is disabled when count is at max', () => {
    // Utiliser un composant avec un état initial à la valeur maximale
    const MaxCountComponent = () => {
      const [count, setCount] = React.useState(5);
      const maxCount = 5;
      const minCount = 0;
    
      const increment = () => {
        if (count < maxCount) {
          setCount(count + 1);
        }
      };
    
      const decrement = () => {
        if (count > minCount) {
          setCount(count - 1);
        }
      };
    
      return (
        <div>
          <h1>Disabled Buttons Test</h1>
          <p>Count: {count}</p>
          <button onClick={increment} disabled={count >= maxCount}>+</button>
          <button onClick={decrement} disabled={count <= minCount}>-</button>
        </div>
      );
    };
    
    render(<MaxCountComponent />);
    
    const incrementButton = screen.getByText('+');
    
    expect(incrementButton).toBeDisabled();
  });
});
