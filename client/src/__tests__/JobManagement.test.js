import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import App from '../App';

// Mock fetch pour simuler les appels API
global.fetch = jest.fn();

// Helper pour configurer les mocks de fetch
const mockFetchResponse = (data) => {
  global.fetch.mockResolvedValueOnce({
    ok: true,
    json: async () => data
  });
};

describe('Job Management', () => {
  beforeEach(() => {
    // Réinitialiser les mocks
    global.fetch.mockClear();
    
    // Mock des données de l'API pour fetchGameState
    const mockGameState = {
      gameState: {
        cycle_number: 1,
        month: 'Tanzanite',
        year: 1349,
        season: 'Hiver',
        season_factor: 0.5,
        treasure: 100,
        materials: 50,
        food_reserves: 100
      },
      jobs: [
        { id: 1, name: 'Worker', number: 10, free: 0, salary: 1 },
        { id: 2, name: 'Miner', number: 5, free: 1, salary: 2 },
        { id: 3, name: 'Farmer', number: 5, free: 0, salary: 2 }
      ],
      events: []
    };
    
    // Mock pour fetchGameState
    mockFetchResponse(mockGameState);
    
    // Mock pour les autres appels API qui pourraient être faits
    mockFetchResponse({ success: true });
  });

  test('navigates to jobs tab and displays job information', async () => {
    render(<App />);
    
    // Attendre que le dashboard soit chargé
    await waitFor(() => {
      expect(screen.getByText('Gestion de la Mine')).toBeInTheDocument();
    });
    
    // Cliquer sur l'onglet Emplois
    fireEvent.click(screen.getByText('Emplois'));
    
    // Vérifier que l'onglet Emplois est affiché
    expect(screen.getByText('Emplois')).toBeInTheDocument();
    
    // Vérifier que les informations des emplois sont affichées
    await waitFor(() => {
      expect(screen.getByText('⛏️ Mineur')).toBeInTheDocument();
      expect(screen.getByText('🌾 Fermier')).toBeInTheDocument();
    });
  });

  test('shift key changes button text from +/- to +5/-5', async () => {
    render(<App />);
    
    // Attendre que le dashboard soit chargé
    await waitFor(() => {
      expect(screen.getByText('Gestion de la Mine')).toBeInTheDocument();
    });
    
    // Cliquer sur l'onglet Emplois
    fireEvent.click(screen.getByText('Emplois'));
    
    // Attendre que les boutons soient chargés
    await waitFor(() => {
      const buttons = screen.getAllByText('+');
      expect(buttons.length).toBeGreaterThan(0);
    });
    
    // Vérifier que les boutons affichent + et - par défaut
    const plusButtons = screen.getAllByText('+');
    const minusButtons = screen.getAllByText('-');
    expect(plusButtons.length).toBeGreaterThan(0);
    expect(minusButtons.length).toBeGreaterThan(0);
    
    // Simuler l'appui sur la touche Shift
    fireEvent.keyDown(document, { key: 'Shift' });
    
    // Vérifier que les boutons affichent +5 et -5 lorsque Shift est enfoncé
    await waitFor(() => {
      const plus5Buttons = screen.getAllByText('+5');
      const minus5Buttons = screen.getAllByText('-5');
      expect(plus5Buttons.length).toBeGreaterThan(0);
      expect(minus5Buttons.length).toBeGreaterThan(0);
    });
    
    // Simuler le relâchement de la touche Shift
    fireEvent.keyUp(document, { key: 'Shift' });
    
    // Vérifier que les boutons affichent à nouveau + et -
    await waitFor(() => {
      const plusButtons = screen.getAllByText('+');
      const minusButtons = screen.getAllByText('-');
      expect(plusButtons.length).toBeGreaterThan(0);
      expect(minusButtons.length).toBeGreaterThan(0);
    });
  });
});
