import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import App from '../App';

// Mock fetch pour simuler les appels API
global.fetch = jest.fn();

// Helper pour configurer les mocks de fetch
const mockFetchResponse = (data) => {
  global.fetch.mockResolvedValueOnce({
    ok: true,
    json: async () => data
  });
};

describe('Moral Tab', () => {
  beforeEach(() => {
    // Réinitialiser les mocks
    global.fetch.mockClear();
    
    // Mock des données de l'API pour fetchGameState
    const mockGameState = {
      gameState: {
        cycle_number: 1,
        month: 'Tanzanite',
        year: 1349,
        season: 'Hiver',
        season_factor: 0.5,
        treasure: 100,
        materials: 50,
        food_reserves: 100,
        moral_value: 1000
      },
      jobs: [
        { id: 1, name: 'Worker', number: 10, free: 0, salary: 1 },
        { id: 2, name: 'Miner', number: 5, free: 1, salary: 2 },
        { id: 3, name: 'Farmer', number: 5, free: 1, salary: 2 }
      ],
      events: []
    };
    
    // Mock pour fetchGameState
    mockFetchResponse(mockGameState);
    
    // Mock pour les modifiers de moral
    mockFetchResponse({
      modifiers: [
        { id: 1, table_id: 3, value: 50, title: 'Bonne nourriture', description: 'Les habitants sont bien nourris', start_date: '1349-01-01' },
        { id: 2, table_id: 3, value: -30, title: 'Logement insuffisant', description: 'Manque de logements', start_date: '1349-01-01' }
      ]
    });
    
    // Mock pour les autres appels API
    mockFetchResponse({ success: true });
  });

  test('navigates to moral tab and displays moral information', async () => {
    render(<App />);
    
    // Attendre que le dashboard soit chargé
    await waitFor(() => {
      expect(screen.getByText('Gestion de la Mine')).toBeInTheDocument();
    });
    
    // Cliquer sur l'onglet Moral
    fireEvent.click(screen.getByText('Moral'));
    
    // Vérifier que l'onglet Moral est affiché
    expect(screen.getByText('Moral')).toBeInTheDocument();
    
    // Vérifier que les informations de moral sont affichées
    await waitFor(() => {
      // Vérifier la présence du score de moral
      expect(screen.getByText(/Score/i)).toBeInTheDocument();
      
      // Vérifier la présence du titre de moral
      expect(screen.getByText(/Titre/i)).toBeInTheDocument();
      
      // Vérifier la présence de l'impact sur la productivité
      expect(screen.getByText(/Impact sur la productivité/i)).toBeInTheDocument();
    });
  });

  test('displays moral modifiers correctly', async () => {
    render(<App />);
    
    // Attendre que le dashboard soit chargé
    await waitFor(() => {
      expect(screen.getByText('Gestion de la Mine')).toBeInTheDocument();
    });
    
    // Cliquer sur l'onglet Moral
    fireEvent.click(screen.getByText('Moral'));
    
    // Vérifier que les modificateurs de moral sont affichés
    await waitFor(() => {
      expect(screen.getByText('Bonne nourriture')).toBeInTheDocument();
      expect(screen.getByText('Logement insuffisant')).toBeInTheDocument();
      
      // Vérifier les valeurs des modificateurs
      expect(screen.getByText('+50')).toBeInTheDocument();
      expect(screen.getByText('-30')).toBeInTheDocument();
    });
  });

  test('moral calculation is displayed correctly', async () => {
    render(<App />);
    
    // Attendre que le dashboard soit chargé
    await waitFor(() => {
      expect(screen.getByText('Gestion de la Mine')).toBeInTheDocument();
    });
    
    // Cliquer sur l'onglet Moral
    fireEvent.click(screen.getByText('Moral'));
    
    // Vérifier que le calcul de moral est affiché
    await waitFor(() => {
      // Vérifier le score de moral
      expect(screen.getByText(/Score/i)).toBeInTheDocument();
      expect(screen.getByText('1000')).toBeInTheDocument();
      
      // Vérifier le titre de moral (neutre pour 1000)
      expect(screen.getByText(/Titre/i)).toBeInTheDocument();
      expect(screen.getByText('Neutre')).toBeInTheDocument();
      
      // Vérifier l'impact sur la productivité
      expect(screen.getByText(/Impact sur la productivité/i)).toBeInTheDocument();
      expect(screen.getByText('0.0%')).toBeInTheDocument();
    });
  });

  test('adds a new moral modifier correctly', async () => {
    // Mock pour l'ajout d'un nouveau modificateur
    mockFetchResponse({
      success: true,
      modifier: {
        id: 3,
        table_id: 3,
        value: 20,
        title: 'Nouveau modificateur',
        description: 'Description du nouveau modificateur',
        start_date: '1349-01-01'
      }
    });
    
    render(<App />);
    
    // Attendre que le dashboard soit chargé
    await waitFor(() => {
      expect(screen.getByText('Gestion de la Mine')).toBeInTheDocument();
    });
    
    // Cliquer sur l'onglet Moral
    fireEvent.click(screen.getByText('Moral'));
    
    // Attendre que l'onglet Moral soit chargé
    await waitFor(() => {
      expect(screen.getByText('Bonne nourriture')).toBeInTheDocument();
    });
    
    // Cliquer sur le bouton pour ajouter un nouveau modificateur
    fireEvent.click(screen.getByText('Ajouter un modificateur'));
    
    // Remplir le formulaire
    fireEvent.change(screen.getByLabelText(/Titre/i), { target: { value: 'Nouveau modificateur' } });
    fireEvent.change(screen.getByLabelText(/Valeur/i), { target: { value: '20' } });
    fireEvent.change(screen.getByLabelText(/Description/i), { target: { value: 'Description du nouveau modificateur' } });
    
    // Soumettre le formulaire
    fireEvent.click(screen.getByText('Ajouter'));
    
    // Vérifier que la requête a été faite avec les bons paramètres
    expect(global.fetch).toHaveBeenCalledWith(
      expect.stringContaining('/api/moral/modifiers'),
      expect.objectContaining({
        method: 'POST',
        headers: expect.objectContaining({
          'Content-Type': 'application/json'
        }),
        body: expect.stringContaining('Nouveau modificateur')
      })
    );
  });
});
