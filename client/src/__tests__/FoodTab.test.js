import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import App from '../App';

// Mock fetch pour simuler les appels API
global.fetch = jest.fn();

// Helper pour configurer les mocks de fetch
const mockFetchResponse = (data) => {
  global.fetch.mockResolvedValueOnce({
    ok: true,
    json: async () => data
  });
};

describe('Food Tab', () => {
  beforeEach(() => {
    // Réinitialiser les mocks
    global.fetch.mockClear();
    
    // Mock des données de l'API pour fetchGameState
    const mockGameState = {
      gameState: {
        cycle_number: 1,
        month: 'Tanzanite',
        year: 1349,
        season: 'Hiver',
        season_factor: 0.5,
        treasure: 100,
        materials: 50,
        food_reserves: 100
      },
      jobs: [
        { id: 1, name: 'Worker', number: 10, free: 0, salary: 1 },
        { id: 2, name: 'Miner', number: 5, free: 1, salary: 2 },
        { id: 3, name: 'Farmer', number: 5, free: 1, salary: 2 }
      ],
      events: []
    };
    
    // Mock pour fetchGameState
    mockFetchResponse(mockGameState);
    
    // Mock pour les modifiers de nourriture
    mockFetchResponse({
      modifiers: [
        { id: 1, table_id: 1, value: 0.2, title: 'Tech Level', description: 'Bonus from technology' },
        { id: 2, table_id: 1, value: 0.8, title: 'General', description: 'General food production' }
      ]
    });
    
    // Mock pour les modifiers de péremption
    mockFetchResponse({
      modifiers: [
        { id: 1, table_id: 2, value: 0.2, title: 'Base Perishable', description: 'Base food perishable rate' }
      ]
    });
    
    // Mock pour les autres appels API
    mockFetchResponse({ success: true });
  });

  test('navigates to food tab and displays food information', async () => {
    render(<App />);
    
    // Attendre que le dashboard soit chargé
    await waitFor(() => {
      expect(screen.getByText('Gestion de la Mine')).toBeInTheDocument();
    });
    
    // Cliquer sur l'onglet Nourriture
    fireEvent.click(screen.getByText('Nourriture'));
    
    // Vérifier que l'onglet Nourriture est affiché
    expect(screen.getByText('Nourriture')).toBeInTheDocument();
    
    // Vérifier que les informations de nourriture sont affichées
    await waitFor(() => {
      // Vérifier la présence des fermiers
      expect(screen.getByText(/Fermiers actifs/i)).toBeInTheDocument();
      
      // Vérifier la présence des réserves
      expect(screen.getByText(/Réserves/i)).toBeInTheDocument();
      
      // Vérifier la présence de la production
      expect(screen.getByText(/Production/i)).toBeInTheDocument();
      
      // Vérifier la présence de la consommation
      expect(screen.getByText(/Consommation/i)).toBeInTheDocument();
    });
  });

  test('displays food modifiers correctly', async () => {
    render(<App />);
    
    // Attendre que le dashboard soit chargé
    await waitFor(() => {
      expect(screen.getByText('Gestion de la Mine')).toBeInTheDocument();
    });
    
    // Cliquer sur l'onglet Nourriture
    fireEvent.click(screen.getByText('Nourriture'));
    
    // Vérifier que les modificateurs de nourriture sont affichés
    await waitFor(() => {
      expect(screen.getByText('Tech Level')).toBeInTheDocument();
      expect(screen.getByText('General')).toBeInTheDocument();
      
      // Vérifier les valeurs des modificateurs
      expect(screen.getByText('20.0%')).toBeInTheDocument();
      expect(screen.getByText('80.0%')).toBeInTheDocument();
    });
  });

  test('displays perishable modifiers correctly', async () => {
    render(<App />);
    
    // Attendre que le dashboard soit chargé
    await waitFor(() => {
      expect(screen.getByText('Gestion de la Mine')).toBeInTheDocument();
    });
    
    // Cliquer sur l'onglet Nourriture
    fireEvent.click(screen.getByText('Nourriture'));
    
    // Cliquer sur l'onglet Péremption
    await waitFor(() => {
      const perishableTab = screen.getByText('Péremption');
      fireEvent.click(perishableTab);
    });
    
    // Vérifier que les modificateurs de péremption sont affichés
    await waitFor(() => {
      expect(screen.getByText('Base Perishable')).toBeInTheDocument();
      
      // Vérifier les valeurs des modificateurs
      expect(screen.getByText('20.0%')).toBeInTheDocument();
    });
  });

  test('food production calculation is displayed correctly', async () => {
    render(<App />);
    
    // Attendre que le dashboard soit chargé
    await waitFor(() => {
      expect(screen.getByText('Gestion de la Mine')).toBeInTheDocument();
    });
    
    // Cliquer sur l'onglet Nourriture
    fireEvent.click(screen.getByText('Nourriture'));
    
    // Vérifier que le calcul de production est affiché
    await waitFor(() => {
      // Vérifier les composants du calcul
      expect(screen.getByText(/Fermiers actifs/i)).toBeInTheDocument();
      expect(screen.getByText(/Production par fermier/i)).toBeInTheDocument();
      expect(screen.getByText(/Facteur de saison/i)).toBeInTheDocument();
      expect(screen.getByText(/Bonus du moral/i)).toBeInTheDocument();
      
      // Vérifier la production totale
      expect(screen.getByText(/Production totale/i)).toBeInTheDocument();
    });
  });
});
