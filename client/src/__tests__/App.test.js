import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import App from '../App';

// Mock fetch pour simuler les appels API
global.fetch = jest.fn();

// Helper pour configurer les mocks de fetch
const mockFetchResponse = (data) => {
  global.fetch.mockResolvedValueOnce({
    ok: true,
    json: async () => data
  });
};

describe('App Component', () => {
  beforeEach(() => {
    // Réinitialiser les mocks
    global.fetch.mockClear();
    
    // Mock des données de l'API pour fetchGameState
    const mockGameState = {
      gameState: {
        cycle_number: 1,
        month: 'Tanzanite',
        year: 1349,
        season: 'Hiver',
        season_factor: 0.5,
        treasure: 100,
        materials: 50,
        food_reserves: 100
      },
      jobs: [
        { id: 1, name: 'Worker', number: 10, free: 0, salary: 1 },
        { id: 2, name: 'Miner', number: 5, free: 1, salary: 2 },
        { id: 3, name: 'Farmer', number: 5, free: 0, salary: 2 }
      ],
      events: []
    };
    
    // Mock pour fetchGameState
    mockFetchResponse(mockGameState);
  });

  test('renders loading state initially', () => {
    render(<App />);
    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  test('renders dashboard after loading', async () => {
    render(<App />);
    
    // Attendre que le dashboard soit chargé
    await waitFor(() => {
      expect(screen.getByText('Gestion de la Mine')).toBeInTheDocument();
    });
    
    // Vérifier que les informations du jeu sont affichées
    expect(screen.getByText('Cycle:')).toBeInTheDocument();
    expect(screen.getByText('Mois:')).toBeInTheDocument();
    expect(screen.getByText('Année:')).toBeInTheDocument();
  });
});

// Ce test sera implémenté plus tard car il nécessite plus de mocks
// describe('Job Management', () => {
//   test('Job buttons show +1/-1 by default', async () => {
//     // À implémenter
//   });
// });
