import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';

// Composant simple pour tester le raccourci Shift
const ShiftKeyComponent = () => {
  const [shiftPressed, setShiftPressed] = React.useState(false);

  React.useEffect(() => {
    const handleKeyDown = (e) => {
      if (e.key === 'Shift') {
        setShiftPressed(true);
      }
    };

    const handleKeyUp = (e) => {
      if (e.key === 'Shift') {
        setShiftPressed(false);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('keyup', handleKeyUp);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('keyup', handleKeyUp);
    };
  }, []);

  return (
    <div>
      <h1>Shift Key Test</h1>
      <button>{shiftPressed ? '+5' : '+'}</button>
      <button>{shiftPressed ? '-5' : '-'}</button>
    </div>
  );
};

describe('Shift Key Component', () => {
  test('buttons show + and - by default', () => {
    render(<ShiftKeyComponent />);
    
    const plusButton = screen.getByText('+');
    const minusButton = screen.getByText('-');
    
    expect(plusButton).toBeInTheDocument();
    expect(minusButton).toBeInTheDocument();
  });

  test('buttons show +5 and -5 when Shift is pressed', () => {
    render(<ShiftKeyComponent />);
    
    // Simuler l'appui sur la touche Shift
    fireEvent.keyDown(document, { key: 'Shift' });
    
    const plus5Button = screen.getByText('+5');
    const minus5Button = screen.getByText('-5');
    
    expect(plus5Button).toBeInTheDocument();
    expect(minus5Button).toBeInTheDocument();
    
    // Simuler le relâchement de la touche Shift
    fireEvent.keyUp(document, { key: 'Shift' });
    
    const plusButton = screen.getByText('+');
    const minusButton = screen.getByText('-');
    
    expect(plusButton).toBeInTheDocument();
    expect(minusButton).toBeInTheDocument();
  });
});
