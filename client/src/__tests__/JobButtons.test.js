import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import App from '../App';

// Mock fetch pour simuler les appels API
global.fetch = jest.fn();

// Helper pour configurer les mocks de fetch
const mockFetchResponse = (data) => {
  global.fetch.mockResolvedValueOnce({
    ok: true,
    json: async () => data
  });
};

describe('Job Buttons', () => {
  beforeEach(() => {
    // Réinitialiser les mocks
    global.fetch.mockClear();
    
    // Mock des données de l'API pour fetchGameState avec un cas où certains boutons devraient être désactivés
    const mockGameState = {
      gameState: {
        cycle_number: 1,
        month: 'Tanzanite',
        year: 1349,
        season: 'Hiver',
        season_factor: 0.5,
        treasure: 100,
        materials: 50,
        food_reserves: 100
      },
      jobs: [
        { id: 1, name: 'Worker', number: 0, free: 0, salary: 1 }, // Aucun ouvrier disponible
        { id: 2, name: 'Miner', number: 5, free: 5, salary: 2 },  // Tous les mineurs sont gratuits
        { id: 3, name: 'Farmer', number: 5, free: 0, salary: 2 }  // Aucun fermier gratuit
      ],
      events: []
    };
    
    // Mock pour fetchGameState
    mockFetchResponse(mockGameState);
    
    // Mock pour les autres appels API qui pourraient être faits
    mockFetchResponse({ success: true });
  });

  test('buttons are disabled when action is not possible', async () => {
    render(<App />);
    
    // Attendre que le dashboard soit chargé
    await waitFor(() => {
      expect(screen.getByText('Gestion de la Mine')).toBeInTheDocument();
    });
    
    // Cliquer sur l'onglet Emplois
    fireEvent.click(screen.getByText('Emplois'));
    
    // Attendre que les emplois soient chargés
    await waitFor(() => {
      expect(screen.getByText('⛏️ Mineur')).toBeInTheDocument();
    });
    
    // Trouver tous les boutons
    const buttons = screen.getAllByRole('button');
    
    // Vérifier qu'il y a des boutons désactivés
    const disabledButtons = buttons.filter(button => button.disabled);
    expect(disabledButtons.length).toBeGreaterThan(0);
    
    // Vérifier spécifiquement que certains boutons sont désactivés
    // Par exemple, le bouton - pour les ouvriers devrait être désactivé car il n'y en a pas
    const workerSection = screen.getByText('👷 Ouvrier').closest('tr');
    const workerMinusButton = workerSection.querySelector('button.job-action-btn.decrease');
    expect(workerMinusButton).toBeDisabled();
    
    // Le bouton + pour augmenter le nombre de mineurs devrait être désactivé car il n'y a pas d'ouvriers disponibles
    const minerSection = screen.getByText('⛏️ Mineur').closest('tr');
    const minerPlusButton = minerSection.querySelector('button.job-action-btn.increase');
    expect(minerPlusButton).toBeDisabled();
  });

  test('shift key changes disabled state based on available workers', async () => {
    // Modifier le mock pour avoir 3 ouvriers disponibles (suffisant pour +1 mais pas pour +5)
    global.fetch.mockClear();
    mockFetchResponse({
      gameState: {
        cycle_number: 1,
        month: 'Tanzanite',
        year: 1349,
        season: 'Hiver',
        season_factor: 0.5,
        treasure: 100,
        materials: 50,
        food_reserves: 100
      },
      jobs: [
        { id: 1, name: 'Worker', number: 3, free: 0, salary: 1 }, // 3 ouvriers disponibles
        { id: 2, name: 'Miner', number: 5, free: 0, salary: 2 },
        { id: 3, name: 'Farmer', number: 5, free: 0, salary: 2 }
      ],
      events: []
    });
    mockFetchResponse({ success: true });
    
    render(<App />);
    
    // Attendre que le dashboard soit chargé
    await waitFor(() => {
      expect(screen.getByText('Gestion de la Mine')).toBeInTheDocument();
    });
    
    // Cliquer sur l'onglet Emplois
    fireEvent.click(screen.getByText('Emplois'));
    
    // Attendre que les emplois soient chargés
    await waitFor(() => {
      expect(screen.getByText('⛏️ Mineur')).toBeInTheDocument();
    });
    
    // Trouver la section des mineurs
    const minerSection = screen.getByText('⛏️ Mineur').closest('tr');
    const minerPlusButton = minerSection.querySelector('button.job-action-btn.increase');
    
    // Le bouton + devrait être activé car il y a 3 ouvriers disponibles
    expect(minerPlusButton).not.toBeDisabled();
    
    // Simuler l'appui sur la touche Shift
    fireEvent.keyDown(document, { key: 'Shift' });
    
    // Attendre que les boutons changent
    await waitFor(() => {
      expect(screen.getAllByText('+5').length).toBeGreaterThan(0);
    });
    
    // Le bouton +5 devrait être désactivé car il n'y a que 3 ouvriers disponibles (pas assez pour +5)
    expect(minerPlusButton).toBeDisabled();
    
    // Simuler le relâchement de la touche Shift
    fireEvent.keyUp(document, { key: 'Shift' });
    
    // Attendre que les boutons reviennent à leur état normal
    await waitFor(() => {
      expect(screen.getAllByText('+').length).toBeGreaterThan(0);
    });
    
    // Le bouton + devrait être à nouveau activé
    expect(minerPlusButton).not.toBeDisabled();
  });
});
