/**
 * Hook for using the enhanced calculation service
 * 
 * Provides a standardized way to access calculation functions
 * with optimized performance and memoization.
 */
import { useCallback, useMemo } from 'react';
import { useSelector } from 'react-redux';
import { 
  selectGameState,
  selectJobs,
  selectModifierTables
} from '../store/slices/gameSlice';
import calculationServiceEnhanced from '../services/CalculationServiceEnhanced';

/**
 * Hook for using the enhanced calculation service
 * @returns {Object} - Calculation functions and derived data
 */
const useCalculation = () => {
  // Select data from Redux store
  const gameState = useSelector(selectGameState);
  const jobs = useSelector(selectJobs);
  const modifierTables = useSelector(selectModifierTables);
  
  // Get moral modifier
  const moralModifier = useMemo(() => {
    if (!gameState) return 0;
    return calculationServiceEnhanced.calculateMoralModifier(gameState.moral_value || 1.0);
  }, [gameState]);
  
  // Get season factor
  const seasonFactor = useMemo(() => {
    if (!gameState) return 1.0;
    return gameState.season_factor || 1.0;
  }, [gameState]);
  
  // Get food data
  const foodData = useMemo(() => {
    if (!gameState || !jobs || !modifierTables) return null;
    
    // Get food modifiers
    const foodGeneralTable = modifierTables.find(t => t.id === 1) || { current_value: 0 };
    const foodTechTable = modifierTables.find(t => t.id === 2) || { current_value: 0 };
    const perishableTable = modifierTables.find(t => t.id === 3) || { current_value: 0.2 };
    
    return calculationServiceEnhanced.calculateFoodStats(
      jobs,
      seasonFactor,
      gameState.food_reserves || 0,
      foodGeneralTable.current_value,
      foodTechTable.current_value,
      perishableTable.current_value,
      moralModifier,
      null
    );
  }, [gameState, jobs, modifierTables, seasonFactor, moralModifier]);
  
  // Get mining data
  const miningData = useMemo(() => {
    if (!gameState || !jobs || !modifierTables) return null;
    
    // Get mining modifiers
    const miningGeneralTable = modifierTables.find(t => t.id === 4) || { current_value: 0 };
    const miningTechTable = modifierTables.find(t => t.id === 5) || { current_value: 0 };
    const engineeringTable = modifierTables.find(t => t.id === 6) || { current_value: 0 };
    
    // Get miners and engineers
    const miners = jobs.find(j => j.name === 'Miner') || { number: 0, sick: 0 };
    const engineers = jobs.find(j => j.name === 'Engineer') || { number: 0, sick: 0 };
    
    // Calculate active miners and engineers
    const activeMiners = miners.number - (miners.sick || 0);
    const activeEngineers = engineers.number - (engineers.sick || 0);
    
    // Calculate engineering multiplier
    const engineeringMultiplier = calculationServiceEnhanced.calculateEngineeringMultiplier(
      activeEngineers,
      engineeringTable.current_value
    );
    
    // Calculate mining production
    const miningProduction = calculationServiceEnhanced.calculateMiningProduction(
      activeMiners,
      miningGeneralTable.current_value,
      miningTechTable.current_value,
      moralModifier,
      engineeringMultiplier
    );
    
    return {
      activeMiners,
      activeEngineers,
      engineeringMultiplier,
      miningProduction,
      modifiers: {
        miningGeneralModifier: miningGeneralTable.current_value,
        miningTechModifier: miningTechTable.current_value,
        engineeringModifier: engineeringTable.current_value,
        moralModifier
      }
    };
  }, [gameState, jobs, modifierTables, moralModifier]);
  
  // Get materials data
  const materialsData = useMemo(() => {
    if (!gameState || !jobs || !modifierTables) return null;
    
    // Get materials modifiers
    const materialsGeneralTable = modifierTables.find(t => t.id === 7) || { current_value: 0 };
    const materialsTechTable = modifierTables.find(t => t.id === 8) || { current_value: 0 };
    
    // Get workers and miners
    const workers = jobs.find(j => j.name === 'Worker') || { number: 0, sick: 0 };
    const miners = jobs.find(j => j.name === 'Miner') || { number: 0, sick: 0 };
    
    // Calculate active workers and miners
    const activeWorkers = workers.number - (workers.sick || 0);
    const activeMiners = miners.number - (miners.sick || 0);
    
    // Calculate materials production
    const materialsProduction = calculationServiceEnhanced.calculateMaterialsProduction(
      activeWorkers,
      activeMiners,
      4, // materialsPerWorker
      2, // materialsPerMiner
      materialsGeneralTable.current_value,
      materialsTechTable.current_value,
      moralModifier
    );
    
    return {
      activeWorkers,
      activeMiners,
      materialsProduction,
      modifiers: {
        materialsGeneralModifier: materialsGeneralTable.current_value,
        materialsTechModifier: materialsTechTable.current_value,
        moralModifier
      }
    };
  }, [gameState, jobs, modifierTables, moralModifier]);
  
  // Get research data
  const researchData = useMemo(() => {
    if (!gameState || !jobs) return null;
    
    return calculationServiceEnhanced.calculateResearchStats(
      jobs,
      gameState.tech_level || 0,
      gameState.research_factor || 1.0,
      moralModifier
    );
  }, [gameState, jobs, moralModifier]);
  
  // Get charges data
  const chargesData = useMemo(() => {
    if (!gameState || !jobs || !modifierTables) return null;
    
    // Get charges modifiers
    const chargesTable = modifierTables.find(t => t.id === 9) || { current_value: 0 };
    
    // Calculate salaries
    const salaries = calculationServiceEnhanced.calculateSalaries(jobs);
    
    // Calculate non-salary charges
    const nonSalaryCharges = calculationServiceEnhanced.calculateNonSalaryCharges(
      jobs.reduce((sum, job) => sum + job.number, 0),
      gameState.charges_per_inhabitant || 1,
      chargesTable.current_value
    );
    
    // Calculate total charges
    const totalCharges = calculationServiceEnhanced.calculateTotalCharges(
      salaries,
      nonSalaryCharges
    );
    
    return {
      salaries,
      nonSalaryCharges,
      totalCharges,
      modifiers: {
        chargesModifier: chargesTable.current_value
      }
    };
  }, [gameState, jobs, modifierTables]);
  
  // Calculate moral stats
  const calculateMoralStats = useCallback(() => {
    if (!gameState) return null;
    
    return calculationServiceEnhanced.calculateMoralStats(
      gameState.moral_value || 1.0,
      moralModifier
    );
  }, [gameState, moralModifier]);
  
  // Calculate health stats
  const calculateHealthStats = useCallback(() => {
    if (!gameState || !jobs || !modifierTables) return null;
    
    // Get health modifiers
    const healthTable = modifierTables.find(t => t.id === 10) || { current_value: 0 };
    
    // Get healers
    const healers = jobs.find(j => j.name === 'Healer') || { number: 0, sick: 0 };
    
    // Calculate active healers
    const activeHealers = healers.number - (healers.sick || 0);
    
    // Calculate sick probability
    const sickProbability = calculationServiceEnhanced.calculateSickProbability(
      jobs.reduce((sum, job) => sum + job.number, 0),
      activeHealers,
      healthTable.current_value
    );
    
    return calculationServiceEnhanced.calculateHealthStats(
      jobs,
      sickProbability,
      healthTable.current_value
    );
  }, [gameState, jobs, modifierTables]);
  
  // Clear calculation caches
  const clearCaches = useCallback(() => {
    calculationServiceEnhanced.clearCaches();
  }, []);
  
  return {
    // Derived data
    moralModifier,
    seasonFactor,
    foodData,
    miningData,
    materialsData,
    researchData,
    chargesData,
    
    // Calculation functions
    calculateMoralStats,
    calculateHealthStats,
    calculateMiningProduction: calculationServiceEnhanced.calculateMiningProduction,
    calculateFoodProduction: calculationServiceEnhanced.calculateFoodProduction,
    calculateMaterialsProduction: calculationServiceEnhanced.calculateMaterialsProduction,
    calculateSalaries: calculationServiceEnhanced.calculateSalaries,
    calculateTotalCharges: calculationServiceEnhanced.calculateTotalCharges,
    calculateSickProbability: calculationServiceEnhanced.calculateSickProbability,
    calculateResearchProbability: calculationServiceEnhanced.calculateResearchProbability,
    calculateTradingRevenue: calculationServiceEnhanced.calculateTradingRevenue,
    calculateCrimeLevel: calculationServiceEnhanced.calculateCrimeLevel,
    calculateDefenseLevel: calculationServiceEnhanced.calculateDefenseLevel,
    calculateRenownLevel: calculationServiceEnhanced.calculateRenownLevel,
    calculateArmyStrength: calculationServiceEnhanced.calculateArmyStrength,
    
    // Utility functions
    clearCaches
  };
};

export default useCalculation;
