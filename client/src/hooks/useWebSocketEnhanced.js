/**
 * Enhanced hook for WebSocket integration
 *
 * Provides a standardized way to interact with WebSockets
 * with optimized performance and error handling.
 */
import { useCallback, useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import {
  selectWebSocketConnected,
  selectWebSocketConnecting,
  selectWebSocketError,
  selectWebSocketLastMessage,
  selectWebSocketConnectionInfo
} from '../store/slices/websocketSlice';
import { wsConnect, wsDisconnect, wsSend } from '../middleware/websocketMiddleware';
import { handleWebSocketError } from '../utils/errorHandler';

/**
 * Enhanced hook for WebSocket integration
 * @param {Object} options - Hook options
 * @param {boolean} options.autoConnect - Whether to automatically connect
 * @param {Function} options.onMessage - Message callback
 * @param {Function} options.onConnect - Connect callback
 * @param {Function} options.onDisconnect - Disconnect callback
 * @param {Function} options.onError - Error callback
 * @returns {Object} - WebSocket state and functions
 */
const useWebSocketEnhanced = ({
  autoConnect = true,
  onMessage = null,
  onConnect = null,
  onDisconnect = null,
  onError = null
} = {}) => {
  const dispatch = useDispatch();
  const connected = useSelector(selectWebSocketConnected);
  const connecting = useSelector(selectWebSocketConnecting);
  const error = useSelector(selectWebSocketError);
  const lastMessage = useSelector(selectWebSocketLastMessage);
  const connectionInfo = useSelector(selectWebSocketConnectionInfo);
  const [messages, setMessages] = useState([]);

  // Connect to WebSocket
  const connect = useCallback(() => {
    try {
      dispatch(wsConnect({ url: 'ws://localhost:3003' }));
    } catch (error) {
      const formattedError = handleWebSocketError(error);

      if (onError) {
        onError(formattedError);
      }
    }
  }, [dispatch, onError]);

  // Disconnect from WebSocket
  const disconnect = useCallback(() => {
    try {
      dispatch(wsDisconnect());
    } catch (error) {
      const formattedError = handleWebSocketError(error);

      if (onError) {
        onError(formattedError);
      }
    }
  }, [dispatch, onError]);

  // Send a message to WebSocket
  const sendMessage = useCallback((message) => {
    try {
      dispatch(wsSend(message));
    } catch (error) {
      const formattedError = handleWebSocketError(error);

      if (onError) {
        onError(formattedError);
      }
    }
  }, [dispatch, onError]);

  // Handle new messages
  useEffect(() => {
    if (lastMessage) {
      // Add message to messages array
      setMessages((prevMessages) => [...prevMessages, lastMessage]);

      // Call onMessage callback if provided
      if (onMessage) {
        onMessage(lastMessage);
      }
    }
  }, [lastMessage, onMessage]);

  // Handle connection state changes
  useEffect(() => {
    if (connected) {
      if (onConnect) {
        onConnect();
      }
    } else if (!connecting && connectionInfo.wasConnected) {
      if (onDisconnect) {
        onDisconnect(connectionInfo.disconnectReason);
      }
    }
  }, [connected, connecting, connectionInfo, onConnect, onDisconnect]);

  // Handle errors
  useEffect(() => {
    if (error && onError) {
      onError(error);
    }
  }, [error, onError]);

  // Connect automatically if autoConnect is true
  useEffect(() => {
    if (autoConnect && !connected && !connecting) {
      connect();
    }

    // Disconnect on unmount
    return () => {
      if (connected) {
        disconnect();
      }
    };
  }, [autoConnect, connected, connecting, connect, disconnect]);

  // Clear messages
  const clearMessages = useCallback(() => {
    setMessages([]);
  }, []);

  return {
    connected,
    connecting,
    error,
    lastMessage,
    messages,
    connectionInfo,
    connect,
    disconnect,
    sendMessage,
    clearMessages
  };
};

export default useWebSocketEnhanced;
