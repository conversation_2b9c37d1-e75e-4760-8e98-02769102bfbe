/**
 * Hook for using the enhanced error handling
 * 
 * Provides a standardized way to handle errors in components.
 */
import { useState, useCallback } from 'react';
import { 
  formatError, 
  logError, 
  ERROR_TYPES, 
  ERROR_SEVERITY 
} from '../utils/errorHandler';

/**
 * Hook for using the enhanced error handling
 * @param {Object} options - Error handling options
 * @param {Function} options.onError - Error callback
 * @param {boolean} options.logErrors - Whether to log errors
 * @returns {Object} - Error state and functions
 */
const useErrorHandling = ({
  onError = null,
  logErrors = true
} = {}) => {
  const [error, setError] = useState(null);
  
  /**
   * Handle an error
   * @param {Error|Object|string} errorData - Error data
   * @param {string} type - Error type
   * @param {string} severity - Error severity
   * @returns {Object} - Formatted error
   */
  const handleError = useCallback((
    errorData,
    type = ERROR_TYPES.UNKNOWN,
    severity = ERROR_SEVERITY.ERROR
  ) => {
    // Format error
    const formattedError = formatError(errorData, type, severity);
    
    // Log error if enabled
    if (logErrors) {
      logError(formattedError);
    }
    
    // Update error state
    setError(formattedError);
    
    // Call onError callback if provided
    if (onError) {
      onError(formattedError);
    }
    
    return formattedError;
  }, [logErrors, onError]);
  
  /**
   * Clear the error
   */
  const clearError = useCallback(() => {
    setError(null);
  }, []);
  
  /**
   * Handle an API error
   * @param {Error|Object|string} errorData - Error data
   * @param {string} severity - Error severity
   * @returns {Object} - Formatted error
   */
  const handleApiError = useCallback((
    errorData,
    severity = ERROR_SEVERITY.ERROR
  ) => {
    return handleError(errorData, ERROR_TYPES.API, severity);
  }, [handleError]);
  
  /**
   * Handle a network error
   * @param {Error|Object|string} errorData - Error data
   * @param {string} severity - Error severity
   * @returns {Object} - Formatted error
   */
  const handleNetworkError = useCallback((
    errorData,
    severity = ERROR_SEVERITY.ERROR
  ) => {
    return handleError(errorData, ERROR_TYPES.NETWORK, severity);
  }, [handleError]);
  
  /**
   * Handle a validation error
   * @param {Error|Object|string} errorData - Error data
   * @param {string} severity - Error severity
   * @returns {Object} - Formatted error
   */
  const handleValidationError = useCallback((
    errorData,
    severity = ERROR_SEVERITY.WARNING
  ) => {
    return handleError(errorData, ERROR_TYPES.VALIDATION, severity);
  }, [handleError]);
  
  /**
   * Handle a WebSocket error
   * @param {Error|Object|string} errorData - Error data
   * @param {string} severity - Error severity
   * @returns {Object} - Formatted error
   */
  const handleWebSocketError = useCallback((
    errorData,
    severity = ERROR_SEVERITY.ERROR
  ) => {
    return handleError(errorData, ERROR_TYPES.WEBSOCKET, severity);
  }, [handleError]);
  
  return {
    error,
    handleError,
    clearError,
    handleApiError,
    handleNetworkError,
    handleValidationError,
    handleWebSocketError,
    ERROR_TYPES,
    ERROR_SEVERITY
  };
};

export default useErrorHandling;
