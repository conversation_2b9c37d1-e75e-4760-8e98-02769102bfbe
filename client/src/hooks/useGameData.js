/**
 * Hook personnalisé pour récupérer et gérer les données du jeu
 *
 * Note: Ce hook est maintenu pour la compatibilité avec le code existant.
 * Il réexporte simplement useGameDataEnhanced qui est la version améliorée.
 */
import useGameDataEnhanced from './useGameDataEnhanced';

/**
 * Hook pour récupérer et gérer les données du jeu
 * @param {Object} options - Options de configuration (ignorées, utilisez les options de useGameDataEnhanced)
 * @returns {Object} - Données du jeu et fonctions de mise à jour
 */
const useGameData = (options = {}) => {
  // Utiliser directement le hook amélioré
  return useGameDataEnhanced();
};

export default useGameData;
