/**
 * Hook for using the enhanced service registry
 * 
 * Provides a standardized way to access services from the service registry.
 */
import { useMemo } from 'react';
import serviceRegistry from '../services/ServiceRegistry';

/**
 * Hook for using a service from the service registry
 * @param {string} serviceName - Service name
 * @returns {Object} - Service instance
 */
const useService = (serviceName) => {
  // Get service from registry
  const service = useMemo(() => {
    if (!serviceName) {
      console.warn('useService: No service name provided');
      return null;
    }
    
    if (!serviceRegistry.has(serviceName)) {
      console.warn(`useService: Service '${serviceName}' not found`);
      return null;
    }
    
    return serviceRegistry.get(serviceName);
  }, [serviceName]);
  
  return service;
};

/**
 * Hook for using the game service
 * @returns {Object} - Game service instance
 */
export const useGameService = () => {
  return useService('game');
};

/**
 * Hook for using the modifiers service
 * @returns {Object} - Modifiers service instance
 */
export const useModifiersService = () => {
  return useService('modifiers');
};

/**
 * Hook for using the inhabitants service
 * @returns {Object} - Inhabitants service instance
 */
export const useInhabitantsService = () => {
  return useService('inhabitants');
};

/**
 * Hook for using the calculation service
 * @returns {Object} - Calculation service instance
 */
export const useCalculationService = () => {
  return useService('calculation');
};

/**
 * Hook for using the cache service
 * @returns {Object} - Cache service instance
 */
export const useCacheService = () => {
  return useService('cache');
};

export default useService;
