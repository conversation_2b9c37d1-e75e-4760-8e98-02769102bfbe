/**
 * Hook personnalisé pour accéder aux statistiques du jeu
 * Ce hook utilise le service StatsService pour calculer et fournir des statistiques
 */
import { useState, useEffect, useCallback } from 'react';
import StatsService from '../services/StatsService';
import useGameData from './useGameData';

/**
 * Hook pour accéder aux statistiques du jeu
 * @param {Object} options - Options du hook
 * @param {boolean} options.autoLoad - Charger automatiquement les données au montage
 * @param {string} options.statType - Type de statistique à calculer (food, moral, health, research, etc.)
 * @returns {Object} - Statistiques, état de chargement, erreur et fonction de rechargement
 */
const useStats = (options = {}) => {
  const { autoLoad = true, statType = 'all' } = options;

  // Utiliser le hook useGameData pour obtenir les données du jeu
  const { data: gameData, loading: gameDataLoading, error: gameDataError, refetch: refetchGameData } = useGameData();

  // États pour les statistiques
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fonction pour calculer les statistiques
  const calculateStats = useCallback(async () => {
    if (!gameData || gameDataLoading) return;

    try {
      setLoading(true);
      setError(null);

      // Extraire les données nécessaires
      const { gameState, jobs, modifiers } = gameData;

      // Calculer les statistiques en fonction du type demandé
      let calculatedStats = {};

      switch (statType) {
        case 'food':
          // Obtenir les modificateurs de nourriture
          const foodGeneralModifiers = modifiers && modifiers[1] ? modifiers[1] : [];
          const foodTechModifiers = modifiers && modifiers[2] ? modifiers[2] : [];
          const perishableModifiers = modifiers && modifiers[3] ? modifiers[3] : [];

          // Calculer les totaux
          const generalEffectsTotal = foodGeneralModifiers.reduce((sum, mod) => sum + (mod.effect || 0), 0);
          const techEffectsTotal = foodTechModifiers.reduce((sum, mod) => sum + (mod.effect || 0), 0);
          const perishableFactor = perishableModifiers.reduce((sum, mod) => sum + (mod.effect || 0), 0);

          // Calculer le modificateur de moral
          const moralValue = gameState?.moral_value || 1.0;
          const moralModifier = moralValue - 1.0;

          // Calculer les statistiques de nourriture
          calculatedStats = StatsService.calculateFoodStats(
            jobs,
            gameState?.season_factor || 1.0,
            gameState?.food_reserves || 0,
            generalEffectsTotal,
            techEffectsTotal,
            perishableFactor,
            moralModifier
          );

          // Ajouter les totaux
          calculatedStats.totals = {
            generalEffectsTotal,
            techEffectsTotal,
            perishableFactor,
            totalProductionBonus: generalEffectsTotal + techEffectsTotal
          };
          break;

        case 'moral':
          // Calculer les statistiques de moral
          calculatedStats = StatsService.calculateMoralStats(gameState?.moral_value || 1.0);
          break;

        case 'health':
          // Obtenir les modificateurs de santé
          const healthModifiers = modifiers[6] || [];

          // Calculer le total des modificateurs de santé
          const healthFactor = healthModifiers.reduce((sum, mod) => sum + (mod.effect || 0), 0);

          // Calculer le modificateur de moral
          const moralValueHealth = gameState?.moral_value || 1.0;
          const moralModifierHealth = moralValueHealth - 1.0;

          // Calculer les statistiques de santé
          calculatedStats = StatsService.calculateHealthStats(
            jobs,
            healthFactor,
            moralModifierHealth
          );
          break;

        case 'research':
          // Obtenir les modificateurs de recherche
          const researchModifiers = modifiers[7] || [];

          // Calculer le total des modificateurs de recherche
          const researchFactor = researchModifiers.reduce((sum, mod) => sum + (mod.effect || 0), 0);

          // Calculer le modificateur de moral
          const moralValueResearch = gameState?.moral_value || 1.0;
          const moralModifierResearch = moralValueResearch - 1.0;

          // Calculer les statistiques de recherche
          calculatedStats = StatsService.calculateResearchStats(
            jobs,
            researchFactor,
            moralModifierResearch,
            gameState?.tech_level || 0
          );
          break;

        case 'all':
        default:
          // Calculer toutes les statistiques
          // Pour l'instant, nous utilisons l'API pour obtenir toutes les statistiques
          calculatedStats = await StatsService.getAllStats();
          break;
      }

      setStats(calculatedStats);
    } catch (err) {
      console.error(`useStats: Error calculating ${statType} stats:`, err);
      setError(err.message || `Failed to calculate ${statType} statistics`);
    } finally {
      setLoading(false);
    }
  }, [gameData, gameDataLoading, statType]);

  // Fonction pour recharger les statistiques
  const refetch = useCallback(async () => {
    await refetchGameData();
    calculateStats();
  }, [refetchGameData, calculateStats]);

  // Calculer les statistiques au montage ou lorsque les données changent
  useEffect(() => {
    if (autoLoad && gameData && !gameDataLoading) {
      calculateStats();
    }
  }, [autoLoad, gameData, gameDataLoading, calculateStats]);

  return {
    stats,
    loading: loading || gameDataLoading,
    error: error || gameDataError,
    refetch
  };
};

export default useStats;
