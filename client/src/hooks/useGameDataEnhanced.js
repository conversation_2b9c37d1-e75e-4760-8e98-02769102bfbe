/**
 * Enhanced hook for accessing game data
 * 
 * Provides a centralized way to access game data with optimized performance.
 * Uses Redux for state management and optimized API requests.
 * Implements caching and deduplication of requests.
 */
import { useCallback } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { 
  fetchGameState, 
  processNextCycle,
  selectGameState,
  selectJobs,
  selectEvents,
  selectGameLoading,
  selectGameError,
  selectFoodData,
  selectGameCycle,
  selectGameMonth,
  selectGameYear,
  selectSeasonFactor,
  selectTreasure,
  selectFoodReserves,
  selectMaterials,
  selectMoralValue,
  selectModifierTableById
} from '../store/slices/gameSlice';
import { 
  changeFreeWorkers, 
  selectJobById 
} from '../store/slices/jobsSlice';
import apiEndpoint from '../services/ApiEndpoint';
import calculationServiceEnhanced from '../services/CalculationServiceEnhanced';

/**
 * Enhanced hook for accessing game data
 * @param {Object} options - Hook options
 * @param {boolean} options.autoRefresh - Whether to automatically refresh data
 * @param {number} options.refreshInterval - Refresh interval in milliseconds
 * @returns {Object} - Game data and functions
 */
const useGameDataEnhanced = (options = {}) => {
  const dispatch = useDispatch();
  
  // Select data from Redux store
  const gameState = useSelector(selectGameState);
  const jobs = useSelector(selectJobs);
  const events = useSelector(selectEvents);
  const loading = useSelector(selectGameLoading);
  const error = useSelector(selectGameError);
  const foodData = useSelector(selectFoodData);
  
  // Select derived data
  const cycle = useSelector(selectGameCycle);
  const month = useSelector(selectGameMonth);
  const year = useSelector(selectGameYear);
  const seasonFactor = useSelector(selectSeasonFactor);
  const treasure = useSelector(selectTreasure);
  const foodReserves = useSelector(selectFoodReserves);
  const materials = useSelector(selectMaterials);
  const moralValue = useSelector(selectMoralValue);
  
  // Calculate moral modifier
  const moralModifier = calculationServiceEnhanced.calculateMoralModifier(moralValue);
  
  /**
   * Refresh game data
   * @param {boolean} force - Whether to force refresh
   * @returns {Promise<Object>} - Game state
   */
  const refreshData = useCallback((force = false) => {
    return dispatch(fetchGameState({ force })).unwrap();
  }, [dispatch]);
  
  /**
   * Process next cycle
   * @returns {Promise<Object>} - Cycle results
   */
  const nextCycle = useCallback(() => {
    return dispatch(processNextCycle()).unwrap();
  }, [dispatch]);
  
  /**
   * Change job number
   * @param {number} jobId - Job ID
   * @param {number} change - Change amount
   * @returns {Promise<Object>} - Updated jobs
   */
  const changeJobNumber = useCallback(async (jobId, change) => {
    try {
      const result = await apiEndpoint.post('game/jobs/change', { jobId, change }, {
        invalidateCache: ['game/state']
      });
      
      // Refresh game state to reflect changes
      dispatch(fetchGameState());
      
      return result;
    } catch (error) {
      console.error('Error changing job number:', error);
      throw error;
    }
  }, [dispatch]);
  
  /**
   * Change free workers
   * @param {number} jobId - Job ID
   * @param {number} change - Change amount
   * @returns {Promise<Object>} - Updated jobs
   */
  const changeFreeWorkersCount = useCallback(async (jobId, change) => {
    try {
      await dispatch(changeFreeWorkers({ jobId, change })).unwrap();
      return true;
    } catch (error) {
      console.error('Error changing free workers:', error);
      throw error;
    }
  }, [dispatch]);
  
  /**
   * Transfer job
   * @param {number} fromJobId - Source job ID
   * @param {number} toJobId - Target job ID
   * @param {number} count - Number of workers to transfer
   * @returns {Promise<Object>} - Updated jobs
   */
  const transferJob = useCallback(async (fromJobId, toJobId, count) => {
    try {
      const result = await apiEndpoint.post('game/job-transfer', { 
        fromJobId, 
        toJobId, 
        count 
      }, {
        invalidateCache: ['game/state']
      });
      
      // Refresh game state to reflect changes
      dispatch(fetchGameState());
      
      return result;
    } catch (error) {
      console.error('Error transferring job:', error);
      throw error;
    }
  }, [dispatch]);
  
  /**
   * Get modifier table by ID
   * @param {number} tableId - Table ID
   * @returns {Object} - Modifier table
   */
  const getModifierTable = useCallback((tableId) => {
    return useSelector(selectModifierTableById(tableId));
  }, []);
  
  /**
   * Get job by ID
   * @param {number} jobId - Job ID
   * @returns {Object} - Job
   */
  const getJob = useCallback((jobId) => {
    return useSelector(selectJobById(jobId));
  }, []);
  
  return {
    // Game state
    gameState,
    jobs,
    events,
    loading,
    error,
    foodData,
    
    // Derived data
    cycle,
    month,
    year,
    seasonFactor,
    treasure,
    foodReserves,
    materials,
    moralValue,
    moralModifier,
    
    // Functions
    refreshData,
    nextCycle,
    changeJobNumber,
    changeFreeWorkers: changeFreeWorkersCount,
    transferJob,
    getModifierTable,
    getJob,
    
    // For compatibility with old hook
    data: gameState,
    refetch: refreshData
  };
};

export default useGameDataEnhanced;
