/**
 * Enhanced hooks index
 *
 * Exports all enhanced hooks for easy access.
 */

// Export enhanced hooks
export { default as useApiEndpoint } from './useApiEndpoint';
export { default as useCalculation } from './useCalculation';
export { default as useErrorHandling } from './useErrorHandling';
export { default as useGameData } from './useGameData';
export { default as useGameDataEnhanced } from './useGameDataEnhanced';
export { default as useReduxEnhanced } from './useReduxEnhanced';
export { default as useService } from './useService';
export { default as useWebSocketEnhanced } from './useWebSocketEnhanced';

// Export service-specific hooks
export {
  useGameService,
  useModifiersService,
  useInhabitantsService,
  useCalculationService,
  useCacheService
} from './useService';
