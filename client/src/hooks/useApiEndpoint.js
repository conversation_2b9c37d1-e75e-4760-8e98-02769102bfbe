/**
 * Hook for using the enhanced API endpoint service
 *
 * Provides a standardized way to make API requests with deduplication,
 * caching, and error handling.
 */
import { useCallback, useState, useEffect, useRef } from 'react';
import apiEndpoint from '../services/ApiEndpoint';
import { handleApiError, handleNetworkError } from '../utils/errorHandler';

/**
 * Hook for using the enhanced API endpoint service
 * @param {Object} options - API request options
 * @param {string} options.endpoint - API endpoint
 * @param {string} options.method - HTTP method
 * @param {Object} options.data - Request data
 * @param {Object} options.requestOptions - Additional request options
 * @param {boolean} options.autoFetch - Whether to fetch automatically
 * @param {Array} options.dependencies - Dependencies for auto-fetch
 * @returns {Object} - API request state and functions
 */
const useApiEndpoint = ({
  endpoint,
  method = 'GET',
  data = null,
  requestOptions = {},
  autoFetch = false,
  dependencies = []
} = {}) => {
  const [response, setResponse] = useState(null);
  const [loading, setLoading] = useState(autoFetch);
  const [error, setError] = useState(null);
  const mounted = useRef(false);

  /**
   * Make an API request
   * @param {Object} overrideOptions - Override options
   * @returns {Promise<Object>} - Response data
   */
  const fetchData = useCallback(async (overrideOptions = {}) => {
    // Get endpoint from options or from hook initialization
    const finalEndpoint = overrideOptions.endpoint || endpoint;

    // Skip if no endpoint
    if (!finalEndpoint) {
      console.error('useApiEndpoint: No endpoint provided');
      return null;
    }

    // Reset state
    setLoading(true);
    setError(null);

    try {
      console.log(`useApiEndpoint: Making ${method.toUpperCase()} request to ${finalEndpoint}`);

      let result;

      // Make request based on method
      switch (method.toUpperCase()) {
        case 'GET':
          result = await apiEndpoint.get(finalEndpoint, {
            ...requestOptions,
            ...overrideOptions
          });
          break;
        case 'POST':
          result = await apiEndpoint.post(finalEndpoint, overrideOptions.data || data, {
            ...requestOptions,
            ...overrideOptions
          });
          break;
        case 'PUT':
          result = await apiEndpoint.put(finalEndpoint, overrideOptions.data || data, {
            ...requestOptions,
            ...overrideOptions
          });
          break;
        case 'DELETE':
          result = await apiEndpoint.delete(finalEndpoint, {
            ...requestOptions,
            ...overrideOptions
          });
          break;
        default:
          throw new Error(`Unsupported method: ${method}`);
      }

      // Update state if component is still mounted
      if (mounted.current) {
        setResponse(result);
        setLoading(false);
      }

      return result;
    } catch (error) {
      // Format error based on type
      const formattedError = error.name === 'AbortError' || error.name === 'TypeError'
        ? handleNetworkError(error)
        : handleApiError(error);

      // Update state if component is still mounted
      if (mounted.current) {
        setError(formattedError);
        setLoading(false);
      }

      throw formattedError;
    }
  }, [endpoint, method, data, requestOptions]);

  /**
   * Reset the request state
   */
  const reset = useCallback(() => {
    setResponse(null);
    setLoading(false);
    setError(null);
  }, []);

  // Set mounted ref
  useEffect(() => {
    mounted.current = true;

    return () => {
      mounted.current = false;
    };
  }, []);

  // Fetch data automatically if autoFetch is true
  useEffect(() => {
    if (autoFetch && endpoint) {
      fetchData();
    }
  }, [autoFetch, endpoint, fetchData, ...dependencies]);

  return {
    data: response,
    loading,
    error,
    fetchData,
    reset
  };
};

export default useApiEndpoint;
