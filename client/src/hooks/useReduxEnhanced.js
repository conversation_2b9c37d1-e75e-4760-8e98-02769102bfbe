/**
 * Enhanced hook for Redux integration
 * 
 * Provides a standardized way to access Redux state and dispatch actions
 * with optimized performance and error handling.
 */
import { useCallback, useEffect, useRef, useState } from 'react';
import { useSelector, useDispatch, shallowEqual } from 'react-redux';
import { handleApiError } from '../utils/errorHandler';

/**
 * Enhanced hook for Redux integration
 * @param {Function} mapState - Function to map state to props
 * @param {Object} actions - Object of action creators
 * @param {Object} options - Hook options
 * @param {boolean} options.autoLoad - Whether to automatically load data
 * @param {Array} options.loadActions - Actions to dispatch on load
 * @param {Function} options.equalityFn - Equality function for state comparison
 * @param {Function} options.onError - Error callback
 * @returns {Object} - State, actions, and loading state
 */
const useReduxEnhanced = (
  mapState = () => ({}),
  actions = {},
  {
    autoLoad = false,
    loadActions = [],
    equalityFn = shallowEqual,
    onError = null
  } = {}
) => {
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(autoLoad);
  const [error, setError] = useState(null);
  const mounted = useRef(false);
  
  // Select state from Redux store
  const state = useSelector(mapState, equalityFn);
  
  // Map actions to dispatch
  const mappedActions = Object.entries(actions).reduce((acc, [key, actionCreator]) => {
    acc[key] = useCallback((...args) => {
      try {
        return dispatch(actionCreator(...args));
      } catch (error) {
        const formattedError = handleApiError(error);
        setError(formattedError);
        
        if (onError) {
          onError(formattedError);
        }
        
        throw formattedError;
      }
    }, [dispatch]);
    
    return acc;
  }, {});
  
  // Load data on mount if autoLoad is true
  useEffect(() => {
    mounted.current = true;
    
    if (autoLoad && loadActions.length > 0) {
      setLoading(true);
      
      const loadPromises = loadActions.map(action => {
        try {
          return dispatch(action());
        } catch (error) {
          const formattedError = handleApiError(error);
          
          if (mounted.current) {
            setError(formattedError);
          }
          
          if (onError) {
            onError(formattedError);
          }
          
          return Promise.reject(formattedError);
        }
      });
      
      Promise.all(loadPromises)
        .then(() => {
          if (mounted.current) {
            setLoading(false);
          }
        })
        .catch(() => {
          if (mounted.current) {
            setLoading(false);
          }
        });
    }
    
    return () => {
      mounted.current = false;
    };
  }, [autoLoad, dispatch, onError]);
  
  /**
   * Reload data
   * @returns {Promise<Array>} - Load promises
   */
  const reload = useCallback(() => {
    if (loadActions.length === 0) {
      return Promise.resolve([]);
    }
    
    setLoading(true);
    setError(null);
    
    const loadPromises = loadActions.map(action => {
      try {
        return dispatch(action());
      } catch (error) {
        const formattedError = handleApiError(error);
        
        if (mounted.current) {
          setError(formattedError);
        }
        
        if (onError) {
          onError(formattedError);
        }
        
        return Promise.reject(formattedError);
      }
    });
    
    Promise.all(loadPromises)
      .then(() => {
        if (mounted.current) {
          setLoading(false);
        }
      })
      .catch(() => {
        if (mounted.current) {
          setLoading(false);
        }
      });
    
    return loadPromises;
  }, [dispatch, loadActions, onError]);
  
  /**
   * Reset error state
   */
  const resetError = useCallback(() => {
    setError(null);
  }, []);
  
  return {
    ...state,
    ...mappedActions,
    loading,
    error,
    reload,
    resetError
  };
};

export default useReduxEnhanced;
