import React, { useMemo } from 'react';
import { useSelector, useDispatch } from 'react-redux';

/**
 * Higher-Order Component (HOC) pour connecter les composants à Redux
 *
 * @param {React.Component} WrappedComponent - Le composant à connecter
 * @param {Object} selectors - Sélecteurs Redux (clé: nom de prop, valeur: sélecteur)
 * @param {Object} actions - Actions Redux (clé: nom de prop, valeur: action)
 * @returns {React.Component} - Composant connecté
 */
export const withRedux = (WrappedComponent, selectors = {}, actions = {}) => {
  const WithReduxComponent = (props) => {
    const dispatch = useDispatch();

    // Appliquer les sélecteurs
    const selectorProps = {};
    Object.entries(selectors).forEach(([propName, selector]) => {
      // eslint-disable-next-line react-hooks/rules-of-hooks
      selectorProps[propName] = useSelector(state => selector ? selector(state) : undefined);
    });

    // Préparer les actions
    const dispatchProps = useMemo(() => {
      const actionProps = {};
      Object.entries(actions).forEach(([propName, actionCreator]) => {
        if (actionCreator) {
          actionProps[propName] = (...args) => dispatch(actionCreator(...args));
        }
      });
      return actionProps;
    }, [dispatch]);

    // Combiner les props (props directes prioritaires)
    const combinedProps = {
      ...selectorProps,
      ...dispatchProps,
      ...props
    };

    return <WrappedComponent {...combinedProps} />;
  };

  // Définir le nom d'affichage pour les outils de développement
  WithReduxComponent.displayName = `WithRedux(${WrappedComponent.displayName || WrappedComponent.name || 'Component'})`;

  return WithReduxComponent;
};

export default withRedux;
