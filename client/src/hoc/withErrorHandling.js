import React from 'react';
import ErrorBoundary from '../components/common/ErrorBoundary';

/**
 * Higher-Order Component for Error Handling
 * 
 * Wraps a component with an ErrorBoundary to catch and handle errors.
 * 
 * @param {React.Component} WrappedComponent - Component to wrap
 * @param {Object} options - Error handling options
 * @param {Function} options.fallback - Custom fallback UI
 * @param {Function} options.onError - Error callback
 * @param {Function} options.onReset - Reset callback
 * @param {boolean} options.showDetails - Whether to show error details
 * @returns {React.Component} - Component wrapped with error boundary
 */
const withErrorHandling = (
  WrappedComponent,
  {
    fallback = null,
    onError = null,
    onReset = null,
    showDetails = process.env.NODE_ENV !== 'production'
  } = {}
) => {
  // Create a new component that wraps the provided component with an ErrorBoundary
  const WithErrorHandling = (props) => (
    <ErrorBoundary
      fallback={fallback}
      onError={onError}
      onReset={onReset}
      showDetails={showDetails}
    >
      <WrappedComponent {...props} />
    </ErrorBoundary>
  );

  // Set display name for debugging
  const displayName = WrappedComponent.displayName || WrappedComponent.name || 'Component';
  WithErrorHandling.displayName = `withErrorHandling(${displayName})`;

  return WithErrorHandling;
};

export default withErrorHandling;
