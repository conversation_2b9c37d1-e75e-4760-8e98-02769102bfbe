/**
 * Enhanced Higher-Order Component for Redux integration
 * 
 * Provides a standardized way to connect components to Redux.
 * Implements performance optimizations and error handling.
 * Supports automatic data loading and refresh.
 */
import React, { useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { shallowEqual } from 'react-redux';

/**
 * Enhanced HOC for Redux integration
 * @param {React.Component} WrappedComponent - Component to wrap
 * @param {Function} mapStateToProps - Function to map state to props
 * @param {Function} mapDispatchToProps - Function to map dispatch to props
 * @param {Object} options - HOC options
 * @param {boolean} options.withRef - Whether to forward ref
 * @param {boolean} options.autoLoad - Whether to automatically load data
 * @param {Array} options.loadActions - Actions to dispatch on load
 * @param {Function} options.shouldComponentUpdate - Custom shouldComponentUpdate function
 * @returns {React.Component} - Enhanced component
 */
const withReduxEnhanced = (
  WrappedComponent,
  mapStateToProps = () => ({}),
  mapDispatchToProps = () => ({}),
  options = {}
) => {
  const {
    withRef = false,
    autoLoad = false,
    loadActions = [],
    shouldComponentUpdate = null
  } = options;
  
  // Component display name
  const displayName = WrappedComponent.displayName || WrappedComponent.name || 'Component';
  
  // Create enhanced component
  const EnhancedComponent = React.forwardRef((props, ref) => {
    const dispatch = useDispatch();
    const [isInitialLoad, setIsInitialLoad] = useState(true);
    const [loadError, setLoadError] = useState(null);
    
    // Select state from Redux store
    const stateProps = useSelector(
      state => mapStateToProps(state, props),
      shallowEqual
    );
    
    // Map dispatch to props
    const dispatchProps = {};
    Object.entries(mapDispatchToProps(dispatch, props)).forEach(([key, actionCreator]) => {
      dispatchProps[key] = (...args) => {
        try {
          return dispatch(actionCreator(...args));
        } catch (error) {
          console.error(`Error dispatching action ${key}:`, error);
          throw error;
        }
      };
    });
    
    // Load data on mount
    useEffect(() => {
      if (autoLoad && isInitialLoad) {
        setIsInitialLoad(false);
        
        // Dispatch load actions
        const loadPromises = loadActions.map(action => {
          try {
            return dispatch(action());
          } catch (error) {
            console.error(`Error dispatching load action:`, error);
            setLoadError(error);
            return Promise.reject(error);
          }
        });
        
        // Handle load errors
        Promise.all(loadPromises).catch(error => {
          console.error('Error loading data:', error);
          setLoadError(error);
        });
      }
    }, [autoLoad, dispatch, isInitialLoad]);
    
    // Combine props
    const combinedProps = {
      ...props,
      ...stateProps,
      ...dispatchProps,
      loadError
    };
    
    // Forward ref if needed
    const refProp = withRef ? { ref } : {};
    
    // Render wrapped component
    return <WrappedComponent {...combinedProps} {...refProp} />;
  });
  
  // Set display name
  EnhancedComponent.displayName = `withReduxEnhanced(${displayName})`;
  
  // Add static methods
  if (WrappedComponent.getInitialProps) {
    EnhancedComponent.getInitialProps = WrappedComponent.getInitialProps;
  }
  
  return EnhancedComponent;
};

export default withReduxEnhanced;
