:root {
  --primary-color: #4CAF50;
  --primary-hover: #45a049;
  --secondary-color: #f5f5f5;
  --border-color: #ddd;
  --text-color: #333;
  --header-color: #282c34;
  --positive-color: #4CAF50;
  --negative-color: #f44336;
  --warning-color: #ff9800;
  --info-color: #2196F3;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  color: var(--text-color);
  background-color: #f0f2f5;
}

.App {
  display: flex;
  min-height: 100vh;
}

.sidebar {
  width: 250px;
  background-color: var(--header-color);
  color: white;
  padding: 20px 0;
  box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  overflow-y: auto;
  z-index: 1000;
}

.sidebar-header {
  padding: 0 20px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  margin-bottom: 20px;
}

.sidebar-tabs {
  list-style: none;
}

.sidebar-tab {
  padding: 12px 20px;
  cursor: pointer;
  transition: background-color 0.3s;
  display: flex;
  align-items: center;
}

.sidebar-tab:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.sidebar-tab.active {
  background-color: rgba(255, 255, 255, 0.2);
  border-left: 4px solid var(--primary-color);
}

.sidebar-tab-icon {
  margin-right: 10px;
  width: 20px;
  text-align: center;
}

.main-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  margin-left: 250px; /* Same as sidebar width */
}

.App-header {
  background-color: white;
  padding: 20px;
  color: var(--text-color);
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.game-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.game-info-item {
  text-align: center;
}

.game-info-label {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 5px;
}

.game-info-value {
  font-size: 1.2rem;
  font-weight: bold;
}

.month-description {
  background-color: #fff9c4;
  border-left: 4px solid #ffd600;
  padding: 15px;
  margin: 20px 0;
  border-radius: 4px;
  text-align: left;
}

.resources-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin: 20px 0;
}

.resource-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
  position: relative;
  overflow: hidden;
}

.resource-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 10px;
}

.resource-title {
  font-size: 1.1rem;
  color: #666;
}

.resource-icon {
  font-size: 1.5rem;
}

.resource-value {
  font-size: 1.8rem;
  font-weight: bold;
  margin-bottom: 5px;
}

.resource-unit {
  font-size: 0.9rem;
  color: #666;
}

.resource-change {
  margin: 10px 0;
  font-weight: bold;
}

.resource-change-note {
  font-size: 0.8rem;
  font-weight: normal;
  color: #666;
  margin-left: 5px;
}

.positive {
  color: var(--positive-color);
}

.negative {
  color: var(--negative-color);
}

.resource-details {
  margin-top: 15px;
  font-size: 0.9rem;
}

.resource-detail-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

/* Events styling */
.events-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 15px;
  margin-top: 15px;
}

.event-card {
  display: flex;
  align-items: flex-start;
  background-color: white;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #ccc;
}

.event-card.sickness {
  border-left-color: #f44336;
}

.event-card.crime {
  border-left-color: #673ab7;
}

.event-card.research {
  border-left-color: #2196f3;
}

.event-card.famine {
  border-left-color: #ff9800;
}

.event-card.treasury {
  border-left-color: #ffc107;
}

.event-icon {
  font-size: 1.8rem;
  margin-right: 15px;
  flex-shrink: 0;
}

.event-content {
  flex: 1;
}

.event-cycle {
  font-size: 0.8rem;
  color: #666;
  margin-bottom: 5px;
}

.event-description {
  font-size: 0.95rem;
  line-height: 1.4;
}

.event-date {
  font-size: 0.8rem;
  color: #888;
  margin-top: 5px;
}

.events-list-full {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.events-list-full .event-card {
  margin-bottom: 10px;
}

/* Calendar Styles */
.calendar-info {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 20px;
  margin-bottom: 30px;
}

.calendar-current, .calendar-description {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.calendar-current h3, .calendar-description h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #444;
  font-size: 1.2rem;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.calendar-current-details {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.calendar-current-month, .calendar-current-year, .calendar-current-season {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.calendar-label {
  font-weight: bold;
  color: #666;
}

.calendar-value {
  font-size: 1.1rem;
}

.month-description-full {
  line-height: 1.6;
  color: #555;
  font-style: italic;
  background-color: #fff9c4;
  padding: 15px;
  border-radius: 4px;
  border-left: 4px solid #ffd600;
}

.calendar-seasons {
  margin-bottom: 30px;
}

.calendar-seasons h3, .calendar-months h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #444;
  font-size: 1.2rem;
}

.seasons-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.season-card {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s, box-shadow 0.3s;
  position: relative;
  overflow: hidden;
}

.season-card.active {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  transform: translateY(-5px);
  border: 2px solid #4CAF50;
}

.season-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
}

.season-card:nth-child(1)::before {
  background-color: #90caf9; /* Winter */
}

.season-card:nth-child(2)::before {
  background-color: #a5d6a7; /* Spring */
}

.season-card:nth-child(3)::before {
  background-color: #ffcc80; /* Summer */
}

.season-card:nth-child(4)::before {
  background-color: #ef9a9a; /* Autumn */
}

.season-icon {
  font-size: 2rem;
  margin-bottom: 10px;
}

.season-name {
  font-size: 1.3rem;
  font-weight: bold;
  margin-bottom: 10px;
}

.season-months {
  color: #666;
  margin-bottom: 10px;
}

.season-factor {
  font-weight: bold;
  margin-bottom: 10px;
}

.season-description {
  font-size: 0.9rem;
  color: #777;
  line-height: 1.4;
}

.months-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 15px;
}

.month-card {
  background-color: white;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  text-align: center;
  transition: transform 0.3s, box-shadow 0.3s;
  cursor: pointer;
}

.month-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.month-card.active {
  border: 2px solid #4CAF50;
  transform: translateY(-3px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.month-card.hiver {
  border-top: 4px solid #90caf9;
}

.month-card.printemps {
  border-top: 4px solid #a5d6a7;
}

.month-card.été {
  border-top: 4px solid #ffcc80;
}

.month-card.automne {
  border-top: 4px solid #ef9a9a;
}

.month-icon {
  font-size: 1.8rem;
  margin-bottom: 10px;
}

.month-name {
  font-weight: bold;
  margin-bottom: 5px;
}

.month-season {
  font-size: 0.8rem;
  color: #777;
}

/* Jobs Tab Styles */
.jobs-summary {
  display: flex;
  justify-content: space-between;
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.jobs-summary-item {
  text-align: center;
  flex: 1;
}

.jobs-summary-label {
  font-weight: bold;
  color: #666;
  margin-bottom: 5px;
}

.jobs-summary-value {
  font-size: 1.5rem;
  color: #333;
}

.jobs-management {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 20px;
}

.jobs-table-container {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.jobs-table {
  width: 100%;
  border-collapse: collapse;
}

.jobs-table th {
  text-align: left;
  padding: 12px 15px;
  border-bottom: 2px solid #eee;
  font-weight: bold;
  color: #555;
}

.jobs-table td {
  padding: 12px 15px;
  border-bottom: 1px solid #eee;
}

.worker-row {
  background-color: #f9f9f9;
  font-weight: bold;
}

.job-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.job-actions {
  display: flex;
  align-items: center;
  gap: 5px;
}

.job-action-buttons {
  display: inline-flex;
  margin-left: 10px;
  gap: 5px;
}

.job-action-btn {
  width: 30px;
  height: 30px;
  border: none;
  border-radius: 4px;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.2s;
}

.job-action-btn.increase {
  background-color: #4CAF50;
  color: white;
}

.job-action-btn.increase.shift {
  background-color: #388E3C; /* Darker green */
  color: white;
  font-size: 0.8rem;
}

.job-action-btn.decrease {
  background-color: #f44336;
  color: white;
}

.job-action-btn.decrease.shift {
  background-color: #D32F2F; /* Darker red */
  color: white;
  font-size: 0.8rem;
}

.job-action-btn:hover {
  opacity: 0.9;
}

.job-action-btn:disabled {
  background-color: #ccc !important;
  cursor: not-allowed;
  opacity: 0.7;
}

.job-action-btn.increase.shift:disabled {
  background-color: #ccc !important;
  cursor: not-allowed;
  opacity: 0.7;
}

.job-action-btn.decrease.shift:disabled {
  background-color: #ccc !important;
  cursor: not-allowed;
  opacity: 0.7;
}

.jobs-info {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.jobs-info-card, .jobs-actions-card {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.jobs-info-card h3, .jobs-actions-card h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #444;
  font-size: 1.2rem;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.jobs-info-card p {
  margin-bottom: 10px;
  line-height: 1.5;
  color: #666;
}

.jobs-action-btn {
  display: block;
  width: 100%;
  padding: 10px;
  margin-bottom: 10px;
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.2s;
}

.jobs-action-btn:hover {
  background-color: #45a049;
}

.next-cycle-btn {
  padding: 12px 24px;
  font-size: 1.2rem;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
  display: block;
  margin-left: auto;
  margin-top: 20px;
}

.next-cycle-btn:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.next-cycle-btn:hover:not(:disabled) {
  background-color: var(--primary-hover);
}

.tab-content h2 {
  font-size: 1.8rem;
  margin-bottom: 20px;
  color: #333;
  padding-bottom: 10px;
  font-weight: 500;
}

.jobs-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 10px;
}

.jobs-table th,
.jobs-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

.jobs-table th {
  background-color: var(--secondary-color);
  font-weight: bold;
}

.jobs-table tr:hover {
  background-color: #f9f9f9;
}

.tab-content {
  display: none;
  padding: 20px;
}

.tab-content.active {
  display: block;
}

/* Food Tab Styles */
.food-summary {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.food-summary-item {
  text-align: center;
  flex: 1;
  min-width: 150px;
  margin: 10px;
}

.food-summary-label {
  font-weight: bold;
  color: #666;
  margin-bottom: 5px;
}

.food-summary-value {
  font-size: 1.5rem;
  color: #333;
}

.tabs-container {
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.tabs-header {
  display: flex;
  background-color: #f5f5f5;
  border-bottom: 1px solid #ddd;
}

.tab {
  padding: 15px 20px;
  cursor: pointer;
  transition: background-color 0.3s;
  font-weight: 500;
}

.tab:hover {
  background-color: #e9e9e9;
}

.tab.active {
  background-color: white;
  border-bottom: 3px solid var(--primary-color);
}

.table-summary {
  margin-bottom: 20px;
}

.table-summary h3 {
  margin-top: 0;
  margin-bottom: 10px;
  color: #333;
}

.table-summary p {
  color: #666;
  margin-bottom: 15px;
}

.table-total {
  font-weight: bold;
  font-size: 1.1rem;
  color: #333;
  margin-bottom: 15px;
}

.modifiers-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 20px;
}

.modifiers-table th,
.modifiers-table td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid #ddd;
}

.modifiers-table th {
  background-color: #f5f5f5;
  font-weight: 600;
}

.modifiers-table tr:hover {
  background-color: #f9f9f9;
}

.delete-btn {
  background-color: #f44336;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.delete-btn:hover {
  background-color: #d32f2f;
}

.add-modifier-form {
  background-color: #f9f9f9;
  padding: 20px;
  border-radius: 8px;
  margin-top: 20px;
}

.add-modifier-form h4 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #333;
}

.form-row {
  display: flex;
  gap: 20px;
  margin-bottom: 15px;
}

.form-group {
  flex: 1;
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #555;
}

.form-group input {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
}

.add-btn {
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
  font-size: 1rem;
}

.add-btn:hover {
  background-color: var(--primary-hover);
}

.edit-btn {
  background-color: #2196F3;
  color: white;
  border: none;
  padding: 6px 10px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
  margin-right: 5px;
  font-size: 0.9rem;
}

.edit-btn:hover {
  background-color: #0b7dda;
}

.save-btn {
  background-color: #4CAF50;
  color: white;
  border: none;
  padding: 6px 10px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
  margin-right: 5px;
  font-size: 0.9rem;
}

.save-btn:hover {
  background-color: #45a049;
}

.cancel-btn {
  background-color: #9e9e9e;
  color: white;
  border: none;
  padding: 6px 10px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
  font-size: 0.9rem;
}

.cancel-btn:hover {
  background-color: #757575;
}

.positive {
  color: #4CAF50;
}

.negative {
  color: #f44336;
}

.section {
  margin-bottom: 30px;
}
