You are tasked to do a code refactoring. Instructions are as follows:


# Développement d'une application web de simulation de gestion de mine

## Objectif
Développer une application web complète qui reproduit le fonctionnement d'un simulateur de gestion de mine actuellement implémenté en Google Sheets, en le transformant en une interface interactive autonome de type jeu de gestion.

## Livrables attendus
- Code source complet et fonctionnel en une seule livraison (approche monolithique)
- Architecture complète (backend Node.js/Express, frontend HTML/CSS/JS, base de données SQLite)
- Documentation d'installation et d'utilisation
- Interface utilisateur interactive et moderne

## Spécifications techniques
- Backend: Node.js avec Express (version la plus récente stable)
- Base de données: SQLite pour la persistence des données
- Frontend: React.js (v18+) avec une architecture par composants
- API RESTful entre le backend et le frontend
- État global géré avec React Context API ou Redux pour les valeurs interdépendantes
- Utilisation de bibliothèques de visualisation compatibles React (Recharts, Material-UI, etc.)

## Interface utilisateur
L'interface ne doit pas ressembler à un tableur mais plutôt à une application de gestion avec:
- Dashboard principal avec jauges, compteurs et indicateurs visuels
- Navigation par onglets latéraux pour accéder aux différentes sections
- Tableaux interactifs permettant l'ajout/suppression/modification de données
- Visualisation claire des ressources, population et états
- Bouton "Cycle suivant" proéminent pour avancer dans la simulation

## Fonctionnalités des tableaux de modificateurs
- Chaque entrée dans un tableau doit avoir un ID unique (non visible par l'utilisateur)
- Interface pour ajouter une nouvelle entrée (bouton "Ajouter" avec formulaire)
- Possibilité de supprimer une entrée existante
- Édition directe des valeurs (intitulé, effet, description)
- Mise à jour instantanée des totaux et des valeurs dépendantes lors des modifications

## Système de réactivité
- Lorsqu'une valeur est modifiée dans l'interface (ex: nombre de Workers), toutes les valeurs qui en dépendent doivent être automatiquement recalculées et affichées
- Les modifications ne doivent pas affecter les réserves (or, nourriture, etc.) jusqu'à ce que le bouton "Cycle suivant" soit activé
- Le système doit tracer un graphe de dépendances entre les variables pour optimiser les recalculs

***Onglets de l'application (chacun correspond à une feuille de calcul du fichier d'origine) :
Dashboard
Nourriture
Minage
Charges
Moral
Matériaux
Santé
Construction
Recherche
Renommée
Commerce
Justice et défense
Liste des bâtiments
Plans des bâtiments

***Explications des tabs
Le Dashboard est l'écran principal avec toutes les valeurs et la plupart des calculs
De nourriture à Liste des bâtiments, il s'agit de listes de modificateurs qui s'ajouteront au fur et à mesure du développement du jeu. 

**Nourriture
Une table "Effets généraux" avec des colonnes : intitulé, effet (%), description. Il y a au sommet une variable "Somme des modificateurs généraux" (FoodOtherMultiplier)  qui équivaut au total de tous les effets de cette table.

Une table "Technologies qui affectent la production de nourriture" avec des colonnes : intitulé, effet (%), description. Il y a au sommet une variable "Somme des modificateurs technologiques"(FoodTechMultiplier) qui équivaut au total de tous les effets de cette table.

Une table "Péremption" avec des colonnes : intitulé, effet (%), description. Il y a au sommet une variable "Péremption" (perempvaluecell). Il y a nativement une valeur "Base" (intitulé), "0.2" (effet), "Péremption naturelle" (Description) dans le tableau. Ainsi, perempvaluecell = total de tous les effets de cette table.

Tu as compris le fonctionnement et ce sera comme ça pour quasiment tout le reste. Des tables dans lesquels on ajoute des entrées pour des modificateurs. Ces modificateurs ne sont pas hardcodés mais peuvent être ajoutés lorsqu'on joue. Tu peux mettre un bouton à chaque table pour ajouter de nouvelles entrées.

**Minage
Table "Effets généraux" (MineProfitability) dont la valeur = total de tous les effets de cette table (mais au minimum 0).

Table "Ingénierie" (EngineeringTechMultiplier) dont la valeur = total de tous les effets de cette table.

Table "Technologies qui affectent la mine" (MineTechMultiplier). Même logique.

**Charges
Table "Coûts généraux" (ChargesPerCycle) dont la valeur = total de tous les effets de cette table (mais au minimum 0).

Table "Modificateurs généraux" (ChargesGlobalModifier). toujours la même logique.

Table "Technologies qui affectent les charges" (ChargesTechModifier). Pareil

**Moral
Table "Effets moral" (MoralGlobalValue). Toujours la même logique.
Attention cependant, cette a spécificités. Il faut une colonne en plus nommée "Début de l'effet" qui permet d'ajouter du texte.
Il faut ajouter un second tableau qui calcule des effets permanents relatifs à la population. Un tableau "Logement" dont la variable numérique dépend de la formule "=IF(DwellingsAvailable<0; DwellingsAvailable*10;0)" et un tableau "Population" dont la variable numérique dépend de la formule "=MIN(0;150+(-5*TotalInhabitants))". Les variables mentionnées sont expliquées plus bas. La valeur de MoralGlobalValue additionne les résultats des deux formules ci-dessus en plus d'additionner les effets de son tableau.

**Matériaux
Table "Effets généraux" (MaterialsOtherMultiplier). Pareil que plus haut.
Table "Technologies qui affectent la génération de matériaux" (MaterialsTechMultiplier). Pareil que plus haut.

**Health
Table "Efficacité de la santé" (TotalHealthModifier). Pareil que plus haut.

**Construction
Table "Effets généraux" (EffortGlobalMultiplier). Pareil que plus haut.
Table "Technologie qui affecte la construction de bâtiments (EffortTechMultiplier). Pareil que plus haut.

**Recherche
Table "Effets sur la recherche" (TotalResearchModifier). Pareil que plus haut.

**Renommée
Table "Effets sur la renommée" (TotalRenown). Pareil que plus haut.

**Commerce
Table "Effets sur le commerce" (TotalTradingModifier). Pareil que plus haut.

**Justice Et défense
Table "Efficacité de la justice" (TotalArmyMultiplier). Pareil que plus haut.
Table "Armée" (TotalArmyMultiplier). Pareil que plus haut.
Table "Moyens de défense". Pas de variable, simplement des entrées de texte.

**Liste des bâtiments
Table "Liste des bâtiments construits". Chaque bâtiment peut avoir une valeur de revenus et d'habitations. Cet onglet a deux variables supplémentaires à afficher et à calculer : BuildingsRevenues (somme des valeurs des revenus des bâtiments de la table) et TotalDwellings (Pareil pour les habitations).

**Plans des bâtiments
Prépare l'onglet mais laisse-le vide pour l'instant.

***Détails du Dashboard
Le dashboard affiche toutes les informations essentielles de la colonie. 

Il y a 9 "jobs" différents pour la population. Scholar, Healer, Worker, Miner, Protector, Craftsman, Farmer, Engineer, Soldier.

Chacun de ces jobs ont des variables semblables. Pour scholar: ScholarNumber, ScholarSalary, FreeScholar, ScholarSick, ScholarTotalSalary

ScholarTotalSalary =(ScholarNumber-FreeScholar)*ScholarSalary. Les autres ont la même liste de variables mais pour eux (HealerNumber, HealerSalary, etc.)

Salaires: Scholar (60), Healer (40), Worker (20), Miner (20), Protector (50), Craftsman (20), Farmer (20), Engineer (50), Soldier (30).

Chacun de ces jobs ont des effets différents dans la simulation.

***Variables globales
Je te donne le nom de l'UI puis la variable et sa formule.

Habitants actifs : ActiveInhabitants=InhabitantsToPay-ScholarSick-WorkerSick-ProtectorSick-MinerSick-CraftsmanSick-FarmerSick-EngineerSick-SoldierSick-HealerSick

Habitants à payer : InhabitantsToPay=ScholarNumber+HealerNumber+WorkerNumber+MinerNumber+ProtectorNumber+CraftsmanNumber+FarmerNumber+EngineerNumber+SoldierNumber-FreeMiner-FreeFarmer-FreeHealer-FreeWorker-FreeScholar-FreeSoldier-FreeEngineer-FreeCraftsman-FreeProtector

Autres habitants : OtherInhabitants (modifié à la main)

Personnages joueurs : PCNumber (modifié à la main)

Numéro du cycle : CycleNumber
Mois : MonthCell
Année : YearCell
Saison : SeasonCell

Réserves Matériaux : ressmatCell
Matériaux par cycle : prodmatCell=value((WorkerNumber-WorkerSick)*MaterialsPerWorker*(1+MaterialsOtherMultiplier+MaterialsTechMultiplier+MoralMultiplier)+(MinerNumber-MinerSick)*MaterialsPerMiner*(1+MaterialsOtherMultiplier+MaterialsTechMultiplier+MoralMultiplier))
Efforts par cycle: constrcycle=ActualWorkerNumber*EffortPerWorker*(1+EffortTechMultiplier+MoralMultiplier+EffortGlobalMultiplier)
Profondeur de la mine : minedepthcell
Habitations : Dwellings
Habitations libres : DwellingsAvailable=Dwellings-TotalInhabitants
Total d'habitants : TotalInhabitants=InhabitantsToPay+PCNumber+OtherInhabitants
Renommée : TotalRenown

Revenus du commerce : revenusCommerceCell=(CraftsmanNumber-CraftsmanSick)*(MAX(0;TotalTradingModifier))*TradingValue
Revenus de la mine : prodmineCell=VALUE(MinerNumber-MinerSick)*MiningValue*(MineProfitability+MineTechMultiplier+EngineeringMultiplier+MoralMultiplier)
Revenus des bâtiments : BuildingsRevenues
Revenus totaux totalRevenusCell=revenusCommerceCell+prodmineCell+BuildingsRevenues

Réserves d'or : treasureCell
Salaires au total : salairesCell=ScholarTotalSalary+HealerTotalSalary+WorkerTotalSalary+MinerTotalSalary+ProtectorTotalSalary+CraftsmanTotalSalary+FarmerTotalSalary+EngineerTotalSalary+SoldierTotalSalary

Charges non salariales : NonSalaryChargesCell=value((FixedCharges*(1+ChargesGlobalModifier)/(1+CraftsmanEffect*(ChargesTechModifier+MoralMultiplier))))

Charges totales : totalchargesCell=salairesCell+NonSalaryChargesCell

Réserves de vivres : ressvivrescell
Tendance des vivres : soldeVivresCell=prodvivresCell-consomVivresCell

Probabilité de crime : crimeprobaCell=MAX(0;0,15/(1+EXP(-0,1*(TotalInhabitants-15*(ProtectorNumber-ProtectorSick)*(1+TotalJusticeModifier)-(2,5*(SoldierNumber-SoldierSick)*(1+TotalJusticeModifier))))))

Force de l'armée : ArmyStrength=((SoldierNumber-SoldierSick)*0,5*(1+TotalArmyMultiplier))+((InhabitantsToPay-ScholarSick-WorkerSick-ProtectorSick-MinerSick-CraftsmanSick-FarmerSick-EngineerSick-HealerSick-SoldierNumber)*0,125)

Modificateur de moral : modmoralCell=(2*(NORMDIST(MoralGlobalValue;1000;1000;TRUE)))-1

Nourriture par Fermier : FoodPerFarmer=4

Facteur Saisonnier : SeasonFactor

Nourriture par cycle : prodvivresCell=value(FarmerNumber-FarmerSick)*FoodPerFarmer*SeasonFactor*(1+FoodOtherMultiplier+FoodTechMultiplier+MoralMultiplier)

Consommation par cycle : consomVivresCell=TotalInhabitants

Revenus d'un mineur : MiningValue = 30

Revenu d'un artisan : TradingValue = 25

Multiplicateur d'ingénierie : EngineeringMultiplier=EngineerNumber*0,03*(1+SUM(EngineeringTechMultiplier))

Charges fixes : FixedCharges=MAX(0;ChargesPerCycle)

Travail des artisans : CraftsmanEffect=MIN(2;1+(0,05*(CraftsmanNumber-CraftsmanSick)))

Total de Modification des charges : ModCharges=1/(ChargesGlobalModifier+(CraftsmanEffect*(1+ChargesTechModifier+MoralMultiplier)))

Travailleurs totaux : ActualWorkerNumber=WorkerNumber-WorkerSick+PCNumber-FreeWorker

Effort par travailleur : EffortPerWorker=10

Variable niveau technologique (à incrémenter à la main)

Variable Probabilité de Eureka
researchprobaCell=MAX(0,5/(1+EXP(-0,33*(ScholarNumber-ScholarSick)*(1+0,05*TechLevel*TotalResearchModifier)))-(0,6/(1+EXP(0,2*TechLevel)));0)+0,01

sickprobaCell=MAX(0;0,25/(1+EXP(-0,1*(TotalInhabitants-18*HealerNumber*(1+TotalHealthModifier)))))

-------------

AppScript.js

function MineCycleRefac() {
  var sheet = SpreadsheetApp.getActiveSpreadsheet().getActiveSheet();

  // Calcul du cycle
  var cycle = sheet.getRange('CycleNumber');
  var cycleNbr = cycle.getValue() + 1;
  cycle.setValue(cycleNbr);

  // Accidents et santé
  var sickProba = sheet.getRange('sickprobaCell').getValue();
  Logger.log('sickProba: ' + sickProba);
  var populationRanges = ['ScholarNumber', 'HealerNumber', 'WorkerNumber', 'MinerNumber', 'ProtectorNumber', 'CraftsmanNumber', 'FarmerNumber', 'EngineerNumber', 'SoldierNumber'];
  var sickRanges = ['ScholarSick', 'HealerSick', 'WorkerSick', 'MinerSick', 'ProtectorSick', 'CraftsmanSick', 'FarmerSick', 'EngineerSick', 'SoldierSick'];

  for (var i = 0; i < populationRanges.length; i++) {
    var population = sheet.getRange(populationRanges[i]).getValue();
    var occurrences = 0;
    for (var j = 0; j < population; j++) {
      if (Math.random() < sickProba) {
        occurrences++;
      }
    }
    sheet.getRange(sickRanges[i]).setValue(occurrences);
  }

  // Crime
  var crimeEventProba = sheet.getRange('crimeprobaCell').getValue();
  Logger.log('crimeEventProba: ' + crimeEventProba);
  var crimeBox = sheet.getRange('crimeboxcell');

  if (Math.random() < crimeEventProba) {
    crimeBox.setValue("Un crime a été commis !");
  } else {
    crimeBox.setValue("Nos galeries sont calmes.");
  }

  var soldeVivres = sheet.getRange('soldeVivresCell').getValue();
  var perempValue = sheet.getRange('perempvaluecell').getValue();
  var resVivresRange = sheet.getRange('ressvivrescell');
  var famineBox = sheet.getRange('foodboxcell');

  var resVivres = resVivresRange.getValue();
  var newValueVivres = (resVivres + soldeVivres) * (1 - perempValue);

  if (newValueVivres < 0) {
    resVivresRange.setValue(0);
    famineBox.setValue("La famine vous frappe !");
  } else {
    resVivresRange.setValue(newValueVivres);
    famineBox.setValue("Vous avez assez à manger.");
  }

  // Pour l'argent
  var prodMine = sheet.getRange('prodmineCell').getValue();
  var totalRevenus = sheet.getRange('totalRevenusCell').getValue();
  var charges = sheet.getRange('totalchargesCell').getValue();
  var resTreasureRange = sheet.getRange('treasureCell');
  
  var newTreasureValue = resTreasureRange.getValue() + totalRevenus - charges;

  // Mettre à jour la valeur du trésor
  resTreasureRange.setValue(newTreasureValue);

  // Journaux de débogage
  Logger.log('prodMine: ' + prodMine);
  Logger.log('totalRevenus: ' + totalRevenus);
  Logger.log('charges: ' + charges);
  Logger.log('newTreasureValue: ' + newTreasureValue);

  // Materiel de construction
  var prodMat = sheet.getRange('prodmatCell').getValue();
  var resMat = sheet.getRange('ressmatCell');
  resMat.setValue(resMat.getValue() + prodMat);

  // Profondeur de la mine
  var totalProdMine = sheet.getRange('totalProdMineCell');
  var leTotalProdMineValeur = totalProdMine.getValue() + prodMine;
  totalProdMine.setValue(leTotalProdMineValeur);
  sheet.getRange('minedepthcell').setValue(Math.floor(leTotalProdMineValeur / 1000));

  // Pour le moral
  var moralValue = sheet.getRange('modmoralCell').getValue();
  var moralTitre = sheet.getRange('moraltitreCell');

  Logger.log('moralValue: ' + moralValue);

  if (moralValue <= 0.2) {
    moralTitre.setValue('Rébellion');
  } else if (moralValue <= 0.4) {
    moralTitre.setValue('En colère');
  } else if (moralValue <= 0.65) {
    moralTitre.setValue('Triste');
  } else if (moralValue <= 0.90) {
    moralTitre.setValue('Mécontent');
  } else if (moralValue <= 1.10) {
    moralTitre.setValue('Neutre');
  } else if (moralValue <= 1.35) {
    moralTitre.setValue('Content');
  } else if (moralValue <= 1.60) {
    moralTitre.setValue('Epanoui');
  } else if (moralValue <= 1.80) {
    moralTitre.setValue('Heureux');
  } else {
    moralTitre.setValue('Ecstatique');
  }

  // MOIS DE L'ANNEE
  var moisDeLAnnee = [{ mois: "Rubis", description: "Rubis (janvier) - Le rubis, pierre précieuse rouge vif, symbolise la passion, la force et la vitalité. En janvier, on célèbre ces qualités en se préparant pour une nouvelle année pleine de défis."}, { mois: "Saphir", description: "Saphir (février) - Le saphir, pierre précieuse bleue, est associé à la sagesse, à la clarté d'esprit et à la protection. En février, on se concentre sur la réflexion et la planification stratégique pour les mois à venir."}, { mois: "Émeraude", description: "Émeraude (mars) - L'émeraude, pierre précieuse verte, représente la croissance, la fertilité et la vitalité. En mars, on célèbre le renouveau de la nature et on se prépare à semer les graines des projets futurs."}, { mois: "Diamant", description: "Diamant (avril) - Le diamant, pierre précieuse transparente et étincelante, symbolise la pureté, la résilience et l'invincibilité. En avril, on célèbre sa force intérieure et sa capacité à surmonter les défis avec grâce et détermination. C'est aussi le mois durant lequel on déterre la hache de guerre."}, { mois: "Améthyste", description: "Améthyste (mai) - L'améthyste, pierre précieuse violet profond, est associée à la spiritualité, à la paix intérieure et à la sagesse. En mai, on se tourne vers l'introspection et la méditation, on cherche à se connecter avec son moi intérieur et à trouver l'harmonie."}, { mois: "Topaze", description: "Topaze (juin) - La topaze, pierre précieuse jaune ou orange, symbolise la créativité, la passion et l'inspiration. En juin, on met l'accent sur l'expression artistique et l'innovation, on cherche à donner vie à de nouvelles idées et à explorer de nouveaux horizons."}, { mois: "Sardoine", description: "Sardoine (juillet) - La sardoine, pierre précieuse rouge ou brun rougeâtre, représente la force physique, le courage et la détermination. En juillet, on se concentre sur l'entraînement et la préparation physique. C'est aussi une période de récoltes importantes."}, { mois: "Perle", description: "Perle (août) - La perle, une gemme formée dans les coquillages, symbolise la pureté, la beauté et la perfection. En août, on célèbre la beauté de la pierre et la perfection de l'univers, se reconnectant avec les éléments qui nous entourent."}, { mois: "Opale", description: "Opale (septembre) - L'opale, pierre précieuse aux reflets multicolores, est associée aux arcanes, au mystère et à la transformation. En septembre, on explore les mystères de l'univers et on cherche à comprendre les secrets cachés derrière les voiles de la réalité."}, { mois: "Aventurine", description: "Aventurine (octobre) - L'aventurine, pierre précieuse verte avec des inclusions scintillantes, symbolise la chance, la prospérité et l'abondance. En octobre, on célèbre la chance et la fortune, se lançant dans de nouvelles aventures et cherchant à atteindre de nouvelles profondeurs."}, { mois: "Grenat", description: "Grenat (novembre) - Le grenat, pierre précieuse rouge foncé à rouge orangé, représente la passion, l'énergie et la détermination. En novembre, on se concentre sur nos objectifs avec une énergie renouvelée, travaillant sans relâche pour atteindre nos rêves les plus chers."}, { mois: "Tanzanite", description: "Tanzanite (décembre) - La tanzanite, pierre précieuse bleu-violet rare, symbolise la spiritualité, la paix intérieure et la connexion avec le divin. En décembre, on se réunit pour célébrer notre spiritualité et notre connexion avec les forces supérieures, se préparant à accueillir une nouvelle année pleine de promesses et de possibilités. On en profite pour faire le point avec le reste du clan. Idéalement, on remet en questions nos rancunes et on enterre la hache de guerre, du moins jusqu'au mois de Diamant."}];

  var mois = sheet.getRange('MonthCell');
  var annee = sheet.getRange('YearCell');
  var season = sheet.getRange('SeasonCell');
  var valMois = sheet.getRange('TotalMonthCell');
  var moisDescription = sheet.getRange('MonthDescription');

  var indexMois = valMois.getValue();
  var nombreIterations =  annee.getValue() - 1326;

  indexMois++;

  // Vérifier si on a parcouru toute la liste
  if (indexMois >= moisDeLAnnee.length) {
    // Réinitialiser l'index du mois
    indexMois = 0;
    
    // Incrémenter le nombre d'itérations
    nombreIterations++;
    annee.setValue(1326 + nombreIterations);
  }

  mois.setValue(moisDeLAnnee[indexMois].mois);
  moisDescription.setValue(moisDeLAnnee[indexMois].description);
  valMois.setValue(indexMois);

  if (indexMois < 3){
    season.setValue("Hiver");
    sheet.getRange('SeasonFactor').setValue(0.5);
  } else if (indexMois >= 3 && indexMois <= 5){
    season.setValue("Printemps");
    sheet.getRange('SeasonFactor').setValue(1);
  } else if (indexMois >= 6 && indexMois <= 8){
    season.setValue("Été");
    sheet.getRange('SeasonFactor').setValue(1.25);
  } else if (indexMois >= 9 && indexMois <= 11){
    season.setValue("Automne");
    sheet.getRange('SeasonFactor').setValue(1);
  }

  // Evénements
  var researchEventProba = sheet.getRange('researchprobaCell').getValue();
  var eventBox = sheet.getRange('researchboxCell');

  Logger.log('researchEventProba: ' + researchEventProba);

  if (Math.random() < researchEventProba) {
    eventBox.setValue("EUREKA! Une nouvelle technologie a été découverte!");
  } else {
    eventBox.setValue("Aucune découverte durant ce cycle.");
  }
}


Instructions for the output format:
- Output code without descriptions, unless it is important.
- Minimize prose, comments and empty lines.
- Only show the relevant code that needs to be modified. Use comments to represent the parts that are not modified.
- Make it easy to copy and paste.
- Consider other possibilities to achieve the result, do not be limited by the prompt.