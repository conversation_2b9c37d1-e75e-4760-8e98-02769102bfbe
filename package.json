{"name": "mine-simulator", "version": "1.0.0", "main": "index.js", "scripts": {"test": "concurrently \"npm run test:client\" \"npm run test:server\"", "test:client": "cd client && npm test", "test:server": "cd server && npm test", "test:coverage": "concurrently \"npm run test:client:coverage\" \"npm run test:server:coverage\"", "test:client:coverage": "cd client && npm test -- --coverage", "test:server:coverage": "cd server && npm test -- --coverage", "start": "node server/index.js", "server": "nodemon server/index.js", "client": "cd client && npm start", "dev": "concurrently \"npm run server\" \"npm run client\""}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "morgan": "^1.10.0", "sqlite": "^5.1.1", "sqlite3": "^5.1.7"}, "devDependencies": {"concurrently": "^9.1.2", "nodemon": "^3.1.10"}, "description": ""}