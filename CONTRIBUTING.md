# Guide de contribution

Merci de votre intérêt pour contribuer au projet Mine Simulator ! Voici quelques lignes directrices pour vous aider à contribuer efficacement.

## Comment contribuer

1. **Forker** le dépôt
2. **Cloner** votre fork
3. **Créer une branche** pour votre fonctionnalité ou correction
4. **Développer** votre fonctionnalité ou correction
5. **Tester** vos modifications
6. **Soumettre** une pull request

## Standards de code

- Utilisez des noms de variables et de fonctions descriptifs
- Commentez votre code lorsque nécessaire
- Suivez les conventions de style existantes
- Écrivez des tests pour les nouvelles fonctionnalités

## Tests

Assurez-vous que tous les tests passent avant de soumettre une pull request :

```bash
# Tests backend
cd server
npm test

# Tests frontend
cd client
npm test
```

## Signaler des bugs

Si vous trouvez un bug, veuille<PERSON> créer une issue en incluant :

- Une description claire du bug
- Les étapes pour reproduire le problème
- Le comportement attendu
- Des captures d'écran si applicable

## Proposer des fonctionnalités

Les suggestions de fonctionnalités sont les bienvenues ! Créez une issue avec :

- Une description claire de la fonctionnalité
- Les avantages qu'elle apporterait
- Des exemples d'utilisation si possible

## Processus de pull request

1. Mettez à jour votre fork avec la dernière version de la branche principale
2. Créez une branche pour votre fonctionnalité
3. Faites vos modifications
4. Testez vos modifications
5. Soumettez une pull request avec une description détaillée

Merci de contribuer à Mine Simulator !
