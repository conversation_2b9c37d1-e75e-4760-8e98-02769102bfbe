const db = require('../db');

/**
 * Get all charges-related data
 */
async function getChargesData() {
    try {
        // Get all charges-related modifier tables
        const chargesGeneralTable = await db.get('SELECT * FROM modifier_tables WHERE id = 7');
        const chargesGlobalTable = await db.get('SELECT * FROM modifier_tables WHERE id = 8');

        // Get modifiers for each table
        const chargesGeneralModifiers = await db.all('SELECT * FROM modifiers WHERE table_id = 7 ORDER BY id');
        const chargesGlobalModifiers = await db.all('SELECT * FROM modifiers WHERE table_id = 8 ORDER BY id');

        // Get base charges from config
        const baseCharges = await db.get('SELECT * FROM game_config WHERE name = "BASE_CHARGES"');

        // Get craftsman data
        const craftsman = await db.get('SELECT * FROM jobs WHERE name = "Craftsman"');

        // Get all jobs for salary calculation
        const jobs = await db.all('SELECT * FROM jobs');

        // Calculate total salaries
        const totalSalaries = jobs.reduce((sum, job) => sum + ((job.number - (job.free || 0)) * job.salary), 0);

        // Update the tables' current_value if needed
        await updateChargesModifiers();

        // Get updated tables
        const updatedChargesGeneralTable = await db.get('SELECT * FROM modifier_tables WHERE id = 7');
        const updatedChargesGlobalTable = await db.get('SELECT * FROM modifier_tables WHERE id = 8');

        // Return all data
        return {
            chargesData: [
                { table: updatedChargesGeneralTable, modifiers: chargesGeneralModifiers },
                { table: updatedChargesGlobalTable, modifiers: chargesGlobalModifiers }
            ],
            stats: {
                baseCharges: baseCharges?.value || 50,
                craftsman: craftsman || { number: 0, sick: 0, free: 0 },
                totalSalaries: totalSalaries
            }
        };
    } catch (error) {
        console.error('Error in getChargesData:', error);
        throw error;
    }
}

/**
 * Update all charges-related modifier tables
 */
async function updateChargesModifiers() {
    try {
        // Update each table's current_value
        for (const tableId of [7, 8]) {
            await db.run(`
                UPDATE modifier_tables
                SET current_value = (
                    SELECT SUM(effect)
                    FROM modifiers
                    WHERE table_id = ?
                ),
                updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            `, [tableId, tableId]);
        }
    } catch (error) {
        console.error('Error updating charges modifiers:', error);
        throw error;
    }
}

/**
 * Calculate charges based on game state
 */
async function calculateCharges(gameState, jobs) {
    try {
        // Get base charges from config
        const baseChargesConfig = await db.get('SELECT * FROM game_config WHERE name = "BASE_CHARGES"');
        const baseCharges = baseChargesConfig?.value || 50;

        // Get modifiers from tables
        const chargesGeneralTable = await db.get('SELECT * FROM modifier_tables WHERE id = 7');
        const chargesGlobalTable = await db.get('SELECT * FROM modifier_tables WHERE id = 8');

        // Calculate craftsman effect - 3% par artisan, effet max de 2 (divise les coûts par 2)
        const craftsman = jobs.find(j => j.name === 'Craftsman') || { number: 0, sick: 0, free: 0 };
        const activeCraftsmen = craftsman.number - craftsman.sick;
        const craftsmanEffect = Math.min(1, (0.03 * activeCraftsmen)); // Max 1 (ce qui donne un diviseur de 2 dans la formule)

        // Calculate salaries
        const salaries = jobs.reduce((sum, job) => sum + ((job.number - (job.free || 0)) * job.salary), 0);

        // Calculate non-salary charges
        // Formula simplifiée: FixedCharges * (1 + ChargesGlobalModifier) / (1 + CraftsmanEffect)
        // Note: chargesGeneralTable.current_value is now the sum of all costs in the general charges table
        const fixedCharges = chargesGeneralTable.current_value; // Use the sum directly, no need to add baseCharges

        // Nous n'utilisons plus la technologie ni le moral pour les charges
        // L'effet des artisans est limité à 1 (diviseur max de 2)
        const nonSalaryCharges = fixedCharges * (1 + chargesGlobalTable.current_value) / (1 + craftsmanEffect);

        // Total charges
        const totalCharges = salaries + nonSalaryCharges;

        return {
            salaries,
            nonSalaryCharges,
            totalCharges,
            craftsmanEffect,
            baseCharges: fixedCharges,
            modifiers: {
                chargesPerCycle: chargesGeneralTable.current_value,
                chargesGlobalModifier: chargesGlobalTable.current_value
            }
        };
    } catch (error) {
        console.error('Error calculating charges:', error);
        throw error;
    }
}

module.exports = {
    getChargesData,
    updateChargesModifiers,
    calculateCharges
};
