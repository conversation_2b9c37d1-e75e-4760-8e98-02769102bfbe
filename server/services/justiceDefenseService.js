const db = require('../db');
const { DEFAULT_VALUES } = require('../utils/constants');

class JusticeDefenseService {
    /**
     * Calculate crime probability based on inhabitants, protectors, soldiers and justice modifiers
     * @param {Object} gameState - Current game state
     * @param {Array} jobs - Array of jobs
     * @param {Array} modifierTables - Array of modifier tables
     * @returns {Number} - Crime probability value
     */
    static calculateCrimeProbability(gameState, jobs, modifierTables) {
        try {
            // Get total inhabitants
            const totalInhabitants = gameState.total_inhabitants || 0;

            // Get protectors
            const protectors = jobs.find(j => j.name === 'Protector') || { number: 0, sick: 0 };
            const activeProtectors = protectors.number - protectors.sick;

            // Get soldiers
            const soldiers = jobs.find(j => j.name === 'Soldier') || { number: 0, sick: 0 };
            const activeSoldiers = soldiers.number - soldiers.sick;

            // Get justice modifier value
            const justiceModifier = this.getModifierValue(modifierTables, 19); // Justice table ID = 19

            // Calculate crime probability using the formula:
            // MAX(0, 0.15 / (1 + EXP(-0.1 * (TotalInhabitants - (15 * activeProtectors * (1 + justiceModifier) - (2.5 * activeSoldiers * (1 + justiceModifier)))))))
            const term = totalInhabitants -
                        (15 * activeProtectors * (1 + justiceModifier) -
                         (2.5 * activeSoldiers * (1 + justiceModifier)));

            const crimeProbability = Math.max(0, 0.15 / (1 + Math.exp(-0.1 * term)));

            console.log('Crime probability calculation:', {
                totalInhabitants,
                activeProtectors,
                activeSoldiers,
                justiceModifier,
                term,
                result: crimeProbability
            });

            return crimeProbability;
        } catch (error) {
            console.error('Error calculating crime probability:', error);
            return 0; // Default to 0 if calculation fails
        }
    }

    /**
     * Calculate army strength based on soldiers and other inhabitants
     * @param {Object} gameState - Current game state
     * @param {Array} jobs - Array of jobs
     * @param {Array} modifierTables - Array of modifier tables
     * @returns {Number} - Army strength value
     */
    static calculateArmyStrength(gameState, jobs, modifierTables) {
        try {
            // Get inhabitants to pay
            const inhabitantsToPay = gameState.inhabitants_to_pay || 0;

            // Get soldiers
            const soldiers = jobs.find(j => j.name === 'Soldier') || { number: 0, sick: 0 };
            const activeSoldiers = soldiers.number - soldiers.sick;

            // Get sick people
            const scholarSick = (jobs.find(j => j.name === 'Scholar') || { sick: 0 }).sick;
            const workerSick = (jobs.find(j => j.name === 'Worker') || { sick: 0 }).sick;
            const protectorSick = (jobs.find(j => j.name === 'Protector') || { sick: 0 }).sick;
            const minerSick = (jobs.find(j => j.name === 'Miner') || { sick: 0 }).sick;
            const craftsmanSick = (jobs.find(j => j.name === 'Craftsman') || { sick: 0 }).sick;
            const farmerSick = (jobs.find(j => j.name === 'Farmer') || { sick: 0 }).sick;
            const engineerSick = (jobs.find(j => j.name === 'Engineer') || { sick: 0 }).sick;
            const healerSick = (jobs.find(j => j.name === 'Healer') || { sick: 0 }).sick;

            // Get army modifier value
            const armyModifier = this.getModifierValue(modifierTables, 20); // Army table ID = 20

            // Calculate army strength using the formula:
            // ((activeSoldiers * 0.5 * (1 + armyModifier)) + ((inhabitantsToPay - scholarSick - workerSick - protectorSick - minerSick - craftsmanSick - farmerSick - engineerSick - healerSick - soldiers.number) * 0.125))
            const soldierStrength = activeSoldiers * 0.5 * (1 + armyModifier);

            const otherInhabitants = inhabitantsToPay - scholarSick - workerSick -
                                    protectorSick - minerSick - craftsmanSick -
                                    farmerSick - engineerSick - healerSick - soldiers.number;

            const otherStrength = otherInhabitants * 0.125;

            const armyStrength = soldierStrength + otherStrength;

            console.log('Army strength calculation:', {
                inhabitantsToPay,
                activeSoldiers,
                armyModifier,
                soldierStrength,
                otherInhabitants,
                otherStrength,
                result: armyStrength
            });

            return armyStrength;
        } catch (error) {
            console.error('Error calculating army strength:', error);
            return 0; // Default to 0 if calculation fails
        }
    }

    /**
     * Calculate defense values per inhabitant type
     * @param {Array} modifierTables - Array of modifier tables
     * @returns {Object} - Defense values for citizens, guards and protectors
     */
    static calculateDefenseValues(modifierTables) {
        try {
            // Get army modifier value
            const armyModifier = this.getModifierValue(modifierTables, 20); // Army table ID = 20

            // Calculate defense values
            const citizenDefense = 0.125; // Base defense value for citizens
            const soldierDefense = 0.5 * (1 + armyModifier); // Defense value for soldiers
            const protectorDefense = 0.25 * (1 + armyModifier); // Defense value for protectors (estimated)

            return {
                citizenDefense,
                soldierDefense,
                protectorDefense
            };
        } catch (error) {
            console.error('Error calculating defense values:', error);
            return {
                citizenDefense: 0.125,
                soldierDefense: 0.5,
                protectorDefense: 0.25
            };
        }
    }

    /**
     * Get a modifier value from the modifier tables
     * @param {Array} modifierTables - Array of modifier tables
     * @param {Number} tableId - ID of the table to get the value from
     * @returns {Number} - Modifier value
     */
    static getModifierValue(modifierTables, tableId) {
        const table = modifierTables.find(t => t.id === tableId);
        return table ? table.current_value : 0;
    }

    /**
     * Get a configuration value
     * @param {String} name - Configuration name
     * @param {*} defaultValue - Default value if not found
     * @returns {*} - Configuration value
     */
    static getConfigValue(name, defaultValue) {
        // For now, return hardcoded values to avoid async issues
        return DEFAULT_VALUES[name] || defaultValue;
    }

    /**
     * Update a modifier table's current_value based on its modifiers
     * @param {Number} tableId - ID of the table to update
     * @returns {Promise<void>}
     */
    static async updateModifierTableSum(tableId) {
        try {
            // Get all modifiers for this table
            const modifiers = await db.all('SELECT * FROM modifiers WHERE table_id = ?', [tableId]);

            // Calculate the sum of all modifier effects
            const sum = modifiers.reduce((total, mod) => total + mod.effect, 0);

            // Update the table's current_value
            await db.run(
                'UPDATE modifier_tables SET current_value = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                [sum, tableId]
            );

            console.log(`Updated table ${tableId} current_value to ${sum}`);
        } catch (error) {
            console.error(`Error updating modifier table ${tableId}:`, error);
            throw error;
        }
    }

    /**
     * Update all justice and defense related modifier tables
     * @returns {Promise<void>}
     */
    static async updateJusticeDefenseModifiers() {
        try {
            // Update justice effects table
            await this.updateModifierTableSum(19); // Justice effects

            // Update army effects table
            await this.updateModifierTableSum(20); // Army effects
        } catch (error) {
            console.error('Error updating justice and defense modifiers:', error);
            throw error;
        }
    }

    /**
     * Get all defense means
     * @returns {Promise<Array>} - Array of defense means
     */
    static async getDefenseMeans() {
        try {
            // Get all defense means
            const defenseMeans = await db.all('SELECT * FROM defense_means ORDER BY id');
            return defenseMeans;
        } catch (error) {
            console.error('Error getting defense means:', error);
            throw error;
        }
    }

    /**
     * Add a new defense mean
     * @param {String} title - Title of the defense mean
     * @param {String} description - Description of the defense mean
     * @returns {Promise<Object>} - Newly created defense mean
     */
    static async addDefenseMean(title, description) {
        try {
            // Insert new defense mean
            const result = await db.run(
                'INSERT INTO defense_means (title, description) VALUES (?, ?)',
                [title, description]
            );

            // Get the newly created defense mean
            const defenseMean = await db.get('SELECT * FROM defense_means WHERE id = ?', [result.lastID]);
            return defenseMean;
        } catch (error) {
            console.error('Error adding defense mean:', error);
            throw error;
        }
    }

    /**
     * Update a defense mean
     * @param {Number} id - ID of the defense mean to update
     * @param {String} title - New title
     * @param {String} description - New description
     * @returns {Promise<Object>} - Updated defense mean
     */
    static async updateDefenseMean(id, title, description) {
        try {
            // Update defense mean
            await db.run(
                'UPDATE defense_means SET title = ?, description = ? WHERE id = ?',
                [title, description, id]
            );

            // Get the updated defense mean
            const defenseMean = await db.get('SELECT * FROM defense_means WHERE id = ?', [id]);
            return defenseMean;
        } catch (error) {
            console.error('Error updating defense mean:', error);
            throw error;
        }
    }

    /**
     * Delete a defense mean
     * @param {Number} id - ID of the defense mean to delete
     * @returns {Promise<void>}
     */
    static async deleteDefenseMean(id) {
        try {
            // Delete defense mean
            await db.run('DELETE FROM defense_means WHERE id = ?', [id]);
        } catch (error) {
            console.error('Error deleting defense mean:', error);
            throw error;
        }
    }

    /**
     * Get all justice and defense related data
     * @returns {Promise<Object>} - Justice and defense data
     */
    static async getJusticeDefenseData() {
        try {
            // Update all justice and defense related modifier tables first
            await this.updateJusticeDefenseModifiers();

            // Get justice effects table
            const justiceTable = await db.get('SELECT * FROM modifier_tables WHERE id = 19');

            // Get army effects table
            const armyTable = await db.get('SELECT * FROM modifier_tables WHERE id = 20');

            // Get modifiers for justice table
            const justiceModifiers = await db.all(
                'SELECT * FROM modifiers WHERE table_id = 19 ORDER BY id'
            );

            // Get modifiers for army table
            const armyModifiers = await db.all(
                'SELECT * FROM modifiers WHERE table_id = 20 ORDER BY id'
            );

            // Get defense means
            const defenseMeans = await this.getDefenseMeans();

            // Get current game state and jobs for calculations
            const gameState = await db.get('SELECT * FROM game_state WHERE id = 1');
            const jobs = await db.all('SELECT * FROM jobs');

            // Get all modifier tables for calculations
            const modifierTables = await db.all('SELECT * FROM modifier_tables');

            // Calculate total inhabitants
            const totalInhabitants = jobs.reduce((total, job) => total + job.number, 0);

            // Use the crime probability from the game state if available, otherwise calculate it
            let crimeProbability;
            if (gameState && gameState.crime_probability !== null && gameState.crime_probability !== undefined) {
                crimeProbability = gameState.crime_probability;
            } else {
                crimeProbability = this.calculateCrimeProbability(
                    { ...gameState, total_inhabitants: totalInhabitants },
                    jobs,
                    modifierTables
                );
            }

            // Use the army strength from the game state if available, otherwise calculate it
            let armyStrength;
            if (gameState && gameState.army_strength !== null && gameState.army_strength !== undefined) {
                armyStrength = gameState.army_strength;
            } else {
                armyStrength = this.calculateArmyStrength(
                    { ...gameState, inhabitants_to_pay: totalInhabitants },
                    jobs,
                    modifierTables
                );
            }

            // Calculate defense values
            const defenseValues = this.calculateDefenseValues(modifierTables);

            // Get protectors and soldiers
            const protectors = jobs.find(j => j.name === 'Protector') || { number: 0, sick: 0 };
            const soldiers = jobs.find(j => j.name === 'Soldier') || { number: 0, sick: 0 };

            return {
                justiceModifiers,
                armyModifiers,
                defenseMeans,
                stats: {
                    crimeProbability,
                    armyStrength,
                    protectors: protectors.number,
                    protectors_sick: protectors.sick,
                    soldiers: soldiers.number,
                    soldiers_sick: soldiers.sick,
                    justiceModifier: justiceTable?.current_value || 0,
                    armyModifier: armyTable?.current_value || 0,
                    defenseValues
                }
            };
        } catch (error) {
            console.error('Error getting justice and defense data:', error);
            throw error;
        }
    }
}

module.exports = JusticeDefenseService;
