const db = require('../db');

class ResearchService {
    /**
     * Get research data including modifiers and stats
     * @returns {Promise<Object>} Research data
     */
    static async getResearchData() {
        try {
            // First, update the research modifiers and recalculate the research probability to ensure it's up to date
            await this.updateResearchModifiers();

            // Get research modifiers
            const modifiers = await db.all(`
                SELECT * FROM modifiers
                WHERE table_id = 16
                ORDER BY id DESC
            `);

            // Get research modifier table
            const modifierTable = await db.get(`
                SELECT * FROM modifier_tables
                WHERE id = 16
            `);

            // Get tech level and research probability from game state
            const gameState = await db.get(`
                SELECT tech_level, research_probability
                FROM game_state
                WHERE id = 1
            `);

            // Get active scholars count
            const scholars = await db.get(`
                SELECT number, sick
                FROM jobs
                WHERE name = 'Scholar'
            `);

            const activeScholars = scholars ? (scholars.number - scholars.sick) : 0;

            // Récupérer directement la somme des effets pour être sûr
            const directSum = await db.get(`
                SELECT SUM(CAST(effect AS REAL)) as total_effect
                FROM modifiers
                WHERE table_id = 16
            `);

            // Utiliser la somme directe plutôt que la valeur de la table
            const totalResearchModifier = parseFloat(directSum?.total_effect || 0);

            console.log('Research data being returned:', {
                modifiers: modifiers.length,
                totalResearchModifier,
                tableValue: modifierTable?.current_value,
                directSum: directSum?.total_effect,
                researchProbability: gameState?.research_probability,
                activeScholars
            });

            return {
                modifiers,
                stats: {
                    techLevel: gameState?.tech_level || 0,
                    researchProbability: gameState?.research_probability || 0,
                    totalResearchModifier,
                    activeScholars,
                    scholars: scholars?.number || 0,
                    scholarsSick: scholars?.sick || 0
                }
            };
        } catch (error) {
            console.error('Error getting research data:', error);
            throw error;
        }
    }

    /**
     * Update the sum of research modifiers
     * @returns {Promise<void>}
     */
    static async updateResearchModifiers() {
        try {
            console.log('Updating research modifiers...');

            // Mettre à jour la valeur actuelle de la table des modificateurs
            // Utiliser la même approche que dans HealthService
            await db.run(`
                UPDATE modifier_tables
                SET current_value = (
                    SELECT SUM(effect)
                    FROM modifiers
                    WHERE table_id = 16
                ),
                updated_at = CURRENT_TIMESTAMP
                WHERE id = 16
            `);

            // Vérifier la valeur actuelle de la table des modificateurs
            const modifierTable = await db.get('SELECT * FROM modifier_tables WHERE id = 16');
            console.log('Modifier table AFTER update:', modifierTable);

            // After updating the modifiers, recalculate the research probability
            await this.recalculateResearchProbability();
        } catch (error) {
            console.error('Error updating research modifiers:', error);
            throw error;
        }
    }

    /**
     * Recalculate and update the research probability based on current data
     * @returns {Promise<number>} The new research probability
     */
    static async recalculateResearchProbability() {
        try {
            // Get all jobs to calculate active scholars
            const jobs = await db.all('SELECT * FROM jobs');

            // Get scholars count
            const scholars = jobs.find(job => job.name === 'Scholar') || { number: 0, sick: 0 };
            const activeScholars = scholars.number - scholars.sick;

            // Get research modifier
            const researchModifierTable = await db.get('SELECT * FROM modifier_tables WHERE id = 16');
            const researchModifier = researchModifierTable?.current_value || 0;

            // Get tech level
            const gameState = await db.get('SELECT tech_level FROM game_state WHERE id = 1');
            const techLevel = gameState?.tech_level || 0;

            // Log values for debugging
            console.log('Research calculation values:', {
                activeScholars,
                techLevel,
                researchModifier
            });

            // Formule originale restaurée
            // Formula: MAX(0.5/(1+EXP(-0.33*(ScholarNumber-ScholarSick)*(1+0.05*TechLevel*TotalResearchModifier)))-(0.6/(1+EXP(0.2*TechLevel)));0)+0.01
            const scholarFactor = activeScholars * (1 + 0.05 * techLevel * researchModifier);
            const term1 = 0.5 / (1 + Math.exp(-0.33 * scholarFactor));
            const term2 = 0.6 / (1 + Math.exp(0.2 * techLevel));
            const researchProbability = Math.max(term1 - term2, 0) + 0.01;

            // Calcul effectué avec la formule originale

            // Enhanced logging for debugging
            console.log('===== RESEARCH PROBABILITY CALCULATION =====');
            console.log('Active Scholars:', activeScholars);
            console.log('Research Modifier (sum of all modifiers):', researchModifier);
            console.log('Tech Level:', techLevel);
            console.log('Scholar Factor:', scholarFactor);
            console.log('Term 1:', term1);
            console.log('Term 2:', term2);
            console.log('Final Research Probability:', researchProbability);
            console.log('==========================================');

            // Update the game state with the new probability
            await db.run(`
                UPDATE game_state
                SET research_probability = ?,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = 1
            `, [researchProbability]);

            return researchProbability;
        } catch (error) {
            console.error('Error recalculating research probability:', error);
            throw error;
        }
    }
}

module.exports = ResearchService;
