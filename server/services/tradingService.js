const db = require('../db');
const { DEFAULT_VALUES } = require('../utils/constants');

class TradingService {
    /**
     * Calculate trading revenue based on craftsmen and modifiers
     * @param {Object} gameState - Current game state
     * @param {Array} jobs - Array of jobs
     * @param {Array} modifierTables - Array of modifier tables
     * @param {Number} moralModifier - Moral modifier value
     * @returns {Number} - Trading revenue value
     */
    static calculateTradingRevenue(gameState, jobs, modifierTables, moralModifier) {
        try {
            // Get craftsmen
            const craftsmen = jobs.find(j => j.name === 'Craftsman') || { number: 0, sick: 0 };
            const activeCraftsmen = craftsmen.number - craftsmen.sick;

            // Get trading modifier value
            const tradingModifier = this.getModifierValue(modifierTables, 18);

            // Calculate trading revenue using the formula:
            // tradingRevenue = activeCraftsmen * MAX(0, 1 + tradingModifier) * TRADING_VALUE
            const tradingRevenue = activeCraftsmen *
                                 Math.max(0, 1 + tradingModifier) *
                                 this.getConfigValue('TRADING_VALUE');

            console.log('Trading revenue calculation:', {
                activeCraftsmen,
                tradingModifier,
                tradingValue: this.getConfigValue('TRADING_VALUE'),
                result: tradingRevenue
            });

            return tradingRevenue;
        } catch (error) {
            console.error('Error calculating trading revenue:', error);
            return 0; // Default to 0 if calculation fails
        }
    }

    /**
     * Get a modifier value from the modifier tables
     * @param {Array} modifierTables - Array of modifier tables
     * @param {Number} tableId - ID of the table to get the value from
     * @returns {Number} - Modifier value
     */
    static getModifierValue(modifierTables, tableId) {
        const table = modifierTables.find(t => t.id === tableId);
        return table ? table.current_value : 0;
    }

    /**
     * Get a configuration value
     * @param {String} name - Configuration name
     * @param {*} defaultValue - Default value if not found
     * @returns {*} - Configuration value
     */
    static getConfigValue(name, defaultValue) {
        // For now, return hardcoded values to avoid async issues
        return DEFAULT_VALUES[name] || defaultValue;
    }

    /**
     * Update a modifier table's current_value based on its modifiers
     * @param {Number} tableId - ID of the table to update
     * @returns {Promise<void>}
     */
    static async updateModifierTableSum(tableId) {
        try {
            // Get all modifiers for this table
            const modifiers = await db.all('SELECT * FROM modifiers WHERE table_id = ?', [tableId]);

            // Calculate the sum of all modifier effects
            const sum = modifiers.reduce((total, mod) => total + mod.effect, 0);

            // Update the table's current_value
            await db.run(
                'UPDATE modifier_tables SET current_value = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                [sum, tableId]
            );

            console.log(`Updated table ${tableId} current_value to ${sum}`);
        } catch (error) {
            console.error(`Error updating modifier table ${tableId}:`, error);
            throw error;
        }
    }

    /**
     * Update all trading-related modifier tables
     * @returns {Promise<void>}
     */
    static async updateTradingModifiers() {
        try {
            // Update trading effects table
            await this.updateModifierTableSum(18); // Trading effects
        } catch (error) {
            console.error('Error updating trading modifiers:', error);
            throw error;
        }
    }

    /**
     * Get all trading-related data
     * @returns {Promise<Object>} - Trading data
     */
    static async getTradingData() {
        try {
            // Update all trading-related modifier tables first
            await this.updateTradingModifiers();

            // Get trading effects table
            const tradingTable = await db.get('SELECT * FROM modifier_tables WHERE id = 18');

            // Get modifiers for trading table
            const tradingModifiers = await db.all(
                'SELECT * FROM modifiers WHERE table_id = 18 ORDER BY id'
            );

            // Get current game state and jobs for calculations
            const gameState = await db.get('SELECT * FROM game_state WHERE id = 1');
            const jobs = await db.all('SELECT * FROM jobs');

            // Get moral modifier
            const moralModifier = gameState.moral_value - 1.0;

            // Calculate trading revenue
            const modifierTables = await db.all('SELECT * FROM modifier_tables');
            const tradingRevenue = this.calculateTradingRevenue(
                gameState,
                jobs,
                modifierTables,
                moralModifier
            );

            // Get craftsmen
            const craftsmen = jobs.find(j => j.name === 'Craftsman') || { number: 0, sick: 0 };

            return {
                modifiers: tradingModifiers,
                stats: {
                    craftsmen: craftsmen.number,
                    craftsmen_sick: craftsmen.sick,
                    trading_revenue: tradingRevenue,
                    trading_modifier: tradingTable?.current_value || 0,
                    moral_modifier: moralModifier,
                    trading_value: this.getConfigValue('TRADING_VALUE')
                }
            };
        } catch (error) {
            console.error('Error getting trading data:', error);
            throw error;
        }
    }
}

module.exports = TradingService;
