const db = require('../db');

class FoodService {
    /**
     * Calculate food production based on current game state
     * @param {Object} gameState - Current game state
     * @param {Array} jobs - Array of job objects
     * @param {Array} modifierTables - Array of modifier tables
     * @param {Number} moralModifier - Current moral modifier value
     * @returns {Number} - Calculated food production
     */
    static calculateFoodProduction(gameState, jobs, modifierTables, moralModifier) {
        // Get farmers
        const farmers = jobs.find(j => j.name === 'Farmer') || { number: 0, sick: 0 };
        const activeFarmers = farmers.number - farmers.sick;

        // Get food modifiers
        const foodGeneralModifier = modifierTables.find(m => m.id === 1)?.current_value || 0;
        const foodTechModifier = modifierTables.find(m => m.id === 2)?.current_value || 0;

        // Get food per farmer from config (hardcoded to 4 for now)
        const foodPerFarmer = 4;

        // Get season factor with fallback
        const seasonFactor = gameState?.season_factor || 1;

        // Log inputs for debugging
        console.log('Food production inputs:', {
            activeFarmers,
            foodPerFarmer,
            seasonFactor,
            foodGeneralModifier,
            foodTechModifier,
            moralModifier
        });

        // Calculate production using the formula from base prompt
        // prodvivresCell = (FarmerNumber - FarmerSick) * FoodPerFarmer * SeasonFactor *
        //                  (1 + FoodOtherMultiplier + FoodTechMultiplier + MoralMultiplier)
        const productionBeforeRounding = activeFarmers *
               foodPerFarmer *
               seasonFactor *
               (1 + foodGeneralModifier + foodTechModifier + moralModifier);

        // Round to 1 decimal place for consistency with UI display
        const production = Math.round(productionBeforeRounding * 10) / 10;

        console.log('Food production calculation:', {
            formula: 'activeFarmers * foodPerFarmer * seasonFactor * (1 + foodGeneralModifier + foodTechModifier + moralModifier)',
            activeFarmers,
            foodPerFarmer,
            seasonFactor,
            foodGeneralModifier,
            foodTechModifier,
            moralModifier,
            before: productionBeforeRounding,
            after: production
        });

        return production;
    }

    /**
     * Calculate food consumption based on total inhabitants
     * @param {Array} jobs - Array of job objects
     * @returns {Number} - Total food consumption
     */
    static calculateFoodConsumption(jobs) {
        // Each inhabitant consumes 1 food unit per cycle
        const totalInhabitants = jobs.reduce((sum, job) => sum + job.number, 0);

        // Round to 1 decimal place for consistency with UI display
        const consumption = Math.round(totalInhabitants * 10) / 10;

        console.log('Food consumption calculation:', {
            totalInhabitants,
            before: totalInhabitants,
            after: consumption
        });
        return consumption;
    }

    /**
     * Calculate new food reserves after a cycle
     * @param {Number} currentReserves - Current food reserves
     * @param {Number} production - Food production this cycle
     * @param {Number} consumption - Food consumption this cycle
     * @param {Array} modifierTables - Array of modifier tables
     * @returns {Object} - New food reserves and related data
     */
    static async calculateNewFoodReserves(currentReserves, production, consumption, modifierTables) {
        console.log('===== FOOD RESERVES CALCULATION - START =====');
        console.log('Input values:', {
            currentReserves,
            production,
            consumption,
            modifierTablesCount: modifierTables ? modifierTables.length : 'undefined'
        });

        // Handle null or undefined values
        const safeCurrentReserves = typeof currentReserves === 'number' && !isNaN(currentReserves) ? currentReserves : 58.0;
        const safeProduction = typeof production === 'number' && !isNaN(production) ? production : 0;
        const safeConsumption = typeof consumption === 'number' && !isNaN(consumption) ? consumption : 30;

        console.log('Safe values after validation:', {
            safeCurrentReserves,
            safeProduction,
            safeConsumption
        });

        // Get perishable rate from modifier table 3 (Food Perishable)
        // First, get all modifiers for table 3
        let perishableRate = 0.2; // Default value

        try {
            // First, update the modifier table to ensure current_value is up to date
            await this.updateModifierTableSum(3);

            // Get the current value from the modifier table
            const perishableTable = await db.get('SELECT current_value FROM modifier_tables WHERE id = 3');

            if (perishableTable && typeof perishableTable.current_value === 'number') {
                perishableRate = perishableTable.current_value;
                console.log('Perishable rate from database:', perishableTable.current_value);
            } else {
                console.log('Using default perishable rate (0.2) because table value is invalid:', perishableTable);
            }

            // Also get the individual modifiers for logging
            const perishableModifiers = await db.all('SELECT * FROM modifiers WHERE table_id = 3');

            console.log('Perishable modifiers details:', {
                modifiers: perishableModifiers,
                tableValue: perishableRate,
                calculatedSum: perishableModifiers.reduce((sum, mod) => sum + mod.effect, 0)
            });

            // Make sure perishable rate is never negative
            perishableRate = Math.max(0, perishableRate);
            console.log('Final perishable rate after validation:', perishableRate);
        } catch (error) {
            console.error('Error getting perishable modifiers:', error);
            console.log('Using default perishable rate (0.2) due to error');
            // Use default value if there's an error
        }

        // Calculate food lost to perishable rate
        const foodLostToPerishable = safeCurrentReserves * perishableRate;

        // Log the exact values with high precision to detect floating point issues
        console.log('Food lost to perishable calculation (high precision):', {
            formula: 'safeCurrentReserves * perishableRate',
            safeCurrentReserves: safeCurrentReserves,
            perishableRate: perishableRate,
            result: foodLostToPerishable,
            resultToString: foodLostToPerishable.toString(),
            resultWithPrecision: foodLostToPerishable.toPrecision(16)
        });

        console.log('Food lost to perishable calculation:', {
            formula: 'safeCurrentReserves * perishableRate',
            safeCurrentReserves,
            perishableRate,
            result: foodLostToPerishable
        });

        // Calculate new reserves
        // New reserves = (Current reserves - Perishable loss) + (Production - Consumption)
        const remainingAfterPerishable = safeCurrentReserves - foodLostToPerishable;
        console.log('Remaining after perishable calculation:', {
            formula: 'safeCurrentReserves - foodLostToPerishable',
            safeCurrentReserves,
            foodLostToPerishable,
            result: remainingAfterPerishable
        });

        // Calculate net production-consumption and round to 1 decimal place
        const netBeforeRounding = safeProduction - safeConsumption;
        const netProductionConsumption = Math.round(netBeforeRounding * 10) / 10;

        console.log('Net production-consumption calculation:', {
            formula: 'Math.round((safeProduction - safeConsumption) * 10) / 10',
            safeProduction,
            safeConsumption,
            before: netBeforeRounding,
            after: netProductionConsumption
        });

        // Add net production to remaining reserves
        let newReservesBeforeMax = remainingAfterPerishable + netProductionConsumption;
        console.log('New reserves before max calculation:', {
            formula: 'remainingAfterPerishable + netProductionConsumption',
            remainingAfterPerishable,
            netProductionConsumption,
            result: newReservesBeforeMax
        });

        // Log the exact values with high precision before rounding
        console.log('New reserves before rounding (high precision):', {
            newReservesBeforeMax: newReservesBeforeMax,
            toString: newReservesBeforeMax.toString(),
            toPrecision: newReservesBeforeMax.toPrecision(16),
            multiplyBy10: (newReservesBeforeMax * 10).toString(),
            rounded: Math.round(newReservesBeforeMax * 10).toString(),
            dividedBy10: (Math.round(newReservesBeforeMax * 10) / 10).toString()
        });

        // Round to 1 decimal place to avoid floating point issues
        const newReservesBeforeRounding = newReservesBeforeMax;
        newReservesBeforeMax = Math.round(newReservesBeforeMax * 10) / 10;
        console.log('New reserves after rounding:', {
            formula: 'Math.round(newReservesBeforeMax * 10) / 10',
            before: newReservesBeforeRounding,
            after: newReservesBeforeMax
        });

        // If we end up with negative reserves, set to 0
        const newReserves = Math.max(0, newReservesBeforeMax);
        console.log('Final new reserves after max check:', {
            formula: 'Math.max(0, newReservesBeforeMax)',
            newReservesBeforeMax,
            result: newReserves
        });

        // Log the calculation for debugging
        console.log('Food reserves calculation summary:', {
            currentReserves: safeCurrentReserves,
            perishableRate,
            foodLostToPerishable,
            remainingAfterPerishable,
            production: safeProduction,
            consumption: safeConsumption,
            netProductionConsumption,
            newReservesBeforeMax,
            newReserves,
            expectedChange: netProductionConsumption - foodLostToPerishable,
            actualChange: newReserves - safeCurrentReserves
        });
        console.log('===== FOOD RESERVES CALCULATION - END =====');

        return {
            newReserves,
            perishableRate,
            foodLostToPerishable,
            hasFamine: newReserves <= 10 // Famine if reserves are 10 or less
        };
    }

    /**
     * Get a configuration value from the database
     * @param {String} name - Configuration name
     * @param {*} defaultValue - Default value if not found
     * @returns {*} - Configuration value
     */
    static getConfigValue(name, defaultValue) {
        // For now, return hardcoded values to avoid async issues
        const configValues = {
            'FOOD_PER_FARMER': 4,
            'MATERIALS_PER_WORKER': 1,
            'MATERIALS_PER_MINER': 0.5,
            'MINING_VALUE': 30,
            'TRADING_VALUE': 25
        };

        return configValues[name] || defaultValue;
    }

    /**
     * Update all food-related modifier tables with their current sums
     * @returns {Object} - Updated modifier values
     */
    static async updateFoodModifiers() {
        try {
            // Update general food effects (table ID 1)
            await this.updateModifierTableSum(1);

            // Update tech food effects (table ID 2)
            await this.updateModifierTableSum(2);

            // Update perishable rate (table ID 3)
            await this.updateModifierTableSum(3);

            // Get updated values
            const tables = await db.all('SELECT id, current_value FROM modifier_tables WHERE id IN (1, 2, 3)');

            return {
                generalEffects: tables.find(t => t.id === 1)?.current_value || 0,
                techEffects: tables.find(t => t.id === 2)?.current_value || 0,
                perishableRate: tables.find(t => t.id === 3)?.current_value || 0.2
            };
        } catch (error) {
            console.error('Error updating food modifiers:', error);
            throw error;
        }
    }

    /**
     * Update a modifier table's current_value with the sum of its modifiers
     * @param {Number} tableId - Modifier table ID
     */
    static async updateModifierTableSum(tableId) {
        try {
            await db.run(`
                UPDATE modifier_tables
                SET current_value = (
                    SELECT SUM(effect)
                    FROM modifiers
                    WHERE table_id = ?
                ),
                updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            `, [tableId, tableId]);
        } catch (error) {
            console.error(`Error updating modifier table ${tableId}:`, error);
            throw error;
        }
    }

    /**
     * Fix duplicate "Base" entries in the perishable rate table
     */
    static async fixDuplicatePerishableEntries() {
        try {
            // Check for duplicate "Base" entries in table 3
            const baseEntries = await db.all(
                'SELECT id FROM modifiers WHERE table_id = 3 AND title = "Base" ORDER BY id'
            );

            // If more than one entry found, keep only the first one
            if (baseEntries.length > 1) {
                console.log(`Found ${baseEntries.length} duplicate "Base" entries in perishable table. Fixing...`);

                // Delete all but the first one
                for (let i = 1; i < baseEntries.length; i++) {
                    await db.run('DELETE FROM modifiers WHERE id = ?', [baseEntries[i].id]);
                }

                // Update the table's current_value
                await this.updateModifierTableSum(3);

                console.log('Duplicate entries fixed.');
            }
        } catch (error) {
            console.error('Error fixing duplicate perishable entries:', error);
            throw error;
        }
    }
}

module.exports = FoodService;
