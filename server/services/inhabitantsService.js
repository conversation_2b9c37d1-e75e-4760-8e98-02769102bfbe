const db = require('../db');
const axios = require('axios');

class InhabitantsService {
    /**
     * Get all inhabitants with pagination and filters
     * @param {number} page - Page number
     * @param {number} limit - Number of items per page
     * @param {Object} filters - Filter criteria
     * @returns {Promise<Object>} - Paginated inhabitants data
     */
    static async getInhabitants(page = 1, limit = 20, filters = {}) {
        try {
            const offset = (page - 1) * limit;

            // Build the WHERE clause based on filters
            let whereClause = '';
            const params = [];

            if (filters.job) {
                whereClause += whereClause ? ' AND ' : ' WHERE ';
                whereClause += 'job_id = ?';
                params.push(filters.job);
            }

            if (filters.race) {
                whereClause += whereClause ? ' AND ' : ' WHERE ';
                whereClause += 'race = ?';
                params.push(filters.race);
            }

            if (filters.gender) {
                whereClause += whereClause ? ' AND ' : ' WHERE ';
                whereClause += 'gender = ?';
                params.push(filters.gender);
            }

            if (filters.isPC !== undefined && filters.isPC !== '') {
                whereClause += whereClause ? ' AND ' : ' WHERE ';
                whereClause += 'is_pc = ?';
                params.push(filters.isPC);
            }

            if (filters.isSick !== undefined && filters.isSick !== '') {
                whereClause += whereClause ? ' AND ' : ' WHERE ';
                whereClause += 'is_sick = ?';
                params.push(filters.isSick);
            }

            if (filters.searchQuery) {
                whereClause += whereClause ? ' AND ' : ' WHERE ';
                whereClause += '(first_name LIKE ? OR last_name LIKE ? OR short_description LIKE ?)';
                const searchTerm = `%${filters.searchQuery}%`;
                params.push(searchTerm, searchTerm, searchTerm);
            }

            // Build the ORDER BY clause
            let orderClause = '';
            if (filters.sortBy) {
                orderClause = ` ORDER BY ${filters.sortBy} ${filters.sortOrder === 'desc' ? 'DESC' : 'ASC'}`;
            } else {
                orderClause = ' ORDER BY last_name ASC';
            }

            // Get total count for pagination
            const countQuery = `SELECT COUNT(*) as total FROM inhabitants${whereClause}`;
            const countResult = await db.get(countQuery, params);
            const total = countResult.total;

            // Get paginated data
            const query = `
                SELECT * FROM inhabitants
                ${whereClause}
                ${orderClause}
                LIMIT ? OFFSET ?
            `;

            const inhabitants = await db.all(query, [...params, limit, offset]);

            return {
                inhabitants,
                total,
                page,
                limit,
                totalPages: Math.ceil(total / limit)
            };
        } catch (error) {
            console.error('Error getting inhabitants:', error);
            throw error;
        }
    }

    /**
     * Get a specific inhabitant by ID
     * @param {number} id - Inhabitant ID
     * @returns {Promise<Object>} - Inhabitant data
     */
    static async getInhabitant(id) {
        try {
            const inhabitant = await db.get('SELECT * FROM inhabitants WHERE id = ?', [id]);

            if (!inhabitant) {
                throw new Error('Inhabitant not found');
            }

            return inhabitant;
        } catch (error) {
            console.error(`Error getting inhabitant ${id}:`, error);
            throw error;
        }
    }

    /**
     * Create a new inhabitant
     * @param {Object} data - Inhabitant data
     * @returns {Promise<Object>} - Created inhabitant
     */
    static async createInhabitant(data) {
        try {
            // Get current game state for arrival date
            const gameState = await db.get('SELECT cycle_number, month, year FROM game_state WHERE id = 1');

            // Generate random age between 20 and 100
            const age = Math.floor(Math.random() * (100 - 20 + 1)) + 20;

            // If name is not provided, generate random name
            if (!data.first_name || !data.last_name) {
                const randomName = await this.generateRandomName(data.race, data.gender);
                data.first_name = randomName.firstName;
                data.last_name = randomName.lastName;
            }

            // If description is not provided, generate a random one
            if (!data.short_description) {
                data.short_description = await this.generateShortDescription(data.race, data.gender, data.job_id);
            }

            // If history is not provided, generate a random one
            if (!data.history) {
                data.history = await this.generateHistory(data.race, data.gender, age);
            }

            const result = await db.run(`
                INSERT INTO inhabitants (
                    first_name, last_name, race, gender, job_id, is_pc, is_sick,
                    short_description, history, age, arrival_cycle, arrival_month, arrival_year
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `, [
                data.first_name,
                data.last_name,
                data.race,
                data.gender,
                data.job_id,
                data.is_pc || 0,
                data.is_sick || 0,
                data.short_description,
                data.history,
                age,
                gameState.cycle_number,
                gameState.month,
                gameState.year
            ]);

            const newInhabitant = await this.getInhabitant(result.lastID);

            // Update job count in jobs table
            await db.run(
                'UPDATE jobs SET number = number + 1, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                [data.job_id]
            );

            return newInhabitant;
        } catch (error) {
            console.error('Error creating inhabitant:', error);
            throw error;
        }
    }

    /**
     * Update an existing inhabitant
     * @param {number} id - Inhabitant ID
     * @param {Object} data - Updated data
     * @returns {Promise<Object>} - Updated inhabitant
     */
    static async updateInhabitant(id, data) {
        try {
            const inhabitant = await this.getInhabitant(id);

            if (!inhabitant) {
                throw new Error('Inhabitant not found');
            }

            const updateFields = [];
            const updateValues = [];

            // Only update provided fields
            if (data.first_name !== undefined) {
                updateFields.push('first_name = ?');
                updateValues.push(data.first_name);
            }

            if (data.last_name !== undefined) {
                updateFields.push('last_name = ?');
                updateValues.push(data.last_name);
            }

            if (data.race !== undefined) {
                updateFields.push('race = ?');
                updateValues.push(data.race);
            }

            if (data.gender !== undefined) {
                updateFields.push('gender = ?');
                updateValues.push(data.gender);
            }

            if (data.job_id !== undefined) {
                updateFields.push('job_id = ?');
                updateValues.push(data.job_id);
            }

            if (data.is_pc !== undefined) {
                updateFields.push('is_pc = ?');
                updateValues.push(data.is_pc);
            }

            if (data.is_sick !== undefined) {
                updateFields.push('is_sick = ?');
                updateValues.push(data.is_sick);
            }

            if (data.short_description !== undefined) {
                updateFields.push('short_description = ?');
                updateValues.push(data.short_description);
            }

            if (data.history !== undefined) {
                updateFields.push('history = ?');
                updateValues.push(data.history);
            }

            if (data.age !== undefined) {
                updateFields.push('age = ?');
                updateValues.push(data.age);
            }

            // Add updated_at timestamp
            updateFields.push('updated_at = CURRENT_TIMESTAMP');

            // Add ID to values array
            updateValues.push(id);

            await db.run(`
                UPDATE inhabitants
                SET ${updateFields.join(', ')}
                WHERE id = ?
            `, updateValues);

            // If job_id was changed, update job counts
            if (data.job_id !== undefined && inhabitant.job_id !== data.job_id) {
                console.log('InhabitantsService: Job changed from', inhabitant.job_id, 'to', data.job_id);

                // Decrement count for old job
                await db.run(
                    'UPDATE jobs SET number = number - 1, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                    [inhabitant.job_id]
                );

                // Increment count for new job
                await db.run(
                    'UPDATE jobs SET number = number + 1, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                    [data.job_id]
                );

                // If the inhabitant was sick, update sick counts
                if (inhabitant.is_sick === 1) {
                    // Decrement sick count for old job
                    await db.run(
                        'UPDATE jobs SET sick = sick - 1, updated_at = CURRENT_TIMESTAMP WHERE id = ? AND sick > 0',
                        [inhabitant.job_id]
                    );

                    // Increment sick count for new job
                    await db.run(
                        'UPDATE jobs SET sick = sick + 1, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                        [data.job_id]
                    );
                }
            }

            // If is_sick was changed, update sick counts
            if (data.is_sick !== undefined && inhabitant.is_sick !== data.is_sick) {
                console.log('InhabitantsService: Sickness status changed from', inhabitant.is_sick, 'to', data.is_sick);

                const change = data.is_sick === 1 ? 1 : -1;

                await db.run(
                    'UPDATE jobs SET sick = sick + ?, updated_at = CURRENT_TIMESTAMP WHERE id = ? AND (sick > 0 OR ? > 0)',
                    [change, inhabitant.job_id, change]
                );
            }

            const updatedInhabitant = await this.getInhabitant(id);
            return updatedInhabitant;
        } catch (error) {
            console.error(`Error updating inhabitant ${id}:`, error);
            throw error;
        }
    }

    /**
     * Delete an inhabitant
     * @param {number} id - Inhabitant ID
     * @returns {Promise<boolean>} - Success status
     */
    static async deleteInhabitant(id) {
        try {
            // Get the inhabitant's job_id before deleting
            let inhabitant;
            try {
                inhabitant = await this.getInhabitant(id);
            } catch (error) {
                // If inhabitant not found, just delete any record that might exist
                if (error.message === 'Inhabitant not found') {
                    const result = await db.run('DELETE FROM inhabitants WHERE id = ?', [id]);
                    return result.changes > 0;
                }
                throw error;
            }

            if (!inhabitant) {
                return false;
            }

            // Delete the inhabitant
            const result = await db.run('DELETE FROM inhabitants WHERE id = ?', [id]);

            if (result.changes > 0 && inhabitant.job_id) {
                // Update job count in jobs table
                await db.run(
                    'UPDATE jobs SET number = number - 1, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                    [inhabitant.job_id]
                );

                // If the inhabitant was sick, update the sick count
                if (inhabitant.is_sick === 1) {
                    await db.run(
                        'UPDATE jobs SET sick = sick - 1, updated_at = CURRENT_TIMESTAMP WHERE id = ? AND sick > 0',
                        [inhabitant.job_id]
                    );
                }
            }

            return result.changes > 0;
        } catch (error) {
            console.error(`Error deleting inhabitant ${id}:`, error);
            throw error;
        }
    }

    /**
     * Generate a random name using Iron Arachne API
     * @param {string} race - Race (e.g., 'Dwarf', 'Human')
     * @param {string} gender - Gender ('Male' or 'Female')
     * @returns {Promise<Object>} - Generated name
     */
    static async generateRandomName(race, gender) {
        try {
            // Map our race names to Iron Arachne's format
            const raceMap = {
                'Dwarf': 'dwarf',
                'Human': 'human',
                'Elf': 'elf',
                'Halfling': 'halfling',
                'Gnome': 'gnome'
            };

            // Map our gender to Iron Arachne's format
            const genderMap = {
                'Male': 'male',
                'Female': 'female'
            };

            const mappedRace = raceMap[race] || 'human';
            const mappedGender = genderMap[gender] || 'male';

            // Try to fetch from Iron Arachne API
            try {
                const response = await axios.get(`https://names.ironarachne.com/race/${mappedRace}/${mappedGender}/1`);

                if (response.data && response.data.length > 0) {
                    const nameParts = response.data[0].split(' ');

                    // Handle cases where the API returns more than two name parts
                    if (nameParts.length >= 2) {
                        return {
                            firstName: nameParts[0],
                            lastName: nameParts.slice(1).join(' ')
                        };
                    }
                }
            } catch (apiError) {
                console.warn('Failed to fetch from Iron Arachne API, using fallback names:', apiError.message);
            }

            // Fallback names if API fails
            const fallbackFirstNames = {
                'Male': {
                    'Dwarf': ['Thorin', 'Balin', 'Dwalin', 'Gimli', 'Gloin'],
                    'Human': ['John', 'William', 'James', 'Robert', 'Michael'],
                    'Elf': ['Legolas', 'Elrond', 'Thranduil', 'Celeborn', 'Glorfindel'],
                    'Halfling': ['Frodo', 'Bilbo', 'Samwise', 'Peregrin', 'Meriadoc'],
                    'Gnome': ['Fizban', 'Gimble', 'Zook', 'Fibblestib', 'Warryn']
                },
                'Female': {
                    'Dwarf': ['Dis', 'Kili', 'Fili', 'Thorina', 'Darina'],
                    'Human': ['Mary', 'Elizabeth', 'Sarah', 'Jennifer', 'Emily'],
                    'Elf': ['Galadriel', 'Arwen', 'Tauriel', 'Celebrian', 'Luthien'],
                    'Halfling': ['Rosie', 'Elanor', 'Primula', 'Lobelia', 'Esmeralda'],
                    'Gnome': ['Tilly', 'Shamil', 'Myrtle', 'Waywocket', 'Bimpnottin']
                }
            };

            const fallbackLastNames = {
                'Dwarf': ['Ironforge', 'Stonehammer', 'Battlehammer', 'Goldbeard', 'Silveraxe'],
                'Human': ['Smith', 'Johnson', 'Williams', 'Brown', 'Jones'],
                'Elf': ['Greenleaf', 'Starweaver', 'Moonwhisper', 'Silverbow', 'Nightbreeze'],
                'Halfling': ['Baggins', 'Gamgee', 'Took', 'Brandybuck', 'Proudfoot'],
                'Gnome': ['Tinkertop', 'Geargrinder', 'Fizzlebang', 'Sparksprocket', 'Cogwheel']
            };

            // Get random names from fallback lists
            const firstNames = fallbackFirstNames[gender][race] || fallbackFirstNames[gender]['Human'];
            const lastNames = fallbackLastNames[race] || fallbackLastNames['Human'];

            const firstName = firstNames[Math.floor(Math.random() * firstNames.length)];
            const lastName = lastNames[Math.floor(Math.random() * lastNames.length)];

            return { firstName, lastName };
        } catch (error) {
            console.error('Error generating random name:', error);
            // Return default name as last resort
            return { firstName: 'Unknown', lastName: 'Resident' };
        }
    }

    /**
     * Generate a short description based on race, gender, and job
     * @param {string} race - Race
     * @param {string} gender - Gender
     * @param {number} jobId - Job ID
     * @returns {Promise<string>} - Generated description
     */
    static async generateShortDescription(race, gender, jobId) {
        try {
            // Get job name
            const job = await db.get('SELECT name FROM jobs WHERE id = ?', [jobId]);
            const jobName = job ? job.name : 'Unknown';

            // Templates for descriptions
            const templates = [
                `Un${gender === 'Female' ? 'e' : ''} ${race.toLowerCase()} ${gender === 'Female' ? 'travailleuse' : 'travailleur'} acharné${gender === 'Female' ? 'e' : ''} qui excelle dans son rôle de ${jobName}.`,
                `${gender === 'Female' ? 'Cette' : 'Ce'} ${race.toLowerCase()} a rejoint la mine à la recherche d'une vie meilleure et travaille maintenant comme ${jobName}.`,
                `Originaire d'une communauté ${race.toLowerCase()} lointaine, ${gender === 'Female' ? 'elle' : 'il'} s'est adapté${gender === 'Female' ? 'e' : ''} à la vie dans la mine comme ${jobName}.`,
                `Un${gender === 'Female' ? 'e' : ''} ${jobName} ${gender === 'Female' ? 'dévouée' : 'dévoué'} qui prend son travail très au sérieux et est respecté${gender === 'Female' ? 'e' : ''} par ses pairs.`,
                `${gender === 'Female' ? 'Elle' : 'Il'} a trouvé sa vocation comme ${jobName} et contribue significativement à la prospérité de la mine.`
            ];

            // Select a random template
            const template = templates[Math.floor(Math.random() * templates.length)];

            return template;
        } catch (error) {
            console.error('Error generating short description:', error);
            return `Un${gender === 'Female' ? 'e' : ''} habitant${gender === 'Female' ? 'e' : ''} de la mine.`;
        }
    }

    /**
     * Generate a history based on race, gender, and age
     * @param {string} race - Race
     * @param {string} gender - Gender
     * @param {number} age - Age
     * @returns {Promise<string>} - Generated history
     */
    static async generateHistory(race, gender, age) {
        try {
            // Templates for histories
            const templates = [
                `${gender === 'Female' ? 'Née' : 'Né'} dans une famille de ${race.toLowerCase()}s, ${gender === 'Female' ? 'elle' : 'il'} a grandi en apprenant les traditions de son peuple. À l'âge de ${Math.floor(age / 3)} ans, ${gender === 'Female' ? 'elle' : 'il'} a quitté sa maison pour chercher fortune et a finalement trouvé sa place dans cette mine.`,

                `Après avoir vécu ${age - Math.floor(Math.random() * 10) - 5} ans dans ${gender === 'Female' ? 'sa' : 'son'} village natal, ${gender === 'Female' ? 'elle' : 'il'} a décidé de partir à l'aventure. Les rumeurs de richesse dans les mines l'ont attiré${gender === 'Female' ? 'e' : ''} ici, où ${gender === 'Female' ? 'elle' : 'il'} s'est installé${gender === 'Female' ? 'e' : ''} et a commencé une nouvelle vie.`,

                `${gender === 'Female' ? 'Elle' : 'Il'} a survécu à une attaque de monstres qui a détruit son village il y a ${Math.floor(age / 4)} ans. Après avoir erré pendant plusieurs années, ${gender === 'Female' ? 'elle' : 'il'} a trouvé refuge dans cette mine où ${gender === 'Female' ? 'elle' : 'il'} a pu reconstruire sa vie.`,

                `Issu${gender === 'Female' ? 'e' : ''} d'une lignée de ${race.toLowerCase()}s ${gender === 'Female' ? 'artisanes' : 'artisans'}, ${gender === 'Female' ? 'elle' : 'il'} a appris son métier dès son plus jeune âge. Sa réputation l'a précédé${gender === 'Female' ? 'e' : ''} jusqu'à cette mine, où ${gender === 'Female' ? 'elle' : 'il'} a été recruté${gender === 'Female' ? 'e' : ''} pour ses compétences exceptionnelles.`,

                `Pendant ${Math.floor(age / 2)} ans, ${gender === 'Female' ? 'elle' : 'il'} a voyagé à travers le monde, accumulant des connaissances et des expériences. La promesse de stabilité et de communauté l'a finalement conduit${gender === 'Female' ? 'e' : ''} à s'installer dans cette mine.`
            ];

            // Add race-specific history elements
            const raceSpecific = {
                'Dwarf': `En tant que ${race.toLowerCase()}, ${gender === 'Female' ? 'elle' : 'il'} possède une affinité naturelle pour la pierre et les métaux, ce qui fait de ${gender === 'Female' ? 'elle' : 'lui'} un${gender === 'Female' ? 'e' : ''} travailleur${gender === 'Female' ? 'se' : ''} précieux${gender === 'Female' ? 'se' : ''} dans la mine.`,

                'Human': `Sa nature adaptable d'${race.toLowerCase()} lui a permis de s'intégrer rapidement dans la communauté diverse de la mine, où ${gender === 'Female' ? 'elle' : 'il'} a établi de nombreuses relations.`,

                'Elf': `Malgré la longévité typique des ${race.toLowerCase()}s, ${gender === 'Female' ? 'elle' : 'il'} a choisi de vivre parmi les races à vie plus courte, fasciné${gender === 'Female' ? 'e' : ''} par leur énergie et leur détermination.`,

                'Halfling': `Comme beaucoup de ${race.toLowerCase()}s, ${gender === 'Female' ? 'elle' : 'il'} apprécie le confort et la bonne nourriture, mais a également un esprit aventureux qui l'a conduit${gender === 'Female' ? 'e' : ''} à cette mine.`,

                'Gnome': `Son ingéniosité naturelle de ${race.toLowerCase()} a souvent conduit à des innovations dans les techniques minières, bien que certaines de ses expériences aient eu des résultats... inattendus.`
            };

            // Select a random template and add race-specific element
            const template = templates[Math.floor(Math.random() * templates.length)];
            const raceElement = raceSpecific[race] || '';

            return `${template} ${raceElement}`;
        } catch (error) {
            console.error('Error generating history:', error);
            return `Un${gender === 'Female' ? 'e' : ''} habitant${gender === 'Female' ? 'e' : ''} ordinaire de la mine.`;
        }
    }

    /**
     * Synchronize inhabitants with jobs
     * @returns {Promise<Object>} - Synchronization results
     */
    static async syncWithJobs() {
        try {
            // Get all jobs
            const jobs = await db.all('SELECT * FROM jobs');

            // Get current inhabitants count by job
            const inhabitantCounts = await db.all('SELECT job_id, COUNT(*) as count FROM inhabitants GROUP BY job_id');

            // Create a map of job_id to count
            const countMap = {};
            inhabitantCounts.forEach(item => {
                countMap[item.job_id] = item.count;
            });

            const results = {
                created: 0,
                removed: 0,
                unchanged: 0,
                details: []
            };

            // Process each job
            for (const job of jobs) {
                const currentCount = countMap[job.id] || 0;
                const targetCount = job.number;

                if (currentCount < targetCount) {
                    // Need to create new inhabitants
                    const toCreate = targetCount - currentCount;

                    for (let i = 0; i < toCreate; i++) {
                        // Randomly select race and gender
                        const races = ['Dwarf', 'Human', 'Elf', 'Halfling', 'Gnome'];
                        const genders = ['Male', 'Female'];

                        const race = races[Math.floor(Math.random() * races.length)];
                        const gender = genders[Math.floor(Math.random() * genders.length)];

                        await this.createInhabitant({
                            race,
                            gender,
                            job_id: job.id,
                            is_pc: 0,
                            is_sick: 0
                        });
                    }

                    results.created += toCreate;
                    results.details.push(`Created ${toCreate} new ${job.name}s`);
                } else if (currentCount > targetCount) {
                    // Need to remove some inhabitants
                    const toRemove = currentCount - targetCount;

                    // Get inhabitants for this job, ordered by creation date (newest first)
                    const inhabitants = await db.all(
                        'SELECT id FROM inhabitants WHERE job_id = ? ORDER BY created_at DESC LIMIT ?',
                        [job.id, toRemove]
                    );

                    // Remove the newest inhabitants first (last in, first out)
                    for (const inhabitant of inhabitants) {
                        try {
                            await this.deleteInhabitant(inhabitant.id);
                        } catch (error) {
                            console.warn(`Failed to delete inhabitant ${inhabitant.id}:`, error.message);
                            // Continue with the next inhabitant
                        }
                    }

                    results.removed += toRemove;
                    results.details.push(`Removed ${toRemove} ${job.name}s`);
                } else {
                    // Count is correct, no changes needed
                    results.unchanged += currentCount;
                    results.details.push(`${job.name}s: ${currentCount} (unchanged)`);
                }
            }

            return results;
        } catch (error) {
            console.error('Error synchronizing inhabitants with jobs:', error);
            throw error;
        }
    }

    /**
     * Apply sickness to inhabitants based on probability
     * @param {number} sickProbability - Probability of getting sick
     * @returns {Promise<Object>} - Results of sickness application
     */
    static async applySickness(sickProbability) {
        try {
            console.log('DEBUG - applySickness called with probability:', sickProbability);

            // Reset all sickness first
            await db.run('UPDATE inhabitants SET is_sick = 0, updated_at = CURRENT_TIMESTAMP');
            console.log('DEBUG - Reset all inhabitants to not sick');

            // Get all non-PC inhabitants
            const inhabitants = await db.all('SELECT id, is_pc FROM inhabitants');
            console.log('DEBUG - Found', inhabitants.length, 'total inhabitants');

            // Filter out PCs
            const nonPcInhabitants = inhabitants.filter(i => i.is_pc !== 1);
            console.log('DEBUG - Found', nonPcInhabitants.length, 'non-PC inhabitants that can get sick');

            let sickCount = 0;
            let checksPerformed = 0;

            // Apply sickness based on probability
            for (const inhabitant of nonPcInhabitants) {
                const randomValue = Math.random();
                const willGetSick = randomValue < sickProbability;
                checksPerformed++;

                if (checksPerformed <= 5) {
                    console.log('DEBUG - Inhabitant', inhabitant.id, 'check:', randomValue, '<', sickProbability, '=', willGetSick);
                }

                if (willGetSick) {
                    await db.run(
                        'UPDATE inhabitants SET is_sick = 1, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                        [inhabitant.id]
                    );
                    sickCount++;
                }
            }

            // Update sick count in jobs table
            const jobSickCounts = await db.all(`
                SELECT job_id, COUNT(*) as sick_count
                FROM inhabitants
                WHERE is_sick = 1
                GROUP BY job_id
            `);

            for (const jobCount of jobSickCounts) {
                await db.run(
                    'UPDATE jobs SET sick = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                    [jobCount.sick_count, jobCount.job_id]
                );
            }

            const result = {
                totalInhabitants: inhabitants.length,
                nonPcInhabitants: nonPcInhabitants.length,
                sickCount,
                sickPercentage: nonPcInhabitants.length > 0 ? (sickCount / nonPcInhabitants.length) * 100 : 0,
                checksPerformed
            };

            console.log('DEBUG - Final sickness results:', result);
            return result;
        } catch (error) {
            console.error('Error applying sickness to inhabitants:', error);
            throw error;
        }
    }

    /**
     * Get statistics about inhabitants
     * @returns {Promise<Object>} - Statistics data
     */
    static async getStatistics() {
        try {
            const stats = {
                total: 0,
                byRace: {},
                byGender: {},
                byJob: {},
                sickCount: 0,
                pcCount: 0,
                averageAge: 0
            };

            // Get total count
            const totalResult = await db.get('SELECT COUNT(*) as count FROM inhabitants');
            stats.total = totalResult.count;

            // Get counts by race
            const raceResults = await db.all('SELECT race, COUNT(*) as count FROM inhabitants GROUP BY race');
            raceResults.forEach(result => {
                stats.byRace[result.race] = result.count;
            });

            // Get counts by gender
            const genderResults = await db.all('SELECT gender, COUNT(*) as count FROM inhabitants GROUP BY gender');
            genderResults.forEach(result => {
                stats.byGender[result.gender] = result.count;
            });

            // Get counts by job
            const jobResults = await db.all(`
                SELECT j.name, COUNT(i.id) as count
                FROM inhabitants i
                JOIN jobs j ON i.job_id = j.id
                GROUP BY i.job_id
            `);
            jobResults.forEach(result => {
                stats.byJob[result.name] = result.count;
            });

            // Get sick count
            const sickResult = await db.get('SELECT COUNT(*) as count FROM inhabitants WHERE is_sick = 1');
            stats.sickCount = sickResult.count;

            // Get PC count
            const pcResult = await db.get('SELECT COUNT(*) as count FROM inhabitants WHERE is_pc = 1');
            stats.pcCount = pcResult.count;

            // Get average age
            const ageResult = await db.get('SELECT AVG(age) as avg_age FROM inhabitants');
            stats.averageAge = ageResult.avg_age;

            // Synchronize job counts with inhabitants
            await this.syncJobCountsWithInhabitants();

            return stats;
        } catch (error) {
            console.error('Error getting inhabitant statistics:', error);
            throw error;
        }
    }

    /**
     * Synchronize job counts in the jobs table with actual inhabitant counts
     * @returns {Promise<void>}
     */
    static async syncJobCountsWithInhabitants() {
        try {
            // Get counts of inhabitants by job
            const jobCounts = await db.all(`
                SELECT job_id, COUNT(*) as count
                FROM inhabitants
                GROUP BY job_id
            `);

            // Get counts of sick inhabitants by job
            const sickCounts = await db.all(`
                SELECT job_id, COUNT(*) as count
                FROM inhabitants
                WHERE is_sick = 1
                GROUP BY job_id
            `);

            // Create a map of job_id to sick count
            const sickCountMap = {};
            sickCounts.forEach(item => {
                sickCountMap[item.job_id] = item.count;
            });

            // Update each job's number field to match the actual inhabitant count
            for (const jobCount of jobCounts) {
                await db.run(
                    'UPDATE jobs SET number = ?, sick = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                    [jobCount.count, sickCountMap[jobCount.job_id] || 0, jobCount.job_id]
                );
            }

            // Handle jobs with no inhabitants
            const jobsWithNoInhabitants = await db.all(`
                SELECT id FROM jobs
                WHERE id NOT IN (SELECT DISTINCT job_id FROM inhabitants WHERE job_id IS NOT NULL)
            `);

            for (const job of jobsWithNoInhabitants) {
                await db.run(
                    'UPDATE jobs SET number = 0, sick = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                    [job.id]
                );
            }

            console.log('Synchronized job counts with inhabitants');

            // After synchronizing job counts, recalculate the sickness probability
            // This ensures that the health tab is always up to date
            const HealthService = require('./healthService');
            await HealthService.recalculateSickProbability();
            console.log('Recalculated sickness probability after job count synchronization');
        } catch (error) {
            console.error('Error synchronizing job counts with inhabitants:', error);
            throw error;
        }
    }

    /**
     * Get job ID by name
     * @param {string} jobName - The name of the job
     * @returns {Promise<number|null>} - The job ID or null if not found
     */
    static async getJobIdByName(jobName) {
        try {
            const job = await db.get('SELECT id FROM jobs WHERE name = ?', [jobName]);
            return job ? job.id : null;
        } catch (error) {
            console.error(`Error getting job ID for ${jobName}:`, error);
            throw error;
        }
    }
}

module.exports = InhabitantsService;
