const db = require('../db');
const { DEFAULT_VALUES } = require('../utils/constants');

class MaterialsService {
    /**
     * Calculate materials production based on workers, miners, and modifiers
     * @param {Object} gameState - Current game state
     * @param {Array} jobs - Array of jobs
     * @param {Array} modifierTables - Array of modifier tables
     * @param {Number} moralModifier - Moral modifier value
     * @returns {Number} - Materials production value
     */
    static calculateMaterialsProduction(gameState, jobs, modifierTables, moralModifier) {
        try {
            // Get workers
            const workers = jobs.find(j => j.name === 'Worker') || { number: 0, sick: 0 };
            const activeWorkers = workers.number - workers.sick;

            // Get miners
            const miners = jobs.find(j => j.name === 'Miner') || { number: 0, sick: 0 };
            const activeMiners = miners.number - miners.sick;

            // Get modifier values
            const materialsGeneralModifier = this.getModifierValue(modifierTables, 11);
            const materialsTechModifier = this.getModifierValue(modifierTables, 12);

            // Calculate materials production using the formula:
            // materialsProduction = (activeWorkers * MATERIALS_PER_WORKER + activeMiners * MATERIALS_PER_MINER) *
            //                       (1 + materialsGeneralModifier + materialsTechModifier + moralModifier)
            const materialsProduction =
                (activeWorkers * this.getConfigValue('MATERIALS_PER_WORKER') +
                 activeMiners * this.getConfigValue('MATERIALS_PER_MINER')) *
                (1 + materialsGeneralModifier + materialsTechModifier + moralModifier);

            console.log('Materials production calculation:', {
                activeWorkers,
                activeMiners,
                materialsPerWorker: this.getConfigValue('MATERIALS_PER_WORKER'),
                materialsPerMiner: this.getConfigValue('MATERIALS_PER_MINER'),
                materialsGeneralModifier,
                materialsTechModifier,
                moralModifier,
                result: materialsProduction
            });

            return materialsProduction;
        } catch (error) {
            console.error('Error calculating materials production:', error);
            return 0; // Default to 0 if calculation fails
        }
    }

    /**
     * Get a modifier value from the modifier tables
     * @param {Array} modifierTables - Array of modifier tables
     * @param {Number} tableId - ID of the table to get the value from
     * @returns {Number} - Modifier value
     */
    static getModifierValue(modifierTables, tableId) {
        const table = modifierTables.find(t => t.id === tableId);
        return table ? table.current_value : 0;
    }

    /**
     * Get a configuration value
     * @param {String} name - Configuration name
     * @param {*} defaultValue - Default value if not found
     * @returns {*} - Configuration value
     */
    static getConfigValue(name, defaultValue) {
        // Hardcoded values for materials production
        if (name === 'MATERIALS_PER_WORKER') return 4;
        if (name === 'MATERIALS_PER_MINER') return 2;

        // For other values, use defaults
        return DEFAULT_VALUES[name] || defaultValue;
    }

    /**
     * Update a modifier table's current_value based on its modifiers
     * @param {Number} tableId - ID of the table to update
     * @returns {Promise<void>}
     */
    static async updateModifierTableSum(tableId) {
        try {
            // Get all modifiers for this table
            const modifiers = await db.all('SELECT * FROM modifiers WHERE table_id = ?', [tableId]);

            // Calculate the sum of all modifier effects
            const sum = modifiers.reduce((total, mod) => total + mod.effect, 0);

            // Update the table's current_value
            await db.run(
                'UPDATE modifier_tables SET current_value = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                [sum, tableId]
            );

            console.log(`Updated table ${tableId} current_value to ${sum}`);
        } catch (error) {
            console.error(`Error updating modifier table ${tableId}:`, error);
            throw error;
        }
    }

    /**
     * Update all materials-related modifier tables
     * @returns {Promise<void>}
     */
    static async updateMaterialsModifiers() {
        try {
            // Update both materials-related tables
            await this.updateModifierTableSum(11); // General materials effects
            await this.updateModifierTableSum(12); // Tech materials effects
        } catch (error) {
            console.error('Error updating materials modifiers:', error);
            throw error;
        }
    }

    /**
     * Get all materials-related data
     * @returns {Promise<Object>} - Materials data
     */
    static async getMaterialsData() {
        try {
            // Update all materials-related modifier tables first
            await this.updateMaterialsModifiers();

            // Get all materials-related tables
            const tables = await db.all('SELECT * FROM modifier_tables WHERE id IN (11, 12)');

            // Get modifiers for each table
            const materialsData = await Promise.all(tables.map(async (table) => {
                const modifiers = await db.all(
                    'SELECT * FROM modifiers WHERE table_id = ? ORDER BY id',
                    [table.id]
                );

                return {
                    table,
                    modifiers,
                    total: table.current_value
                };
            }));

            // Get current game state and jobs for calculations
            const gameState = await db.get('SELECT * FROM game_state WHERE id = 1');
            const jobs = await db.all('SELECT * FROM jobs');

            // Get moral modifier
            const moralModifier = gameState.moral_value - 1.0;

            // Calculate materials production
            const modifierTables = await db.all('SELECT * FROM modifier_tables');
            const materialsProduction = this.calculateMaterialsProduction(
                gameState,
                jobs,
                modifierTables,
                moralModifier
            );

            // Get workers and miners
            const workers = jobs.find(j => j.name === 'Worker') || { number: 0, sick: 0 };
            const miners = jobs.find(j => j.name === 'Miner') || { number: 0, sick: 0 };

            return {
                materialsData,
                stats: {
                    workers: workers.number,
                    workers_sick: workers.sick,
                    miners: miners.number,
                    miners_sick: miners.sick,
                    materials_production: materialsProduction,
                    moral_modifier: moralModifier
                }
            };
        } catch (error) {
            console.error('Error getting materials data:', error);
            throw error;
        }
    }
}

module.exports = MaterialsService;
