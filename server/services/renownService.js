const db = require('../db');

class RenownService {
    /**
     * Get renown data including modifiers and stats
     * @returns {Promise<Object>} Renown data
     */
    static async getRenownData() {
        try {
            // Get renown value from game state
            const gameState = await db.get(`
                SELECT renown
                FROM game_state
                WHERE id = 1
            `);

            // Get renown modifiers
            const modifiers = await db.all(`
                SELECT * FROM modifiers
                WHERE table_id = 17
                ORDER BY id DESC
            `);

            // Calculate total renown effect
            const totalRenownEffect = modifiers.reduce((sum, mod) => sum + mod.effect, 0);

            // La renommée totale est égale au total des effets des modificateurs
            const totalRenown = totalRenownEffect;

            return {
                totalRenown: totalRenown,
                renownPerCycle: totalRenownEffect
            };
        } catch (error) {
            console.error('Error getting renown data:', error);
            throw error;
        }
    }

    /**
     * Update renown modifiers
     * @returns {Promise<void>}
     */
    static async updateRenownModifiers() {
        try {
            // Get all modifiers for the renown table (ID 17)
            const modifiers = await db.all(`
                SELECT effect
                FROM modifiers
                WHERE table_id = 17
            `);

            // Calculate total effect
            const totalEffect = modifiers.reduce((sum, mod) => sum + mod.effect, 0);

            // Update the modifier table with the new value
            await db.run(`
                UPDATE modifier_tables
                SET current_value = ?
                WHERE id = 17
            `, [totalEffect]);

            console.log('Renown modifiers updated successfully');
        } catch (error) {
            console.error('Error updating renown modifiers:', error);
            throw error;
        }
    }
}

module.exports = RenownService;
