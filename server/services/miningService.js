const db = require('../db');
const { DEFAULT_VALUES } = require('../utils/constants');

class MiningService {
    /**
     * Calculate mining production based on miners, engineers, and modifiers
     * @param {Object} gameState - Current game state
     * @param {Array} jobs - Array of jobs
     * @param {Array} modifierTables - Array of modifier tables
     * @param {Number} moralModifier - Moral modifier value
     * @returns {Number} - Mining production value
     */
    static calculateMiningProduction(gameState, jobs, modifierTables, moralModifier) {
        try {
            // Get miners
            const miners = jobs.find(j => j.name === 'Miner') || { number: 0, sick: 0 };
            const activeMiner = miners.number - miners.sick;

            // Get engineers for engineering modifier
            const engineers = jobs.find(j => j.name === 'Engineer') || { number: 0, sick: 0 };
            const activeEngineers = engineers.number - engineers.sick;

            // Get modifier values
            const miningGeneralModifier = this.getModifierValue(modifierTables, 4);
            const engineeringModifier = this.getModifierValue(modifierTables, 5);
            const miningTechModifier = this.getModifierValue(modifierTables, 6);

            // Calculate engineering multiplier
            const engineeringMultiplier = activeEngineers * 0.03 * (1 + engineeringModifier);

            // Calculate mining production using the new formula:
            // miningProduction = activeMiner * MINING_VALUE * (1 + generalEffects + techEffects + moralModifier) * (1 + engineeringMultiplier)
            const miningProduction = activeMiner *
                                   this.getConfigValue('MINING_VALUE') *
                                   (1 + miningGeneralModifier + miningTechModifier + moralModifier) *
                                   (1 + engineeringMultiplier);

            console.log('Mining production calculation:', {
                activeMiner,
                miningValue: this.getConfigValue('MINING_VALUE'),
                miningGeneralModifier,
                miningTechModifier,
                engineeringMultiplier,
                moralModifier,
                result: miningProduction
            });

            return miningProduction;
        } catch (error) {
            console.error('Error calculating mining production:', error);
            return 0; // Default to 0 if calculation fails
        }
    }

    /**
     * Get a modifier value from the modifier tables
     * @param {Array} modifierTables - Array of modifier tables
     * @param {Number} tableId - ID of the table to get the value from
     * @returns {Number} - Modifier value
     */
    static getModifierValue(modifierTables, tableId) {
        const table = modifierTables.find(t => t.id === tableId);
        return table ? table.current_value : 0;
    }

    /**
     * Get a configuration value
     * @param {String} name - Configuration name
     * @param {*} defaultValue - Default value if not found
     * @returns {*} - Configuration value
     */
    static getConfigValue(name, defaultValue) {
        // For now, return hardcoded values to avoid async issues
        return DEFAULT_VALUES[name] || defaultValue;
    }

    /**
     * Update a modifier table's current_value based on its modifiers
     * @param {Number} tableId - ID of the table to update
     * @returns {Promise<void>}
     */
    static async updateModifierTableSum(tableId) {
        try {
            // Get all modifiers for this table
            const modifiers = await db.all('SELECT * FROM modifiers WHERE table_id = ?', [tableId]);

            // Calculate the sum of all modifier effects
            const sum = modifiers.reduce((total, mod) => total + mod.effect, 0);

            // Update the table's current_value
            await db.run(
                'UPDATE modifier_tables SET current_value = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                [sum, tableId]
            );

            console.log(`Updated table ${tableId} current_value to ${sum}`);
        } catch (error) {
            console.error(`Error updating modifier table ${tableId}:`, error);
            throw error;
        }
    }

    /**
     * Update all mining-related modifier tables
     * @returns {Promise<void>}
     */
    static async updateMiningModifiers() {
        try {
            // Update all three mining-related tables
            await this.updateModifierTableSum(4); // General mining effects
            await this.updateModifierTableSum(5); // Engineering effects
            await this.updateModifierTableSum(6); // Tech mining effects
        } catch (error) {
            console.error('Error updating mining modifiers:', error);
            throw error;
        }
    }

    /**
     * Get all mining-related data
     * @returns {Promise<Object>} - Mining data
     */
    static async getMiningData() {
        try {
            // Update all mining-related modifier tables first
            await this.updateMiningModifiers();

            // Get all mining-related tables
            const tables = await db.all('SELECT * FROM modifier_tables WHERE id IN (4, 5, 6)');

            // Get modifiers for each table
            const miningData = await Promise.all(tables.map(async (table) => {
                const modifiers = await db.all(
                    'SELECT * FROM modifiers WHERE table_id = ? ORDER BY id',
                    [table.id]
                );

                return {
                    table,
                    modifiers,
                    total: table.current_value
                };
            }));

            // Get current game state and jobs for calculations
            const gameState = await db.get('SELECT * FROM game_state WHERE id = 1');
            const jobs = await db.all('SELECT * FROM jobs');

            // Get moral modifier
            const moralModifier = gameState.moral_value - 1.0;

            // Calculate mining production
            const modifierTables = await db.all('SELECT * FROM modifier_tables');
            const miningProduction = this.calculateMiningProduction(
                gameState,
                jobs,
                modifierTables,
                moralModifier
            );

            // Get miners and engineers
            const miners = jobs.find(j => j.name === 'Miner') || { number: 0, sick: 0 };
            const engineers = jobs.find(j => j.name === 'Engineer') || { number: 0, sick: 0 };

            return {
                miningData,
                stats: {
                    miners: miners.number,
                    miners_sick: miners.sick,
                    engineers: engineers.number,
                    engineers_sick: engineers.sick,
                    mining_production: miningProduction,
                    moral_modifier: moralModifier
                }
            };
        } catch (error) {
            console.error('Error getting mining data:', error);
            throw error;
        }
    }
}

module.exports = MiningService;
