const db = require('../db');
const { MORAL_TITLES } = require('../utils/constants');

class MoralService {
    /**
     * Calcule la valeur globale du moral en tenant compte de tous les facteurs
     * @returns {Promise<number>} - Valeur globale du moral
     */
    static async calculateMoralGlobalValue() {
        try {
            // 1. Obtenir les modificateurs de moral depuis la table
            const moralModifiers = await db.all('SELECT * FROM modifiers WHERE table_id = 10');

            // 2. Calculer la somme des effets des modificateurs
            const moralModifiersSum = moralModifiers.reduce((sum, mod) => sum + mod.effect, 0);

            // 3. Obtenir les données nécessaires pour les calculs de logement et population
            const gameState = await db.get('SELECT dwellings FROM game_state WHERE id = 1');
            const jobs = await db.all('SELECT * FROM jobs');

            // 4. Calculer le nombre total d'habitants
            const totalInhabitants = jobs.reduce((sum, job) => sum + job.number, 0);

            // 5. Calculer les habitations disponibles
            const dwellingsAvailable = gameState.dwellings - totalInhabitants;

            // 6. Calculer l'effet du logement: IF(DwellingsAvailable < 0; DwellingsAvailable * 10; 0)
            const housingEffect = dwellingsAvailable < 0 ? dwellingsAvailable * 10 : 0;

            // 7. Calculer l'effet de la population: MIN(0; 150 + (-5 * TotalInhabitants))
            const populationEffect = Math.min(0, 150 + (-5 * totalInhabitants));

            // 8. Calculer la valeur globale du moral
            const moralGlobalValue = 1000 + moralModifiersSum + housingEffect + populationEffect;

            // 9. Mettre à jour la table des modificateurs
            await db.run(
                'UPDATE modifier_tables SET current_value = ?, updated_at = CURRENT_TIMESTAMP WHERE id = 10',
                [moralGlobalValue]
            );

            return moralGlobalValue;
        } catch (error) {
            console.error('Error calculating moral global value:', error);
            return 1000; // Valeur par défaut en cas d'erreur
        }
    }

    /**
     * Calcule le modificateur de moral basé sur la valeur globale
     * @param {number} moralGlobalValue - Valeur globale du moral
     * @returns {number} - Modificateur de moral
     */
    static calculateMoralModifier(moralGlobalValue) {
        // Formule: (2 * NORMDIST(MoralGlobalValue, 1000, 1000, TRUE)) - 1
        const normDist = this.normalDistribution(moralGlobalValue, 1000, 1000);
        console.log(`Calculating moral modifier with moralGlobalValue: ${moralGlobalValue}`);
        console.log(`Normal distribution result: ${normDist}`);

        // Pour un moral de 1000, le modificateur devrait être 0 (neutre)
        // Pour un moral > 1000, le modificateur devrait être > 0 (bonus)
        // Pour un moral < 1000, le modificateur devrait être < 0 (malus)
        const moralModifier = (2 * normDist) - 1;

        console.log(`Final moral modifier: ${moralModifier}`);
        return moralModifier;
    }

    /**
     * Calcule la distribution normale cumulative
     * @param {number} x - Valeur à évaluer
     * @param {number} mean - Moyenne de la distribution
     * @param {number} stdDev - Écart-type de la distribution
     * @returns {number} - Valeur de la distribution normale cumulative
     */
    static normalDistribution(x, mean, stdDev) {
        // Fonction de répartition de la loi normale
        return 0.5 * (1 + this.erf((x - mean) / (stdDev * Math.sqrt(2))));
    }

    /**
     * Approximation de la fonction d'erreur
     * @param {number} x - Valeur à évaluer
     * @returns {number} - Approximation de erf(x)
     */
    static erf(x) {
        // Constants
        const a1 =  0.254829592;
        const a2 = -0.284496736;
        const a3 =  1.421413741;
        const a4 = -1.453152027;
        const a5 =  1.061405429;
        const p  =  0.3275911;

        // Save the sign of x
        const sign = (x < 0) ? -1 : 1;
        x = Math.abs(x);

        // Abramowitz and Stegun formula 7.1.26
        const t = 1.0 / (1.0 + p * x);
        const y = 1.0 - (((((a5 * t + a4) * t) + a3) * t + a2) * t + a1) * t * Math.exp(-x * x);

        return sign * y;
    }

    /**
     * Détermine le titre du moral en fonction de la valeur du modificateur
     * @param {number} moralValue - Valeur du moral
     * @returns {string} - Titre du moral
     */
    static getMoralTitle(moralValue) {
        for (const title of MORAL_TITLES) {
            if (moralValue >= title.min && moralValue < title.max) {
                return title.title;
            }
        }

        return 'Neutre'; // Valeur par défaut
    }

    /**
     * Met à jour la valeur du moral dans la base de données
     * @param {number} moralValue - Nouvelle valeur du moral
     * @returns {Promise<void>}
     */
    static async updateMoralValue(moralValue) {
        try {
            const moralTitle = this.getMoralTitle(moralValue);

            await db.run(`
                UPDATE game_state
                SET moral_value = ?,
                    moral_title = ?,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = 1
            `, [moralValue, moralTitle]);
        } catch (error) {
            console.error('Error updating moral value:', error);
        }
    }

    /**
     * Récupère toutes les données liées au moral
     * @returns {Promise<Object>} - Données de moral
     */
    static async getMoralData() {
        try {
            // Calculer la valeur globale du moral
            const moralGlobalValue = await this.calculateMoralGlobalValue();

            // Récupérer la table de moral
            const table = await db.get('SELECT * FROM modifier_tables WHERE id = 10');

            // Récupérer les modificateurs de moral
            const modifiers = await db.all('SELECT * FROM modifiers WHERE table_id = 10 ORDER BY start_date DESC, id DESC');

            // Récupérer les statistiques de moral
            const gameState = await db.get('SELECT moral_value, moral_title, dwellings FROM game_state WHERE id = 1');
            const jobs = await db.all('SELECT * FROM jobs');

            // Calculer les statistiques supplémentaires
            const totalInhabitants = jobs.reduce((sum, job) => sum + job.number, 0);
            const dwellingsAvailable = gameState.dwellings - totalInhabitants;
            const housingEffect = dwellingsAvailable < 0 ? dwellingsAvailable * 10 : 0;
            const populationEffect = Math.min(0, 150 + (-5 * totalInhabitants));

            // Calculer le modificateur de moral basé sur la valeur globale
            const moralModifier = this.calculateMoralModifier(table.current_value);

            // Convertir le modificateur en valeur multiplicative pour la base de données
            // Pour un modificateur de 0 (neutre), la valeur sera 1.0
            // Pour un modificateur positif, la valeur sera > 1.0
            // Pour un modificateur négatif, la valeur sera < 1.0
            const moralValue = moralModifier + 1.0;

            // Mettre à jour la valeur du moral dans la base de données
            await this.updateMoralValue(moralValue);

            // Créer l'objet de réponse
            return {
                table,
                modifiers,
                total: table.current_value,
                stats: {
                    moral_value: moralValue,
                    moral_modifier: moralModifier, // Ajout du modificateur brut pour référence
                    moral_title: this.getMoralTitle(moralValue),
                    dwellings: gameState.dwellings,
                    total_inhabitants: totalInhabitants,
                    dwellings_available: dwellingsAvailable,
                    housing_effect: housingEffect,
                    population_effect: populationEffect
                }
            };
        } catch (error) {
            console.error('Error getting moral data:', error);
            throw error;
        }
    }
}

module.exports = MoralService;
