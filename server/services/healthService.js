const db = require('../db');

class HealthService {
    /**
     * Get all health-related data including modifiers and statistics
     * @returns {Promise<Object>} Health data
     */
    static async getHealthData() {
        try {
            // Get health modifiers table (ID 13)
            const healthModifiersTable = await db.get('SELECT * FROM modifier_tables WHERE id = 13');

            // Get all health modifiers
            const healthModifiers = await db.all('SELECT * FROM modifiers WHERE table_id = 13 ORDER BY id');

            // Get game state for health statistics
            const gameState = await db.get('SELECT * FROM game_state WHERE id = 1');

            // Get jobs to calculate sick people
            const jobs = await db.all('SELECT * FROM jobs');

            // Calculate health statistics
            const totalPopulation = jobs.reduce((sum, job) => sum + job.number, 0);
            const totalSick = jobs.reduce((sum, job) => sum + job.sick, 0);
            const sickPercentage = totalPopulation > 0 ? (totalSick / totalPopulation) * 100 : 0;

            // Get healers count
            const healers = jobs.find(job => job.name === 'Healer') || { number: 0, sick: 0 };
            const activeHealers = healers.number - healers.sick;

            return {
                modifiersTable: healthModifiersTable,
                modifiers: healthModifiers,
                stats: {
                    totalPopulation,
                    totalSick,
                    sickPercentage,
                    sickProbability: gameState.sick_probability || 0,
                    activeHealers
                }
            };
        } catch (error) {
            console.error('Error in getHealthData:', error);
            throw error;
        }
    }

    /**
     * Update the sum of health modifiers
     * @returns {Promise<void>}
     */
    static async updateHealthModifiers() {
        try {
            await db.run(`
                UPDATE modifier_tables
                SET current_value = (
                    SELECT SUM(effect)
                    FROM modifiers
                    WHERE table_id = 13
                ),
                updated_at = CURRENT_TIMESTAMP
                WHERE id = 13
            `);

            // After updating the modifiers, recalculate the sickness probability
            await this.recalculateSickProbability();
        } catch (error) {
            console.error('Error updating health modifiers:', error);
            throw error;
        }
    }

    /**
     * Recalculate and update the sickness probability based on current data
     * @returns {Promise<number>} The new sickness probability
     */
    static async recalculateSickProbability() {
        try {
            // Get all jobs to calculate total inhabitants and active healers
            const jobs = await db.all('SELECT * FROM jobs');

            // Calculate total inhabitants
            const totalInhabitants = jobs.reduce((sum, job) => sum + job.number, 0);

            // Get healers count
            const healers = jobs.find(job => job.name === 'Healer') || { number: 0, sick: 0 };
            const activeHealers = healers.number - healers.sick;

            // Get health modifier
            const healthModifierTable = await db.get('SELECT * FROM modifier_tables WHERE id = 13');
            const healthModifier = healthModifierTable?.current_value || 0;

            // Calculate sickness probability using the same formula as in cycleService.js
            const sickProbability = Math.max(0, 0.25 / (1 + Math.exp(-0.1 * (totalInhabitants - (18 * activeHealers * (1 + healthModifier))))));

            // Update the game state with the new probability
            await db.run(`
                UPDATE game_state
                SET sick_probability = ?,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = 1
            `, [sickProbability]);

            return sickProbability;
        } catch (error) {
            console.error('Error recalculating sick probability:', error);
            throw error;
        }
    }
}

module.exports = HealthService;
