const db = require('../db');
const { MONTHS, SEASONS, MORAL_TITLES } = require('../utils/constants');
const FoodService = require('./foodService');
const InhabitantsService = require('./inhabitantsService');
const MaterialsService = require('./materialsService');
const ChargesService = require('./chargesService');
const JusticeDefenseService = require('./justiceDefenseService');

class CycleService {
    static async processCycle() {
        try {
            // Fix duplicate "Base" entries in perishable rate table
            await FoodService.fixDuplicatePerishableEntries();

            // Update all food-related modifier tables
            await FoodService.updateFoodModifiers();

            // Get current game state and all necessary data
            const gameState = await db.get('SELECT * FROM game_state WHERE id = 1');
            const jobs = await db.all('SELECT * FROM jobs');
            const config = await db.all('SELECT * FROM game_config');
            const modifierTables = await db.all('SELECT * FROM modifier_tables');

            // Calculate base values
            const totalInhabitants = jobs.reduce((sum, job) => sum + (job.number - job.sick), 0);

            // La valeur du moral est déjà au format multiplicatif (1.0 = neutre)
            // Pour l'utiliser comme modificateur, on soustrait 1.0
            const moralModifier = gameState.moral_value - 1.0;
            console.log('Using moral modifier in processCycle:', moralModifier);

            // Process all calculations in order
            const results = await this.calculateAll(gameState, jobs, config, modifierTables, moralModifier, totalInhabitants);

            // Update game state with new values
            await this.updateGameState(results);

            // Log events
            await this.logEvents(results.events);

            return results;
        } catch (error) {
            console.error('Error processing cycle:', error);
            throw error;
        }
    }

    static async calculateAll(gameState, jobs, config, modifierTables, moralModifier, totalInhabitants) {
        // Get modifier values
        const getModifierValue = (id) => {
            const table = modifierTables.find(t => t.id === id);
            return table ? table.current_value : 0;
        };

        // Get renown value (ID 17 in constants.js)
        const renownValue = getModifierValue(17);

        // Get config value
        const getConfigValue = (name) => {
            const conf = config.find(c => c.name === name);
            return conf ? parseFloat(conf.value) : 0;
        };

        // Calculate probabilities
        const probabilities = await this.calculateProbabilities(jobs, modifierTables, totalInhabitants);

        // Process random events
        const events = await this.processRandomEvents(probabilities);

        // Calculate production
        const production = await this.calculateProduction(
            jobs,
            gameState,
            moralModifier,
            getModifierValue,
            getConfigValue
        );

        // Calculate consumption and charges
        const consumption = await this.calculateConsumption(
            jobs,
            getModifierValue
        );

        // Calculate new reserves
        const newReserves = await this.calculateNewReserves(
            gameState,
            production,
            consumption,
            getConfigValue
        );

        return {
            probabilities,
            events,
            production,
            consumption,
            newReserves,
            renown: renownValue
        };
    }

    static async calculateProbabilities(jobs, modifierTables, totalInhabitants) {
        const healers = jobs.find(j => j.name === 'Healer') || { number: 0, sick: 0 };
        const activeHealers = healers.number - healers.sick;
        // Health modifier is ID 13 in constants.js
        const healthModifier = modifierTables.find(t => t.id === 13)?.current_value || 0;

        console.log('DEBUG - Calculating sickness probability with:', {
            totalInhabitants,
            activeHealers,
            healthModifier
        });

        // Calculate crime probability using JusticeDefenseService
        const crimeProbability = JusticeDefenseService.calculateCrimeProbability(
            { total_inhabitants: totalInhabitants },
            jobs,
            modifierTables
        );

        // Calculate army strength using JusticeDefenseService
        const armyStrength = JusticeDefenseService.calculateArmyStrength(
            { inhabitants_to_pay: totalInhabitants },
            jobs,
            modifierTables
        );

        // Get justice and army modifiers
        const justiceModifier = modifierTables.find(t => t.id === 19)?.current_value || 0;
        const armyModifier = modifierTables.find(t => t.id === 20)?.current_value || 0;

        const scholars = jobs.find(j => j.name === 'Scholar') || { number: 0, sick: 0 };
        const activeScholars = scholars.number - scholars.sick;
        // Research modifier is ID 16 in constants.js
        const researchModifier = modifierTables.find(t => t.id === 16)?.current_value || 0;

        // Get tech level from game state
        const gameState = await db.get('SELECT tech_level FROM game_state WHERE id = 1');
        const techLevel = gameState?.tech_level || 0;

        // Calculate sickness probability using the formula from base prompt
        const sicknessProbability = Math.max(0, 0.25 / (1 + Math.exp(-0.1 * (totalInhabitants - (18 * activeHealers * (1 + healthModifier))))));

        console.log('DEBUG - Calculated sickness probability:', sicknessProbability);
        console.log('DEBUG - Formula breakdown:', {
            formula: "Math.max(0, 0.25 / (1 + Math.exp(-0.1 * (totalInhabitants - (18 * activeHealers * (1 + healthModifier))))))",
            totalInhabitants,
            activeHealers,
            healthModifier,
            healerEffect: 18 * activeHealers * (1 + healthModifier),
            difference: totalInhabitants - (18 * activeHealers * (1 + healthModifier)),
            expTerm: Math.exp(-0.1 * (totalInhabitants - (18 * activeHealers * (1 + healthModifier)))),
            denominator: 1 + Math.exp(-0.1 * (totalInhabitants - (18 * activeHealers * (1 + healthModifier)))),
            result: sicknessProbability
        });

        return {
            // Formula from base prompt: MAX(0; 0,25/(1+EXP(-0,1*(TotalInhabitants-18*HealerNumber*(1+TotalHealthModifier)))))
            sickness: sicknessProbability,

            // Use the crime probability calculated by JusticeDefenseService
            crime: crimeProbability,

            // Add army strength to probabilities
            armyStrength: armyStrength,

            // Add justice and army modifiers
            justiceModifier: justiceModifier,
            armyModifier: armyModifier,

            // Formula from base prompt: MAX(0,5/(1+EXP(-0,33*(ScholarNumber-ScholarSick)*(1+0,05*TechLevel*TotalResearchModifier)))-(0,6/(1+EXP(0,2*TechLevel)));0)+0,01
            research: Math.max(
                0.5 / (1 + Math.exp(-0.33 * activeScholars * (1 + 0.05 * techLevel * researchModifier))) - (0.6 / (1 + Math.exp(0.2 * techLevel))),
                0
            ) + 0.01
        };
    }

    static async processRandomEvents(probabilities) {
        const events = [];

        // Note: Sickness is now handled differently - we apply it to each inhabitant individually
        // in the updateGameState method, not as a global event here

        // Process crime with more detailed descriptions
        if (Math.random() < probabilities.crime) {
            const crimeDescriptions = [
                'Un crime a été commis! Les protecteurs enquêtent sur l\'affaire.',
                'Un vol important a été signalé dans les quartiers des mineurs.',
                'Une bagarre a éclaté entre plusieurs habitants, causant des blessures.',
                'Des actes de vandalisme ont été découverts près des entrepôts.'
            ];

            events.push({
                type: 'CRIME',
                description: crimeDescriptions[Math.floor(Math.random() * crimeDescriptions.length)]
            });
        }

        // Process research with more detailed descriptions
        if (Math.random() < probabilities.research) {
            const researchDescriptions = [
                'EUREKA! Une nouvelle technologie a été découverte par nos érudits!',
                'Après des semaines de recherche, nos érudits ont fait une percée significative!',
                'Une innovation prometteuse vient d\'être mise au point dans nos ateliers.',
                'Un érudit a eu une révélation qui pourrait changer notre façon de travailler!'
            ];

            events.push({
                type: 'RESEARCH',
                description: researchDescriptions[Math.floor(Math.random() * researchDescriptions.length)]
            });
        }

        // Check for famine event
        const gameState = await db.get('SELECT food_reserves FROM game_state WHERE id = 1');
        if (gameState && gameState.food_reserves <= 10) {
            events.push({
                type: 'FAMINE',
                description: 'Les réserves de nourriture sont dangereusement basses! La famine menace la colonie.'
            });
        }

        // Check for treasury event
        if (gameState && gameState.treasure <= 100) {
            events.push({
                type: 'TREASURY',
                description: 'Le trésor se vide rapidement! Des mesures d\'austérité pourraient être nécessaires.'
            });
        }

        return events;
    }

    static calculateMoralModifier(moralValue) {
        console.log('Calculating moral modifier with moralValue:', moralValue);
        const normDist = this.normalDistribution(moralValue, 1000, 1000);
        console.log('Normal distribution result:', normDist);
        const modifier = (2 * normDist) - 1;
        console.log('Final moral modifier:', modifier);
        return modifier;
    }

    static normalDistribution(x, mean, stdDev, cumulative = true) {
        if (cumulative) {
            // Cumulative distribution function (CDF)
            return 0.5 * (1 + this.erf((x - mean) / (stdDev * Math.sqrt(2))));
        } else {
            // Probability density function (PDF)
            return (1 / (stdDev * Math.sqrt(2 * Math.PI))) *
                   Math.exp(-0.5 * Math.pow((x - mean) / stdDev, 2));
        }
    }

    // Error function approximation needed for cumulative normal distribution
    static erf(x) {
        // Constants
        const a1 =  0.254829592;
        const a2 = -0.284496736;
        const a3 =  1.421413741;
        const a4 = -1.453152027;
        const a5 =  1.061405429;
        const p  =  0.3275911;

        // Save the sign of x
        const sign = (x < 0) ? -1 : 1;
        x = Math.abs(x);

        // Abramowitz and Stegun formula 7.1.26
        const t = 1.0 / (1.0 + p * x);
        const y = 1.0 - (((((a5 * t + a4) * t) + a3) * t + a2) * t + a1) * t * Math.exp(-x * x);

        return sign * y;
    }

    static async calculateProduction(jobs, gameState, moralModifier, getModifierValue, getConfigValue) {
        // Get all modifier tables for calculations
        const modifierTables = await db.all('SELECT * FROM modifier_tables');

        // Calculate food production using FoodService
        let foodProduction;
        try {
            // Assurons-nous que le modificateur de moral est correct
            console.log('Moral modifier used for production:', moralModifier);

            foodProduction = FoodService.calculateFoodProduction(
                gameState,
                jobs,
                modifierTables,
                moralModifier
            );
            console.log('Food production calculated:', foodProduction);
        } catch (error) {
            console.error('Error calculating food production:', error);
            foodProduction = 0; // Default to 0 if calculation fails
        }

        // Calculate mining production
        const miners = jobs.find(j => j.name === 'Miner') || { number: 0, sick: 0 };
        const activeMiner = miners.number - miners.sick;

        // Mining modifiers: 4 = general mining effects, 6 = tech mining effects
        // Engineering modifier calculation
        const engineers = jobs.find(j => j.name === 'Engineer') || { number: 0, sick: 0 };
        const activeEngineers = engineers.number - engineers.sick;
        const engineeringMultiplier = activeEngineers * 0.03 * (1 + getModifierValue(5)); // 5 = engineering modifier

        const miningProduction = activeMiner *
                               getConfigValue('MINING_VALUE') *
                               (1 + getModifierValue(4) + getModifierValue(6) + moralModifier) *
                               (1 + engineeringMultiplier);

        // Calculate materials production using MaterialsService
        const materialsProduction = MaterialsService.calculateMaterialsProduction(
            gameState,
            jobs,
            modifierTables,
            moralModifier
        );

        // Calculate trading revenue
        const craftsmen = jobs.find(j => j.name === 'Craftsman') || { number: 0, sick: 0 };
        const activeCraftsmen = craftsmen.number - craftsmen.sick;

        // Trading modifier: 18 = trading effects
        const tradingRevenue = activeCraftsmen *
                             Math.max(0, 1 + getModifierValue(18)) *
                             getConfigValue('TRADING_VALUE');

        return {
            foodProduction,
            miningProduction,
            materialsProduction,
            tradingRevenue,
            engineeringMultiplier
        };
    }

    static async calculateConsumption(jobs, getModifierValue) {
        // Get all modifier tables for calculations
        const modifierTables = await db.all('SELECT * FROM modifier_tables');

        // Get game state
        const gameState = await db.get('SELECT * FROM game_state WHERE id = 1');

        // Calculate charges using ChargesService
        const chargesData = await ChargesService.calculateCharges(gameState, jobs, modifierTables);

        // Calculate food consumption using FoodService
        const foodConsumption = FoodService.calculateFoodConsumption(jobs);

        return {
            salaries: chargesData.salaries,
            charges: chargesData.nonSalaryCharges,
            foodConsumption,
            craftsmanEffect: chargesData.craftsmanEffect,
            chargesData: chargesData // Include full charges data for reference
        };
    }

    static async calculateNewReserves(gameState, production, consumption, getConfigValue) {
        // Get all modifier tables for calculations
        const modifierTables = await db.all('SELECT * FROM modifier_tables');

        // Log the inputs for debugging
        console.log('calculateNewReserves inputs:', {
            currentFoodReserves: gameState.food_reserves,
            foodProduction: production.foodProduction,
            foodConsumption: consumption.foodConsumption
        });

        // Calculate new food reserves using FoodService
        const foodResult = await FoodService.calculateNewFoodReserves(
            gameState.food_reserves,
            production.foodProduction,
            consumption.foodConsumption,
            modifierTables
        );

        // Calculate new treasure
        const newTreasure = Math.max(0, gameState.treasure +
                                  production.miningProduction +
                                  production.tradingRevenue -
                                  consumption.salaries -
                                  consumption.charges);

        // Calculate new materials
        const newMaterials = gameState.materials + production.materialsProduction;

        // Calculate new mine depth based on total production
        const newTotalMineProduction = (gameState.total_mine_production || 0) + production.miningProduction;
        const newMineDepth = Math.floor(newTotalMineProduction / 1000);

        return {
            newReserves: {
                food: foodResult.newReserves,
                treasure: newTreasure,
                materials: newMaterials,
                totalMineProduction: newTotalMineProduction,
                mineDepth: newMineDepth
            },
            totalMineProduction: newTotalMineProduction,
            mineDepth: newMineDepth,
            perishableRate: foodResult.perishableRate,
            hasFamine: foodResult.hasFamine
        };
    }

    static async updateGameState(results) {
        // Get current game state
        const gameState = await db.get('SELECT * FROM game_state WHERE id = 1');

        // Set default values for null fields only on first run
        if (gameState.cycle_number === 1) {
            if (gameState.food_reserves === null) {
                console.log('Setting initial food reserves to 58.0');
                await db.run('UPDATE game_state SET food_reserves = 58.0 WHERE id = 1');
                gameState.food_reserves = 58.0;
            }

            if (gameState.treasure === null) {
                console.log('Setting initial treasure to 1000');
                await db.run('UPDATE game_state SET treasure = 1000 WHERE id = 1');
                gameState.treasure = 1000;
            }

            if (gameState.materials === null) {
                console.log('Setting initial materials to 100');
                await db.run('UPDATE game_state SET materials = 100 WHERE id = 1');
                gameState.materials = 100;
            }
        } else {
            // For subsequent cycles, make sure we don't reset to default values
            if (gameState.food_reserves === null) {
                gameState.food_reserves = 0;
            }
            if (gameState.treasure === null) {
                gameState.treasure = 0;
            }
            if (gameState.materials === null) {
                gameState.materials = 0;
            }
        }

        // Handle month and season changes
        let newMonthIndex = gameState.month_index + 1;
        if (newMonthIndex >= MONTHS.length) {
            newMonthIndex = 0;
        }

        const newMonth = MONTHS[newMonthIndex].mois;
        const newMonthDescription = MONTHS[newMonthIndex].description;

        // Determine season and season factor based on the new month indices
        let newSeason, newSeasonFactor;
        if (newMonthIndex === 11 || newMonthIndex < 2) {
            newSeason = SEASONS.WINTER.name;
            newSeasonFactor = SEASONS.WINTER.factor;
        } else if (newMonthIndex >= 2 && newMonthIndex < 5) {
            newSeason = SEASONS.SPRING.name;
            newSeasonFactor = SEASONS.SPRING.factor;
        } else if (newMonthIndex >= 5 && newMonthIndex < 8) {
            newSeason = SEASONS.SUMMER.name;
            newSeasonFactor = SEASONS.SUMMER.factor;
        } else {
            newSeason = SEASONS.AUTUMN.name;
            newSeasonFactor = SEASONS.AUTUMN.factor;
        }

        // Handle year change
        let newYear = gameState.year;
        if (newMonthIndex === 0 && gameState.month_index === 11) {
            newYear += 1;
        }

        // Update moral title based on moral value
        let moralTitle = 'Neutre';
        for (const title of MORAL_TITLES) {
            if (gameState.moral_value >= title.min && gameState.moral_value < title.max) {
                moralTitle = title.title;
                break;
            }
        }

        // Update game state with all new values
        // Ensure we have valid numbers for food reserves
        let foodReserves = 0;

        // Check if we have valid new reserves from the calculation
        console.log('Checking newReserves:', results);

        if (results.newReserves && results.newReserves.newReserves && typeof results.newReserves.newReserves.food === 'number' && !isNaN(results.newReserves.newReserves.food)) {
            foodReserves = Math.max(0, results.newReserves.newReserves.food);
            console.log('Using calculated food reserves:', foodReserves);
        }
        // If not, check if we have valid current reserves
        else if (gameState.food_reserves && typeof gameState.food_reserves === 'number' && !isNaN(gameState.food_reserves)) {
            // If we don't have valid new reserves, use the current reserves minus consumption
            const consumption = 30; // Hardcoded for now
            foodReserves = Math.max(0, gameState.food_reserves - consumption);
            console.log('Using fallback food reserves calculation:', foodReserves);
        }
        // Otherwise, just use 0 (don't set a default value)
        else {
            foodReserves = 0;
            console.log('No valid food reserves found, using 0');
        }

        console.log('Updating food reserves to:', foodReserves);

        try {
            await db.run(`
                UPDATE game_state
                SET cycle_number = cycle_number + 1,
                    month = ?,
                    month_index = ?,
                    month_description = ?,
                    year = ?,
                    season = ?,
                    season_factor = ?,
                    food_reserves = ?,
                    treasure = ?,
                    materials = ?,
                    total_mine_production = ?,
                    mine_depth = ?,
                    moral_title = ?,
                    sick_probability = ?,
                    crime_probability = ?,
                    research_probability = ?,
                    renown = ?,
                    army_strength = ?,
                    justice_modifier = ?,
                    army_modifier = ?,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = 1
            `, [
                newMonth,
                newMonthIndex,
                newMonthDescription,
                newYear,
                newSeason,
                newSeasonFactor,
                foodReserves,
                results.newReserves.newReserves.treasure,
                results.newReserves.newReserves.materials,
                results.newReserves.newReserves.totalMineProduction,
                results.newReserves.newReserves.mineDepth,
                moralTitle,
                results.probabilities.sickness,
                results.probabilities.crime,
                results.probabilities.research,
                results.renown,
                results.probabilities.armyStrength,
                results.probabilities.justiceModifier,
                results.probabilities.armyModifier
            ]);

            // Verify the update
            const updatedState = await db.get('SELECT food_reserves FROM game_state WHERE id = 1');
            console.log('Verified food reserves after update:', updatedState.food_reserves);
        } catch (error) {
            console.error('Error updating game state:', error);
        }

        // Apply sickness to individual inhabitants at each cycle
        console.log('DEBUG - Applying sickness to individual inhabitants with probability:', results.probabilities.sickness);
        const sickResults = await InhabitantsService.applySickness(results.probabilities.sickness);
        console.log('DEBUG - Applied sickness to inhabitants:', sickResults);

        // If any inhabitants got sick, add a sickness event to the log
        if (sickResults.sickCount > 0) {
            const sicknessDescriptions = [
                'Une maladie se propage dans la colonie! Plusieurs travailleurs sont alités.',
                'Une épidémie de fièvre a touché plusieurs habitants de la mine.',
                'Des symptômes inquiétants se manifestent chez certains travailleurs. Les guérisseurs sont débordés.',
                'Une toux persistante se répand parmi les mineurs. La productivité en souffre.'
            ];

            await this.logEvents([{
                type: 'SICKNESS',
                description: sicknessDescriptions[Math.floor(Math.random() * sicknessDescriptions.length)]
            }]);

            console.log('DEBUG - Added sickness event to log because', sickResults.sickCount, 'inhabitants got sick');
        } else {
            console.log('DEBUG - No inhabitants got sick this cycle');
        }

        // Synchronize inhabitants with jobs after cycle
        await InhabitantsService.syncWithJobs();
    }

    static async logEvents(events) {
        for (const event of events) {
            await db.run(`
                INSERT INTO events_log (cycle, event_type, description)
                SELECT cycle_number, ?, ?
                FROM game_state
                WHERE id = 1
            `, [event.type, event.description]);
        }
    }
}

module.exports = CycleService;