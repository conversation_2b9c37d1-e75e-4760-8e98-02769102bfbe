const db = require('../db');

/**
 * <PERSON><PERSON><PERSON><PERSON> pour les moyens de défense
 */
class DefenseMean {
  /**
   * Récupérer tous les moyens de défense
   * @returns {Promise<Array>} Liste des moyens de défense
   */
  static async getAll() {
    return new Promise((resolve, reject) => {
      const sql = `SELECT * FROM defense_means ORDER BY id DESC`;
      
      db.all(sql, [], (err, rows) => {
        if (err) {
          console.error('Error getting defense means:', err);
          reject(err);
          return;
        }
        
        resolve(rows);
      });
    });
  }
  
  /**
   * Ajouter un nouveau moyen de défense
   * @param {Object} defenseMean Moyen de défense à ajouter
   * @returns {Promise<Object>} Moyen de défense ajouté avec son ID
   */
  static async add(defenseMean) {
    return new Promise((resolve, reject) => {
      const { title, description } = defenseMean;
      const sql = `INSERT INTO defense_means (title, description) VALUES (?, ?)`;
      
      db.run(sql, [title, description], function(err) {
        if (err) {
          console.error('Error adding defense mean:', err);
          reject(err);
          return;
        }
        
        // Récupérer le moyen de défense ajouté
        db.get(`SELECT * FROM defense_means WHERE id = ?`, [this.lastID], (err, row) => {
          if (err) {
            console.error('Error getting added defense mean:', err);
            reject(err);
            return;
          }
          
          resolve(row);
        });
      });
    });
  }
  
  /**
   * Supprimer un moyen de défense
   * @param {Number} id ID du moyen de défense à supprimer
   * @returns {Promise<Object>} Résultat de la suppression
   */
  static async delete(id) {
    return new Promise((resolve, reject) => {
      const sql = `DELETE FROM defense_means WHERE id = ?`;
      
      db.run(sql, [id], function(err) {
        if (err) {
          console.error(`Error deleting defense mean ${id}:`, err);
          reject(err);
          return;
        }
        
        resolve({ id, deleted: this.changes > 0 });
      });
    });
  }
}

module.exports = DefenseMean;
