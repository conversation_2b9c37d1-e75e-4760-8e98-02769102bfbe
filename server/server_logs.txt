Database schema initialized
Game data initialized
Server running on port 3001
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 3,
  foodPerFarmer: 4,
  seasonFactor: 1.25,
  foodGeneralModifier: 2,
  foodTechModifier: 0,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 45.000000015
Food production calculated: 45.000000015
Food consumption calculation: { totalInhabitants: 8, consumption: 8 }
calculateNewReserves inputs: {
  currentFoodReserves: 577.7,
  foodProduction: 45.000000015,
  foodConsumption: 8
}
Perishable modifiers: {
  modifiers: [
    {
      id: 1,
      table_id: 3,
      title: 'Base',
      effect: 0.2,
      description: 'Péremption naturelle',
      start_date: null,
      created_at: '2025-04-23 22:53:21',
      updated_at: '2025-04-23 22:53:21'
    },
    {
      id: 6,
      table_id: 3,
      title: 'adad',
      effect: -0.2,
      description: '',
      start_date: null,
      created_at: '2025-04-24 12:34:37',
      updated_at: '2025-04-24 17:09:04'
    }
  ],
  tableValue: 0,
  calculatedSum: 0
}
Food reserves calculation: {
  currentReserves: 577.7,
  perishableRate: 0,
  foodLostToPerishable: 0,
  remainingAfterPerishable: 577.7,
  production: 45.000000015,
  consumption: 8,
  netProductionConsumption: 37.000000015,
  newReservesBeforeMax: 614.7,
  newReserves: 614.7
}
Checking newReserves: {
  probabilities: {
    sickness: 0.0003752955641842479,
    crime: 0.10022816582522492,
    research: 0.01
  },
  events: [],
  production: {
    foodProduction: 45.000000015,
    miningProduction: 0,
    materialsProduction: 0,
    tradingRevenue: 0,
    engineeringMultiplier: 0
  },
  consumption: { salaries: 240, charges: 0, foodConsumption: 8, craftsmanEffect: 1 },
  newReserves: {
    newReserves: { food: 614.7, treasure: 0, materials: 0 },
    totalMineProduction: 70.57785180445435,
    mineDepth: 0,
    perishableRate: 0,
    hasFamine: false
  }
}
Using calculated food reserves: 614.7
Updating food reserves to: 614.7
Verified food reserves after update: 614.7
[0mPOST /api/game/cycle [32m200[0m 8.561 ms - 3328[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 3,
  foodPerFarmer: 4,
  seasonFactor: 1.25,
  foodGeneralModifier: 2,
  foodTechModifier: 0,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 45.000000015
Food production calculated: 45.000000015
Food consumption calculation: { totalInhabitants: 8, consumption: 8 }
calculateNewReserves inputs: {
  currentFoodReserves: 614.7,
  foodProduction: 45.000000015,
  foodConsumption: 8
}
Perishable modifiers: {
  modifiers: [
    {
      id: 1,
      table_id: 3,
      title: 'Base',
      effect: 0.2,
      description: 'Péremption naturelle',
      start_date: null,
      created_at: '2025-04-23 22:53:21',
      updated_at: '2025-04-23 22:53:21'
    },
    {
      id: 6,
      table_id: 3,
      title: 'adad',
      effect: -0.2,
      description: '',
      start_date: null,
      created_at: '2025-04-24 12:34:37',
      updated_at: '2025-04-24 17:09:04'
    }
  ],
  tableValue: 0,
  calculatedSum: 0
}
Food reserves calculation: {
  currentReserves: 614.7,
  perishableRate: 0,
  foodLostToPerishable: 0,
  remainingAfterPerishable: 614.7,
  production: 45.000000015,
  consumption: 8,
  netProductionConsumption: 37.000000015,
  newReservesBeforeMax: 651.7,
  newReserves: 651.7
}
Checking newReserves: {
  probabilities: {
    sickness: 0.0003752955641842479,
    crime: 0.10022816582522492,
    research: 0.01
  },
  events: [],
  production: {
    foodProduction: 45.000000015,
    miningProduction: 0,
    materialsProduction: 0,
    tradingRevenue: 0,
    engineeringMultiplier: 0
  },
  consumption: { salaries: 240, charges: 0, foodConsumption: 8, craftsmanEffect: 1 },
  newReserves: {
    newReserves: { food: 651.7, treasure: 0, materials: 0 },
    totalMineProduction: 70.57785180445435,
    mineDepth: 0,
    perishableRate: 0,
    hasFamine: false
  }
}
Using calculated food reserves: 651.7
Updating food reserves to: 651.7
Verified food reserves after update: 651.7
[0mPOST /api/game/cycle [32m200[0m 8.768 ms - 3348[0m
[0mGET /api/modifiers/table/3 [32m200[0m 2.203 ms - 565[0m
[0mGET /api/modifiers/table/1 [32m200[0m 1.706 ms - 422[0m
[0mGET /api/modifiers/table/2 [32m200[0m 1.615 ms - 299[0m
[0mGET /api/modifiers/table/1 [36m304[0m 0.851 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 1.488 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 1.924 ms - -[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 3,
  foodPerFarmer: 4,
  seasonFactor: 1,
  foodGeneralModifier: 2,
  foodTechModifier: 0,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 36.000000012
Food production calculated: 36.000000012
Food consumption calculation: { totalInhabitants: 8, consumption: 8 }
calculateNewReserves inputs: {
  currentFoodReserves: 651.7,
  foodProduction: 36.000000012,
  foodConsumption: 8
}
Perishable modifiers: {
  modifiers: [
    {
      id: 1,
      table_id: 3,
      title: 'Base',
      effect: 0.2,
      description: 'Péremption naturelle',
      start_date: null,
      created_at: '2025-04-23 22:53:21',
      updated_at: '2025-04-23 22:53:21'
    },
    {
      id: 6,
      table_id: 3,
      title: 'adad',
      effect: -0.2,
      description: '',
      start_date: null,
      created_at: '2025-04-24 12:34:37',
      updated_at: '2025-04-24 17:09:04'
    }
  ],
  tableValue: 0,
  calculatedSum: 0
}
Food reserves calculation: {
  currentReserves: 651.7,
  perishableRate: 0,
  foodLostToPerishable: 0,
  remainingAfterPerishable: 651.7,
  production: 36.000000012,
  consumption: 8,
  netProductionConsumption: 28.000000012,
  newReservesBeforeMax: 679.7,
  newReserves: 679.7
}
Checking newReserves: {
  probabilities: {
    sickness: 0.0003752955641842479,
    crime: 0.10022816582522492,
    research: 0.01
  },
  events: [
    {
      type: 'CRIME',
      description: 'Un vol important a été signalé dans les quartiers des mineurs.'
    }
  ],
  production: {
    foodProduction: 36.000000012,
    miningProduction: 0,
    materialsProduction: 0,
    tradingRevenue: 0,
    engineeringMultiplier: 0
  },
  consumption: { salaries: 240, charges: 0, foodConsumption: 8, craftsmanEffect: 1 },
  newReserves: {
    newReserves: { food: 679.7, treasure: 0, materials: 0 },
    totalMineProduction: 70.57785180445435,
    mineDepth: 0,
    perishableRate: 0,
    hasFamine: false
  }
}
Using calculated food reserves: 679.7
Updating food reserves to: 679.7
Verified food reserves after update: 679.7
[0mPOST /api/game/cycle [32m200[0m 8.883 ms - 3317[0m
[0mGET /api/modifiers/table/1 [32m200[0m 1.147 ms - 422[0m
[0mGET /api/modifiers/table/2 [32m200[0m 1.140 ms - 299[0m
[0mGET /api/modifiers/table/3 [32m200[0m 1.563 ms - 565[0m
[0mGET /api/modifiers/table/3 [36m304[0m 0.966 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 1.179 ms - -[0m
[0mGET /api/modifiers/table/1 [36m304[0m 1.999 ms - -[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 3,
  foodPerFarmer: 4,
  seasonFactor: 1,
  foodGeneralModifier: 2,
  foodTechModifier: 0,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 36.000000012
Food production calculated: 36.000000012
Food consumption calculation: { totalInhabitants: 8, consumption: 8 }
calculateNewReserves inputs: {
  currentFoodReserves: 679.7,
  foodProduction: 36.000000012,
  foodConsumption: 8
}
Perishable modifiers: {
  modifiers: [
    {
      id: 1,
      table_id: 3,
      title: 'Base',
      effect: 0.2,
      description: 'Péremption naturelle',
      start_date: null,
      created_at: '2025-04-23 22:53:21',
      updated_at: '2025-04-23 22:53:21'
    },
    {
      id: 6,
      table_id: 3,
      title: 'adad',
      effect: -0.2,
      description: '',
      start_date: null,
      created_at: '2025-04-24 12:34:37',
      updated_at: '2025-04-24 17:09:04'
    }
  ],
  tableValue: 0,
  calculatedSum: 0
}
Food reserves calculation: {
  currentReserves: 679.7,
  perishableRate: 0,
  foodLostToPerishable: 0,
  remainingAfterPerishable: 679.7,
  production: 36.000000012,
  consumption: 8,
  netProductionConsumption: 28.000000012,
  newReservesBeforeMax: 707.7,
  newReserves: 707.7
}
Checking newReserves: {
  probabilities: {
    sickness: 0.0003752955641842479,
    crime: 0.10022816582522492,
    research: 0.01
  },
  events: [
    {
      type: 'CRIME',
      description: 'Un vol important a été signalé dans les quartiers des mineurs.'
    }
  ],
  production: {
    foodProduction: 36.000000012,
    miningProduction: 0,
    materialsProduction: 0,
    tradingRevenue: 0,
    engineeringMultiplier: 0
  },
  consumption: { salaries: 240, charges: 0, foodConsumption: 8, craftsmanEffect: 1 },
  newReserves: {
    newReserves: { food: 707.7, treasure: 0, materials: 0 },
    totalMineProduction: 70.57785180445435,
    mineDepth: 0,
    perishableRate: 0,
    hasFamine: false
  }
}
Using calculated food reserves: 707.7
Updating food reserves to: 707.7
Verified food reserves after update: 707.7
[0mPOST /api/game/cycle [32m200[0m 7.779 ms - 3561[0m
[0mGET /api/modifiers/table/1 [32m200[0m 1.653 ms - 422[0m
[0mGET /api/modifiers/table/2 [32m200[0m 1.526 ms - 299[0m
[0mGET /api/modifiers/table/3 [32m200[0m 1.461 ms - 565[0m
[0mGET /api/modifiers/table/2 [36m304[0m 1.208 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 1.196 ms - -[0m
[0mGET /api/modifiers/table/1 [36m304[0m 1.348 ms - -[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 3,
  foodPerFarmer: 4,
  seasonFactor: 1,
  foodGeneralModifier: 2,
  foodTechModifier: 0,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 36.000000012
Food consumption calculation: { totalInhabitants: 8, consumption: 8 }
[0mGET /api/game/state [32m200[0m 5.393 ms - 7351[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 3,
  foodPerFarmer: 4,
  seasonFactor: 1,
  foodGeneralModifier: 2,
  foodTechModifier: 0,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 36.000000012
Food consumption calculation: { totalInhabitants: 8, consumption: 8 }
[0mGET /api/game/state [36m304[0m 2.400 ms - -[0m
[0mGET /api/game/events [32m200[0m 0.354 ms - 1701[0m
[0mGET /api/game/events [36m304[0m 0.325 ms - -[0m
[0mGET /api/modifiers/table/2 [32m200[0m 0.995 ms - 299[0m
[0mGET /api/modifiers/table/3 [32m200[0m 1.083 ms - 565[0m
[0mGET /api/modifiers/table/1 [32m200[0m 1.470 ms - 422[0m
[0mGET /api/modifiers/table/3 [36m304[0m 0.730 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 1.007 ms - -[0m
[0mGET /api/modifiers/table/1 [36m304[0m 0.882 ms - -[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 3,
  foodPerFarmer: 4,
  seasonFactor: 1,
  foodGeneralModifier: 2,
  foodTechModifier: 0,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 36.000000012
Food production calculated: 36.000000012
Food consumption calculation: { totalInhabitants: 8, consumption: 8 }
calculateNewReserves inputs: {
  currentFoodReserves: 707.7,
  foodProduction: 36.000000012,
  foodConsumption: 8
}
Perishable modifiers: {
  modifiers: [
    {
      id: 1,
      table_id: 3,
      title: 'Base',
      effect: 0.2,
      description: 'Péremption naturelle',
      start_date: null,
      created_at: '2025-04-23 22:53:21',
      updated_at: '2025-04-23 22:53:21'
    },
    {
      id: 6,
      table_id: 3,
      title: 'adad',
      effect: -0.2,
      description: '',
      start_date: null,
      created_at: '2025-04-24 12:34:37',
      updated_at: '2025-04-24 17:09:04'
    }
  ],
  tableValue: 0,
  calculatedSum: 0
}
Food reserves calculation: {
  currentReserves: 707.7,
  perishableRate: 0,
  foodLostToPerishable: 0,
  remainingAfterPerishable: 707.7,
  production: 36.000000012,
  consumption: 8,
  netProductionConsumption: 28.000000012,
  newReservesBeforeMax: 735.7,
  newReserves: 735.7
}
Checking newReserves: {
  probabilities: {
    sickness: 0.0003752955641842479,
    crime: 0.10022816582522492,
    research: 0.01
  },
  events: [],
  production: {
    foodProduction: 36.000000012,
    miningProduction: 0,
    materialsProduction: 0,
    tradingRevenue: 0,
    engineeringMultiplier: 0
  },
  consumption: { salaries: 240, charges: 0, foodConsumption: 8, craftsmanEffect: 1 },
  newReserves: {
    newReserves: { food: 735.7, treasure: 0, materials: 0 },
    totalMineProduction: 70.57785180445435,
    mineDepth: 0,
    perishableRate: 0,
    hasFamine: false
  }
}
Using calculated food reserves: 735.7
Updating food reserves to: 735.7
Verified food reserves after update: 735.7
[0mPOST /api/game/cycle [32m200[0m 7.627 ms - 3231[0m
[0mGET /api/modifiers/table/1 [32m200[0m 2.796 ms - 422[0m
[0mGET /api/modifiers/table/2 [32m200[0m 2.689 ms - 299[0m
[0mGET /api/modifiers/table/3 [32m200[0m 2.169 ms - 565[0m
[0mGET /api/modifiers/table/3 [36m304[0m 0.777 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 1.114 ms - -[0m
[0mGET /api/modifiers/table/1 [36m304[0m 0.988 ms - -[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 3,
  foodPerFarmer: 4,
  seasonFactor: 0.5,
  foodGeneralModifier: 2,
  foodTechModifier: 0,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 18.000000006
Food production calculated: 18.000000006
Food consumption calculation: { totalInhabitants: 8, consumption: 8 }
calculateNewReserves inputs: {
  currentFoodReserves: 735.7,
  foodProduction: 18.000000006,
  foodConsumption: 8
}
Perishable modifiers: {
  modifiers: [
    {
      id: 1,
      table_id: 3,
      title: 'Base',
      effect: 0.2,
      description: 'Péremption naturelle',
      start_date: null,
      created_at: '2025-04-23 22:53:21',
      updated_at: '2025-04-23 22:53:21'
    },
    {
      id: 6,
      table_id: 3,
      title: 'adad',
      effect: -0.2,
      description: '',
      start_date: null,
      created_at: '2025-04-24 12:34:37',
      updated_at: '2025-04-24 17:09:04'
    }
  ],
  tableValue: 0,
  calculatedSum: 0
}
Food reserves calculation: {
  currentReserves: 735.7,
  perishableRate: 0,
  foodLostToPerishable: 0,
  remainingAfterPerishable: 735.7,
  production: 18.000000006,
  consumption: 8,
  netProductionConsumption: 10.000000006,
  newReservesBeforeMax: 745.7,
  newReserves: 745.7
}
Checking newReserves: {
  probabilities: {
    sickness: 0.0003752955641842479,
    crime: 0.10022816582522492,
    research: 0.01
  },
  events: [],
  production: {
    foodProduction: 18.000000006,
    miningProduction: 0,
    materialsProduction: 0,
    tradingRevenue: 0,
    engineeringMultiplier: 0
  },
  consumption: { salaries: 240, charges: 0, foodConsumption: 8, craftsmanEffect: 1 },
  newReserves: {
    newReserves: { food: 745.7, treasure: 0, materials: 0 },
    totalMineProduction: 70.57785180445435,
    mineDepth: 0,
    perishableRate: 0,
    hasFamine: false
  }
}
Using calculated food reserves: 745.7
Updating food reserves to: 745.7
Verified food reserves after update: 745.7
[0mPOST /api/game/cycle [32m200[0m 6.447 ms - 3260[0m
[0mGET /api/modifiers/table/1 [32m200[0m 0.952 ms - 422[0m
[0mGET /api/modifiers/table/2 [32m200[0m 0.984 ms - 299[0m
[0mGET /api/modifiers/table/3 [32m200[0m 1.257 ms - 565[0m
[0mGET /api/modifiers/table/1 [36m304[0m 1.730 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 1.367 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 1.687 ms - -[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 3,
  foodPerFarmer: 4,
  seasonFactor: 0.5,
  foodGeneralModifier: 2,
  foodTechModifier: 0,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 18.000000006
Food consumption calculation: { totalInhabitants: 8, consumption: 8 }
[0mGET /api/game/state [32m200[0m 5.430 ms - 7050[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 3,
  foodPerFarmer: 4,
  seasonFactor: 0.5,
  foodGeneralModifier: 2,
  foodTechModifier: 0,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 18.000000006
Food consumption calculation: { totalInhabitants: 8, consumption: 8 }
[0mGET /api/game/state [36m304[0m 3.495 ms - -[0m
[0mGET /api/game/events [36m304[0m 0.290 ms - -[0m
[0mGET /api/game/events [36m304[0m 0.185 ms - -[0m
[0mGET /api/modifiers/table/1 [32m200[0m 1.243 ms - 422[0m
[0mGET /api/modifiers/table/2 [32m200[0m 1.259 ms - 299[0m
[0mGET /api/modifiers/table/3 [32m200[0m 1.299 ms - 565[0m
[0mGET /api/modifiers/table/1 [36m304[0m 1.184 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 0.938 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 0.924 ms - -[0m
[0mDELETE /api/modifiers/6 [32m200[0m 3.113 ms - 413[0m
[0mGET /api/modifiers/table/1 [36m304[0m 4.348 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 3.736 ms - -[0m
[0mGET /api/modifiers/table/3 [32m200[0m 3.332 ms - 413[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 3,
  foodPerFarmer: 4,
  seasonFactor: 0.5,
  foodGeneralModifier: 2,
  foodTechModifier: 0,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 18.000000006
Food production calculated: 18.000000006
Food consumption calculation: { totalInhabitants: 8, consumption: 8 }
calculateNewReserves inputs: {
  currentFoodReserves: 745.7,
  foodProduction: 18.000000006,
  foodConsumption: 8
}
Perishable modifiers: {
  modifiers: [
    {
      id: 1,
      table_id: 3,
      title: 'Base',
      effect: 0.2,
      description: 'Péremption naturelle',
      start_date: null,
      created_at: '2025-04-23 22:53:21',
      updated_at: '2025-04-23 22:53:21'
    }
  ],
  tableValue: 0.2,
  calculatedSum: 0.2
}
Food reserves calculation: {
  currentReserves: 745.7,
  perishableRate: 0.2,
  foodLostToPerishable: 149.14000000000001,
  remainingAfterPerishable: 596.5600000000001,
  production: 18.000000006,
  consumption: 8,
  netProductionConsumption: 10.000000006,
  newReservesBeforeMax: 606.6,
  newReserves: 606.6
}
Checking newReserves: {
  probabilities: {
    sickness: 0.0003752955641842479,
    crime: 0.10022816582522492,
    research: 0.01
  },
  events: [],
  production: {
    foodProduction: 18.000000006,
    miningProduction: 0,
    materialsProduction: 0,
    tradingRevenue: 0,
    engineeringMultiplier: 0
  },
  consumption: { salaries: 240, charges: 0, foodConsumption: 8, craftsmanEffect: 1 },
  newReserves: {
    newReserves: { food: 606.6, treasure: 0, materials: 0 },
    totalMineProduction: 70.57785180445435,
    mineDepth: 0,
    perishableRate: 0.2,
    hasFamine: false
  }
}
Using calculated food reserves: 606.6
Updating food reserves to: 606.6
Verified food reserves after update: 606.6
[0mPOST /api/game/cycle [32m200[0m 8.433 ms - 3255[0m
[0mGET /api/modifiers/table/1 [32m200[0m 1.272 ms - 422[0m
[0mGET /api/modifiers/table/2 [32m200[0m 1.501 ms - 299[0m
[0mGET /api/modifiers/table/3 [32m200[0m 2.300 ms - 413[0m
[0mGET /api/modifiers/table/2 [36m304[0m 1.929 ms - -[0m
[0mGET /api/modifiers/table/1 [36m304[0m 1.559 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 1.529 ms - -[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 3,
  foodPerFarmer: 4,
  seasonFactor: 0.5,
  foodGeneralModifier: 2,
  foodTechModifier: 0,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 18.000000006
Food production calculated: 18.000000006
Food consumption calculation: { totalInhabitants: 8, consumption: 8 }
calculateNewReserves inputs: {
  currentFoodReserves: 606.6,
  foodProduction: 18.000000006,
  foodConsumption: 8
}
Perishable modifiers: {
  modifiers: [
    {
      id: 1,
      table_id: 3,
      title: 'Base',
      effect: 0.2,
      description: 'Péremption naturelle',
      start_date: null,
      created_at: '2025-04-23 22:53:21',
      updated_at: '2025-04-23 22:53:21'
    }
  ],
  tableValue: 0.2,
  calculatedSum: 0.2
}
Food reserves calculation: {
  currentReserves: 606.6,
  perishableRate: 0.2,
  foodLostToPerishable: 121.32000000000001,
  remainingAfterPerishable: 485.28000000000003,
  production: 18.000000006,
  consumption: 8,
  netProductionConsumption: 10.000000006,
  newReservesBeforeMax: 495.3,
  newReserves: 495.3
}
Checking newReserves: {
  probabilities: {
    sickness: 0.0003752955641842479,
    crime: 0.10022816582522492,
    research: 0.01
  },
  events: [],
  production: {
    foodProduction: 18.000000006,
    miningProduction: 0,
    materialsProduction: 0,
    tradingRevenue: 0,
    engineeringMultiplier: 0
  },
  consumption: { salaries: 240, charges: 0, foodConsumption: 8, craftsmanEffect: 1 },
  newReserves: {
    newReserves: { food: 495.3, treasure: 0, materials: 0 },
    totalMineProduction: 70.57785180445435,
    mineDepth: 0,
    perishableRate: 0.2,
    hasFamine: false
  }
}
Using calculated food reserves: 495.3
Updating food reserves to: 495.3
Verified food reserves after update: 495.3
[0mPOST /api/game/cycle [32m200[0m 8.296 ms - 3350[0m
[0mGET /api/modifiers/table/1 [32m200[0m 1.822 ms - 422[0m
[0mGET /api/modifiers/table/2 [32m200[0m 1.525 ms - 299[0m
[0mGET /api/modifiers/table/3 [32m200[0m 1.339 ms - 413[0m
[0mGET /api/modifiers/table/3 [36m304[0m 0.671 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 0.868 ms - -[0m
[0mGET /api/modifiers/table/1 [36m304[0m 1.127 ms - -[0m
[0mPOST /api/game/workers/change [32m200[0m 1.786 ms - 1258[0m
[0mPOST /api/game/jobs/change [32m200[0m 2.946 ms - 1258[0m
[0mPOST /api/game/jobs/change [33m400[0m 0.988 ms - 56[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 4,
  foodPerFarmer: 4,
  seasonFactor: 1,
  foodGeneralModifier: 2,
  foodTechModifier: 0,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 48.000000016
Food consumption calculation: { totalInhabitants: 9, consumption: 9 }
[0mGET /api/game/state [32m200[0m 5.927 ms - 6984[0m
[0mGET /api/game/events [36m304[0m 0.388 ms - -[0m
[0mPOST /api/game/jobs/change [33m400[0m 1.515 ms - 56[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 4,
  foodPerFarmer: 4,
  seasonFactor: 1,
  foodGeneralModifier: 2,
  foodTechModifier: 0,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 48.000000016
Food consumption calculation: { totalInhabitants: 9, consumption: 9 }
[0mGET /api/game/state [32m200[0m 5.899 ms - 6984[0m
[0mGET /api/game/events [36m304[0m 0.465 ms - -[0m
[0mPOST /api/game/jobs/change [33m400[0m 1.181 ms - 56[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 4,
  foodPerFarmer: 4,
  seasonFactor: 1,
  foodGeneralModifier: 2,
  foodTechModifier: 0,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 48.000000016
Food consumption calculation: { totalInhabitants: 9, consumption: 9 }
[0mGET /api/game/state [36m304[0m 6.052 ms - -[0m
[0mGET /api/game/events [36m304[0m 0.339 ms - -[0m
[0mPOST /api/game/workers/change [32m200[0m 3.821 ms - 1258[0m
[0mPOST /api/game/jobs/change [32m200[0m 3.751 ms - 1258[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1,
  foodGeneralModifier: 2,
  foodTechModifier: 0,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 60.00000002
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
[0mGET /api/game/state [32m200[0m 3.889 ms - 6983[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1,
  foodGeneralModifier: 2,
  foodTechModifier: 0,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 60.00000002
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
[0mGET /api/game/state [36m304[0m 1.796 ms - -[0m
[0mGET /api/game/events [36m304[0m 0.260 ms - -[0m
[0mGET /api/game/events [36m304[0m 0.270 ms - -[0m
[0mGET /api/modifiers/table/2 [32m200[0m 1.028 ms - 299[0m
[0mGET /api/modifiers/table/1 [32m200[0m 1.550 ms - 422[0m
[0mGET /api/modifiers/table/3 [32m200[0m 1.449 ms - 413[0m
[0mGET /api/modifiers/table/2 [36m304[0m 0.791 ms - -[0m
[0mGET /api/modifiers/table/1 [36m304[0m 0.743 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 0.856 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 0.904 ms - -[0m
[0mGET /api/modifiers/table/1 [36m304[0m 1.283 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 1.205 ms - -[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1,
  foodGeneralModifier: 2,
  foodTechModifier: 0,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 60.00000002
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
[0mGET /api/game/state [32m200[0m 5.052 ms - 6983[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1,
  foodGeneralModifier: 2,
  foodTechModifier: 0,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 60.00000002
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
[0mGET /api/game/state [36m304[0m 1.564 ms - -[0m
[0mGET /api/game/events [36m304[0m 0.388 ms - -[0m
[0mGET /api/game/events [36m304[0m 0.475 ms - -[0m
[0mGET /api/modifiers/table/1 [32m200[0m 1.127 ms - 422[0m
[0mGET /api/modifiers/table/2 [32m200[0m 1.178 ms - 299[0m
[0mGET /api/modifiers/table/3 [32m200[0m 1.363 ms - 413[0m
[0mGET /api/modifiers/table/1 [36m304[0m 0.904 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 0.994 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 0.904 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 1.914 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 1.392 ms - -[0m
[0mGET /api/modifiers/table/1 [36m304[0m 1.730 ms - -[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1,
  foodGeneralModifier: 2,
  foodTechModifier: 0,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 60.00000002
Food production calculated: 60.00000002
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
calculateNewReserves inputs: {
  currentFoodReserves: 495.3,
  foodProduction: 60.00000002,
  foodConsumption: 10
}
Perishable modifiers: {
  modifiers: [
    {
      id: 1,
      table_id: 3,
      title: 'Base',
      effect: 0.2,
      description: 'Péremption naturelle',
      start_date: null,
      created_at: '2025-04-23 22:53:21',
      updated_at: '2025-04-23 22:53:21'
    }
  ],
  tableValue: 0.2,
  calculatedSum: 0.2
}
Food reserves calculation: {
  currentReserves: 495.3,
  perishableRate: 0.2,
  foodLostToPerishable: 99.06,
  remainingAfterPerishable: 396.24,
  production: 60.00000002,
  consumption: 10,
  netProductionConsumption: 50.00000002,
  newReservesBeforeMax: 446.2,
  newReserves: 446.2
}
Checking newReserves: {
  probabilities: {
    sickness: 0.0004582347356232009,
    crime: 0.10664242539375059,
    research: 0.01
  },
  events: [],
  production: {
    foodProduction: 60.00000002,
    miningProduction: 0,
    materialsProduction: 0,
    tradingRevenue: 0,
    engineeringMultiplier: 0
  },
  consumption: {
    salaries: 280,
    charges: 0,
    foodConsumption: 10,
    craftsmanEffect: 1
  },
  newReserves: {
    newReserves: { food: 446.2, treasure: 0, materials: 0 },
    totalMineProduction: 70.57785180445435,
    mineDepth: 0,
    perishableRate: 0.2,
    hasFamine: false
  }
}
Using calculated food reserves: 446.2
Updating food reserves to: 446.2
Verified food reserves after update: 446.2
[0mPOST /api/game/cycle [32m200[0m 7.681 ms - 3317[0m
[0mGET /api/modifiers/table/2 [32m200[0m 2.056 ms - 299[0m
[0mGET /api/modifiers/table/3 [32m200[0m 2.085 ms - 413[0m
[0mGET /api/modifiers/table/1 [32m200[0m 1.918 ms - 422[0m
[0mGET /api/modifiers/table/3 [36m304[0m 1.053 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 1.135 ms - -[0m
[0mGET /api/modifiers/table/1 [36m304[0m 1.267 ms - -[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1,
  foodGeneralModifier: 2,
  foodTechModifier: 0,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 60.00000002
Food production calculated: 60.00000002
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
calculateNewReserves inputs: {
  currentFoodReserves: 446.2,
  foodProduction: 60.00000002,
  foodConsumption: 10
}
Perishable modifiers: {
  modifiers: [
    {
      id: 1,
      table_id: 3,
      title: 'Base',
      effect: 0.2,
      description: 'Péremption naturelle',
      start_date: null,
      created_at: '2025-04-23 22:53:21',
      updated_at: '2025-04-23 22:53:21'
    }
  ],
  tableValue: 0.2,
  calculatedSum: 0.2
}
Food reserves calculation: {
  currentReserves: 446.2,
  perishableRate: 0.2,
  foodLostToPerishable: 89.24000000000001,
  remainingAfterPerishable: 356.96,
  production: 60.00000002,
  consumption: 10,
  netProductionConsumption: 50.00000002,
  newReservesBeforeMax: 407,
  newReserves: 407
}
Checking newReserves: {
  probabilities: {
    sickness: 0.0004582347356232009,
    crime: 0.10664242539375059,
    research: 0.01
  },
  events: [],
  production: {
    foodProduction: 60.00000002,
    miningProduction: 0,
    materialsProduction: 0,
    tradingRevenue: 0,
    engineeringMultiplier: 0
  },
  consumption: {
    salaries: 280,
    charges: 0,
    foodConsumption: 10,
    craftsmanEffect: 1
  },
  newReserves: {
    newReserves: { food: 407, treasure: 0, materials: 0 },
    totalMineProduction: 70.57785180445435,
    mineDepth: 0,
    perishableRate: 0.2,
    hasFamine: false
  }
}
Using calculated food reserves: 407
Updating food reserves to: 407
Verified food reserves after update: 407
[0mPOST /api/game/cycle [32m200[0m 6.775 ms - 3303[0m
[0mGET /api/modifiers/table/2 [32m200[0m 1.780 ms - 299[0m
[0mGET /api/modifiers/table/3 [32m200[0m 1.829 ms - 413[0m
[0mGET /api/modifiers/table/1 [32m200[0m 3.049 ms - 422[0m
[0mGET /api/modifiers/table/2 [36m304[0m 1.879 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 2.458 ms - -[0m
[0mGET /api/modifiers/table/1 [36m304[0m 1.085 ms - -[0m
[0mPOST /api/modifiers/table/3 [32m200[0m 3.559 ms - 608[0m
[0mGET /api/modifiers/table/3 [32m200[0m 2.985 ms - 601[0m
[0mGET /api/modifiers/table/1 [36m304[0m 3.600 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 3.664 ms - -[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1,
  foodGeneralModifier: 2,
  foodTechModifier: 0,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 60.00000002
Food production calculated: 60.00000002
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
calculateNewReserves inputs: {
  currentFoodReserves: 407,
  foodProduction: 60.00000002,
  foodConsumption: 10
}
Perishable modifiers: {
  modifiers: [
    {
      id: 1,
      table_id: 3,
      title: 'Base',
      effect: 0.2,
      description: 'Péremption naturelle',
      start_date: null,
      created_at: '2025-04-23 22:53:21',
      updated_at: '2025-04-23 22:53:21'
    },
    {
      id: 6,
      table_id: 3,
      title: 'dingo',
      effect: 0.1,
      description: '',
      start_date: null,
      created_at: '2025-04-24 21:34:38',
      updated_at: '2025-04-24 21:34:38'
    }
  ],
  tableValue: 0.30000000000000004,
  calculatedSum: 0.30000000000000004
}
Food reserves calculation: {
  currentReserves: 407,
  perishableRate: 0.30000000000000004,
  foodLostToPerishable: 122.10000000000002,
  remainingAfterPerishable: 284.9,
  production: 60.00000002,
  consumption: 10,
  netProductionConsumption: 50.00000002,
  newReservesBeforeMax: 334.9,
  newReserves: 334.9
}
Checking newReserves: {
  probabilities: {
    sickness: 0.0004582347356232009,
    crime: 0.10664242539375059,
    research: 0.01
  },
  events: [],
  production: {
    foodProduction: 60.00000002,
    miningProduction: 0,
    materialsProduction: 0,
    tradingRevenue: 0,
    engineeringMultiplier: 0
  },
  consumption: {
    salaries: 280,
    charges: 0,
    foodConsumption: 10,
    craftsmanEffect: 1
  },
  newReserves: {
    newReserves: { food: 334.9, treasure: 0, materials: 0 },
    totalMineProduction: 70.57785180445435,
    mineDepth: 0,
    perishableRate: 0.30000000000000004,
    hasFamine: false
  }
}
Using calculated food reserves: 334.9
Updating food reserves to: 334.9
Verified food reserves after update: 334.9
[0mPOST /api/game/cycle [32m200[0m 7.247 ms - 3319[0m
[0mGET /api/modifiers/table/3 [32m200[0m 1.293 ms - 601[0m
[0mGET /api/modifiers/table/1 [32m200[0m 1.340 ms - 422[0m
[0mGET /api/modifiers/table/2 [32m200[0m 1.419 ms - 299[0m
[0mGET /api/modifiers/table/1 [36m304[0m 0.824 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 1.100 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 0.991 ms - -[0m
[0mPOST /api/modifiers/table/2 [32m200[0m 3.283 ms - 461[0m
[0mGET /api/modifiers/table/2 [32m200[0m 3.137 ms - 454[0m
[0mGET /api/modifiers/table/1 [36m304[0m 3.163 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 3.317 ms - -[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1.25,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 80.750000025
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
[0mGET /api/game/state [32m200[0m 4.843 ms - 7284[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1.25,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 80.750000025
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
[0mGET /api/game/state [36m304[0m 1.798 ms - -[0m
[0mGET /api/game/events [36m304[0m 0.262 ms - -[0m
[0mGET /api/game/events [36m304[0m 0.435 ms - -[0m
[0mGET /api/modifiers/table/2 [32m200[0m 0.836 ms - 454[0m
[0mGET /api/modifiers/table/1 [32m200[0m 1.203 ms - 422[0m
[0mGET /api/modifiers/table/3 [32m200[0m 1.039 ms - 601[0m
[0mGET /api/modifiers/table/2 [36m304[0m 0.789 ms - -[0m
[0mGET /api/modifiers/table/1 [36m304[0m 0.840 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 0.916 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 0.674 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 0.743 ms - -[0m
[0mGET /api/modifiers/table/1 [36m304[0m 1.305 ms - -[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1.25,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 80.750000025
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
[0mGET /api/game/state [32m200[0m 5.744 ms - 7284[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1.25,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 80.750000025
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
[0mGET /api/game/state [36m304[0m 2.001 ms - -[0m
[0mGET /api/game/events [36m304[0m 0.345 ms - -[0m
[0mGET /api/game/events [36m304[0m 0.548 ms - -[0m
[0mGET /api/modifiers/table/1 [32m200[0m 1.135 ms - 422[0m
[0mGET /api/modifiers/table/2 [32m200[0m 1.450 ms - 454[0m
[0mGET /api/modifiers/table/3 [32m200[0m 1.766 ms - 601[0m
[0mGET /api/modifiers/table/1 [36m304[0m 3.605 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 3.441 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 3.883 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 1.864 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 2.318 ms - -[0m
[0mGET /api/modifiers/table/1 [36m304[0m 2.756 ms - -[0m
[0mPOST /api/modifiers/table/3 [32m200[0m 2.610 ms - 768[0m
[0mGET /api/modifiers/table/3 [32m200[0m 2.725 ms - 761[0m
[0mGET /api/modifiers/table/1 [36m304[0m 2.848 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 2.898 ms - -[0m
[0mPUT /api/modifiers/8 [32m200[0m 4.155 ms - 756[0m
[0mGET /api/modifiers/table/1 [32m200[0m 2.965 ms - 422[0m
[0mGET /api/modifiers/table/2 [32m200[0m 3.128 ms - 454[0m
[0mGET /api/modifiers/table/3 [32m200[0m 3.349 ms - 756[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1.25,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 80.750000025
Food production calculated: 80.750000025
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
calculateNewReserves inputs: {
  currentFoodReserves: 334.9,
  foodProduction: 80.750000025,
  foodConsumption: 10
}
Perishable modifiers: {
  modifiers: [
    {
      id: 1,
      table_id: 3,
      title: 'Base',
      effect: 0.2,
      description: 'Péremption naturelle',
      start_date: null,
      created_at: '2025-04-23 22:53:21',
      updated_at: '2025-04-23 22:53:21'
    },
    {
      id: 6,
      table_id: 3,
      title: 'dingo',
      effect: 0.1,
      description: '',
      start_date: null,
      created_at: '2025-04-24 21:34:38',
      updated_at: '2025-04-24 21:34:38'
    },
    {
      id: 8,
      table_id: 3,
      title: 'ad',
      effect: -0.25,
      description: '',
      start_date: null,
      created_at: '2025-04-24 21:48:09',
      updated_at: '2025-04-24 21:48:26'
    }
  ],
  tableValue: 0.05000000000000002,
  calculatedSum: 0.050000000000000044
}
Food reserves calculation: {
  currentReserves: 334.9,
  perishableRate: 0.05000000000000002,
  foodLostToPerishable: 16.745000000000005,
  remainingAfterPerishable: 318.155,
  production: 80.750000025,
  consumption: 10,
  netProductionConsumption: 70.750000025,
  newReservesBeforeMax: 388.9,
  newReserves: 388.9
}
Checking newReserves: {
  probabilities: {
    sickness: 0.0004582347356232009,
    crime: 0.10664242539375059,
    research: 0.01
  },
  events: [],
  production: {
    foodProduction: 80.750000025,
    miningProduction: 0,
    materialsProduction: 0,
    tradingRevenue: 0,
    engineeringMultiplier: 0
  },
  consumption: {
    salaries: 280,
    charges: 0,
    foodConsumption: 10,
    craftsmanEffect: 1
  },
  newReserves: {
    newReserves: { food: 388.9, treasure: 0, materials: 0 },
    totalMineProduction: 70.57785180445435,
    mineDepth: 0,
    perishableRate: 0.05000000000000002,
    hasFamine: false
  }
}
Using calculated food reserves: 388.9
Updating food reserves to: 388.9
Verified food reserves after update: 388.9
[0mPOST /api/game/cycle [32m200[0m 7.615 ms - 3298[0m
[0mGET /api/modifiers/table/1 [32m200[0m 2.034 ms - 422[0m
[0mGET /api/modifiers/table/3 [32m200[0m 2.212 ms - 756[0m
[0mGET /api/modifiers/table/2 [32m200[0m 2.412 ms - 454[0m
[0mGET /api/modifiers/table/3 [36m304[0m 1.936 ms - -[0m
[0mGET /api/modifiers/table/1 [36m304[0m 1.853 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 1.691 ms - -[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1.25,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 80.750000025
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
[0mGET /api/game/state [32m200[0m 4.047 ms - 7417[0m
[0mGET /api/game/events [36m304[0m 0.167 ms - -[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1.25,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 80.750000025
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
[0mGET /api/game/state [36m304[0m 1.991 ms - -[0m
[0mGET /api/modifiers/table/1 [32m200[0m 0.766 ms - 422[0m
[0mGET /api/modifiers/table/2 [32m200[0m 1.207 ms - 454[0m
[0mGET /api/modifiers/table/3 [32m200[0m 1.038 ms - 756[0m
[0mGET /api/modifiers/table/1 [36m304[0m 0.722 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 1.120 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 1.204 ms - -[0m
[0mGET /api/game/events [36m304[0m 0.227 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 0.776 ms - -[0m
[0mGET /api/modifiers/table/1 [36m304[0m 1.196 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 1.102 ms - -[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1.25,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 80.750000025
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
[0mGET /api/game/state [32m200[0m 21.424 ms - 7417[0m
[0mGET /api/game/events [36m304[0m 2.075 ms - -[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1.25,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 80.750000025
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
[0mGET /api/game/state [36m304[0m 15.529 ms - -[0m
[0mGET /api/modifiers/table/2 [32m200[0m 0.840 ms - 454[0m
[0mGET /api/modifiers/table/1 [32m200[0m 1.199 ms - 422[0m
[0mGET /api/modifiers/table/3 [32m200[0m 1.060 ms - 756[0m
[0mGET /api/modifiers/table/1 [36m304[0m 1.268 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 1.386 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 1.804 ms - -[0m
[0mGET /api/game/events [36m304[0m 0.384 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 2.109 ms - -[0m
[0mGET /api/modifiers/table/1 [36m304[0m 2.662 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 2.556 ms - -[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1.25,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 80.750000025
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
[0mGET /api/game/state [32m200[0m 6.595 ms - 7417[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1.25,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 80.750000025
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
[0mGET /api/game/state [36m304[0m 2.116 ms - -[0m
[0mGET /api/game/events [36m304[0m 0.314 ms - -[0m
[0mGET /api/game/events [36m304[0m 0.213 ms - -[0m
[0mGET /api/modifiers/table/1 [32m200[0m 1.555 ms - 422[0m
[0mGET /api/modifiers/table/2 [32m200[0m 2.290 ms - 454[0m
[0mGET /api/modifiers/table/3 [32m200[0m 2.209 ms - 756[0m
[0mGET /api/modifiers/table/1 [36m304[0m 2.295 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 2.067 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 2.045 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 2.625 ms - -[0m
[0mGET /api/modifiers/table/1 [36m304[0m 2.485 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 2.657 ms - -[0m
[0mPUT /api/modifiers/6 [32m200[0m 2.786 ms - 756[0m
[0mGET /api/modifiers/table/1 [36m304[0m 4.175 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 3.516 ms - -[0m
[0mGET /api/modifiers/table/3 [32m200[0m 3.568 ms - 756[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1.25,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 80.750000025
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
[0mGET /api/game/state [32m200[0m 4.479 ms - 7417[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1.25,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 80.750000025
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
[0mGET /api/game/state [36m304[0m 2.125 ms - -[0m
[0mGET /api/game/events [36m304[0m 0.284 ms - -[0m
[0mGET /api/game/events [36m304[0m 0.313 ms - -[0m
[0mGET /api/modifiers/table/1 [32m200[0m 1.315 ms - 422[0m
[0mGET /api/modifiers/table/2 [32m200[0m 1.270 ms - 454[0m
[0mGET /api/modifiers/table/3 [32m200[0m 1.008 ms - 756[0m
[0mGET /api/modifiers/table/1 [36m304[0m 0.668 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 0.643 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 0.636 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 0.744 ms - -[0m
[0mGET /api/modifiers/table/1 [36m304[0m 1.071 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 1.045 ms - -[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1.25,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 80.750000025
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
[0mGET /api/game/state [32m200[0m 12.634 ms - 7417[0m
[0mGET /api/game/events [36m304[0m 0.666 ms - -[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1.25,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 80.750000025
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
[0mGET /api/game/state [36m304[0m 10.246 ms - -[0m
[0mGET /api/modifiers/table/3 [32m200[0m 1.085 ms - 756[0m
[0mGET /api/modifiers/table/1 [32m200[0m 1.536 ms - 422[0m
[0mGET /api/modifiers/table/2 [32m200[0m 1.547 ms - 454[0m
[0mGET /api/modifiers/table/2 [36m304[0m 0.799 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 1.374 ms - -[0m
[0mGET /api/modifiers/table/1 [36m304[0m 2.541 ms - -[0m
[0mGET /api/game/events [36m304[0m 0.324 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 1.208 ms - -[0m
[0mGET /api/modifiers/table/1 [36m304[0m 1.280 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 1.322 ms - -[0m
[0mDELETE /api/modifiers/8 [32m200[0m 5.491 ms - 601[0m
[0mGET /api/modifiers/table/1 [36m304[0m 4.163 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 3.999 ms - -[0m
[0mGET /api/modifiers/table/3 [32m200[0m 4.021 ms - 601[0m
[0mPOST /api/modifiers/table/3 [32m200[0m 2.459 ms - 765[0m
[0mGET /api/modifiers/table/3 [32m200[0m 3.034 ms - 758[0m
[0mGET /api/modifiers/table/1 [32m200[0m 3.106 ms - 422[0m
[0mGET /api/modifiers/table/2 [32m200[0m 3.210 ms - 454[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1.25,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 80.750000025
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
[0mGET /api/game/state [32m200[0m 4.450 ms - 7419[0m
[0mGET /api/game/events [36m304[0m 0.215 ms - -[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1.25,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 80.750000025
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
[0mGET /api/game/state [36m304[0m 2.165 ms - -[0m
[0mGET /api/game/events [36m304[0m 0.306 ms - -[0m
[0mGET /api/modifiers/table/1 [32m200[0m 0.915 ms - 422[0m
[0mGET /api/modifiers/table/2 [32m200[0m 0.982 ms - 454[0m
[0mGET /api/modifiers/table/3 [32m200[0m 1.028 ms - 758[0m
[0mGET /api/modifiers/table/1 [36m304[0m 0.635 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 0.664 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 1.002 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 0.591 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 0.682 ms - -[0m
[0mGET /api/modifiers/table/1 [36m304[0m 1.020 ms - -[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1.25,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 80.750000025
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
[0mGET /api/game/state [32m200[0m 5.461 ms - 7419[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1.25,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 80.750000025
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
[0mGET /api/game/state [36m304[0m 1.827 ms - -[0m
[0mGET /api/game/events [36m304[0m 0.340 ms - -[0m
[0mGET /api/game/events [36m304[0m 0.428 ms - -[0m
[0mGET /api/modifiers/table/2 [32m200[0m 1.057 ms - 454[0m
[0mGET /api/modifiers/table/1 [32m200[0m 1.069 ms - 422[0m
[0mGET /api/modifiers/table/3 [32m200[0m 1.118 ms - 758[0m
[0mGET /api/modifiers/table/2 [36m304[0m 0.719 ms - -[0m
[0mGET /api/modifiers/table/1 [36m304[0m 0.638 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 0.785 ms - -[0m
[0mGET /api/modifiers/table/1 [36m304[0m 1.339 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 1.000 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 0.973 ms - -[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1.25,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 80.750000025
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
[0mGET /api/game/state [32m200[0m 4.480 ms - 7419[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1.25,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 80.750000025
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
[0mGET /api/game/state [36m304[0m 1.296 ms - -[0m
[0mGET /api/game/events [36m304[0m 0.334 ms - -[0m
[0mGET /api/game/events [36m304[0m 0.215 ms - -[0m
[0mGET /api/modifiers/table/2 [32m200[0m 1.065 ms - 454[0m
[0mGET /api/modifiers/table/1 [32m200[0m 1.415 ms - 422[0m
[0mGET /api/modifiers/table/3 [32m200[0m 0.850 ms - 758[0m
[0mGET /api/modifiers/table/3 [36m304[0m 0.440 ms - -[0m
[0mGET /api/modifiers/table/1 [36m304[0m 0.647 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 0.838 ms - -[0m
[0mGET /api/modifiers/table/1 [36m304[0m 1.116 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 0.998 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 1.168 ms - -[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1.25,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 80.750000025
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
[0mGET /api/game/state [32m200[0m 4.621 ms - 7419[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1.25,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 80.750000025
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
[0mGET /api/game/state [36m304[0m 1.709 ms - -[0m
[0mGET /api/game/events [36m304[0m 0.300 ms - -[0m
[0mGET /api/game/events [36m304[0m 0.800 ms - -[0m
[0mGET /api/modifiers/table/2 [32m200[0m 1.108 ms - 454[0m
[0mGET /api/modifiers/table/1 [32m200[0m 1.621 ms - 422[0m
[0mGET /api/modifiers/table/3 [32m200[0m 1.439 ms - 758[0m
[0mGET /api/modifiers/table/2 [36m304[0m 0.821 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 0.635 ms - -[0m
[0mGET /api/modifiers/table/1 [36m304[0m 0.824 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 1.279 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 1.274 ms - -[0m
[0mGET /api/modifiers/table/1 [36m304[0m 2.281 ms - -[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1.25,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 80.750000025
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
[0mGET /api/game/state [32m200[0m 3.323 ms - 7419[0m
[0mGET /api/game/events [36m304[0m 0.346 ms - -[0m
[0mGET /api/modifiers/table/1 [32m200[0m 0.919 ms - 422[0m
[0mGET /api/modifiers/table/3 [32m200[0m 0.913 ms - 758[0m
[0mGET /api/modifiers/table/2 [32m200[0m 1.091 ms - 454[0m
[0mGET /api/modifiers/table/1 [36m304[0m 0.928 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 1.016 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 1.049 ms - -[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1.25,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 80.750000025
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
[0mGET /api/game/state [32m200[0m 3.898 ms - 7419[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1.25,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 80.750000025
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
[0mGET /api/game/state [36m304[0m 1.633 ms - -[0m
[0mGET /api/game/events [36m304[0m 0.290 ms - -[0m
[0mGET /api/game/events [36m304[0m 0.214 ms - -[0m
[0mGET /api/modifiers/table/1 [32m200[0m 0.708 ms - 422[0m
[0mGET /api/modifiers/table/2 [32m200[0m 0.705 ms - 454[0m
[0mGET /api/modifiers/table/3 [32m200[0m 0.798 ms - 758[0m
[0mGET /api/modifiers/table/1 [36m304[0m 0.957 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 0.834 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 1.108 ms - -[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1.25,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 80.750000025
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
[0mGET /api/game/state [32m200[0m 4.427 ms - 7419[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1.25,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 80.750000025
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
[0mGET /api/game/state [36m304[0m 1.779 ms - -[0m
[0mGET /api/game/events [36m304[0m 0.368 ms - -[0m
[0mGET /api/game/events [36m304[0m 0.211 ms - -[0m
[0mGET /api/modifiers/table/1 [32m200[0m 1.430 ms - 422[0m
[0mGET /api/modifiers/table/2 [32m200[0m 1.420 ms - 454[0m
[0mGET /api/modifiers/table/3 [32m200[0m 1.105 ms - 758[0m
[0mGET /api/modifiers/table/1 [36m304[0m 0.821 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 0.760 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 0.778 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 1.177 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 1.232 ms - -[0m
[0mGET /api/modifiers/table/1 [36m304[0m 1.739 ms - -[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1.25,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 80.750000025
Food production calculated: 80.750000025
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
calculateNewReserves inputs: {
  currentFoodReserves: 388.9,
  foodProduction: 80.750000025,
  foodConsumption: 10
}
Perishable modifiers: {
  modifiers: [
    {
      id: 1,
      table_id: 3,
      title: 'Base',
      effect: 0.2,
      description: 'Péremption naturelle',
      start_date: null,
      created_at: '2025-04-23 22:53:21',
      updated_at: '2025-04-23 22:53:21'
    },
    {
      id: 6,
      table_id: 3,
      title: 'dingo',
      effect: 0.1,
      description: '',
      start_date: null,
      created_at: '2025-04-24 21:34:38',
      updated_at: '2025-04-24 22:01:57'
    },
    {
      id: 8,
      table_id: 3,
      title: 'Test',
      effect: -0.25,
      description: '',
      start_date: null,
      created_at: '2025-04-24 22:08:35',
      updated_at: '2025-04-24 22:08:35'
    }
  ],
  tableValue: 0.05000000000000002,
  calculatedSum: 0.050000000000000044
}
Food reserves calculation: {
  currentReserves: 388.9,
  perishableRate: 0.05000000000000002,
  foodLostToPerishable: 19.445000000000004,
  remainingAfterPerishable: 369.455,
  production: 80.750000025,
  consumption: 10,
  netProductionConsumption: 70.750000025,
  newReservesBeforeMax: 440.2,
  newReserves: 440.2
}
Checking newReserves: {
  probabilities: {
    sickness: 0.0004582347356232009,
    crime: 0.10664242539375059,
    research: 0.01
  },
  events: [],
  production: {
    foodProduction: 80.750000025,
    miningProduction: 0,
    materialsProduction: 0,
    tradingRevenue: 0,
    engineeringMultiplier: 0
  },
  consumption: {
    salaries: 280,
    charges: 0,
    foodConsumption: 10,
    craftsmanEffect: 1
  },
  newReserves: {
    newReserves: { food: 440.2, treasure: 0, materials: 0 },
    totalMineProduction: 70.57785180445435,
    mineDepth: 0,
    perishableRate: 0.05000000000000002,
    hasFamine: false
  }
}
Using calculated food reserves: 440.2
Updating food reserves to: 440.2
Verified food reserves after update: 440.2
[0mPOST /api/game/cycle [32m200[0m 6.727 ms - 3322[0m
[0mGET /api/modifiers/table/1 [32m200[0m 2.036 ms - 422[0m
[0mGET /api/modifiers/table/2 [32m200[0m 1.508 ms - 454[0m
[0mGET /api/modifiers/table/3 [32m200[0m 1.749 ms - 758[0m
[0mGET /api/modifiers/table/1 [36m304[0m 0.732 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 1.188 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 1.104 ms - -[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1.25,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 80.750000025
Food production calculated: 80.750000025
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
calculateNewReserves inputs: {
  currentFoodReserves: 440.2,
  foodProduction: 80.750000025,
  foodConsumption: 10
}
Perishable modifiers: {
  modifiers: [
    {
      id: 1,
      table_id: 3,
      title: 'Base',
      effect: 0.2,
      description: 'Péremption naturelle',
      start_date: null,
      created_at: '2025-04-23 22:53:21',
      updated_at: '2025-04-23 22:53:21'
    },
    {
      id: 6,
      table_id: 3,
      title: 'dingo',
      effect: 0.1,
      description: '',
      start_date: null,
      created_at: '2025-04-24 21:34:38',
      updated_at: '2025-04-24 22:01:57'
    },
    {
      id: 8,
      table_id: 3,
      title: 'Test',
      effect: -0.25,
      description: '',
      start_date: null,
      created_at: '2025-04-24 22:08:35',
      updated_at: '2025-04-24 22:08:35'
    }
  ],
  tableValue: 0.05000000000000002,
  calculatedSum: 0.050000000000000044
}
Food reserves calculation: {
  currentReserves: 440.2,
  perishableRate: 0.05000000000000002,
  foodLostToPerishable: 22.010000000000005,
  remainingAfterPerishable: 418.19,
  production: 80.750000025,
  consumption: 10,
  netProductionConsumption: 70.750000025,
  newReservesBeforeMax: 488.9,
  newReserves: 488.9
}
Checking newReserves: {
  probabilities: {
    sickness: 0.0004582347356232009,
    crime: 0.10664242539375059,
    research: 0.01
  },
  events: [],
  production: {
    foodProduction: 80.750000025,
    miningProduction: 0,
    materialsProduction: 0,
    tradingRevenue: 0,
    engineeringMultiplier: 0
  },
  consumption: {
    salaries: 280,
    charges: 0,
    foodConsumption: 10,
    craftsmanEffect: 1
  },
  newReserves: {
    newReserves: { food: 488.9, treasure: 0, materials: 0 },
    totalMineProduction: 70.57785180445435,
    mineDepth: 0,
    perishableRate: 0.05000000000000002,
    hasFamine: false
  }
}
Using calculated food reserves: 488.9
Updating food reserves to: 488.9
Verified food reserves after update: 488.9
[0mPOST /api/game/cycle [32m200[0m 6.573 ms - 3342[0m
[0mGET /api/modifiers/table/1 [32m200[0m 1.310 ms - 422[0m
[0mGET /api/modifiers/table/2 [32m200[0m 1.351 ms - 454[0m
[0mGET /api/modifiers/table/3 [32m200[0m 1.415 ms - 758[0m
[0mGET /api/modifiers/table/1 [36m304[0m 2.330 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 1.680 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 0.937 ms - -[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 64.60000002
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
[0mGET /api/game/state [32m200[0m 4.643 ms - 7468[0m
[0mGET /api/game/events [36m304[0m 0.246 ms - -[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 64.60000002
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
[0mGET /api/game/state [36m304[0m 2.319 ms - -[0m
[0mGET /api/game/events [36m304[0m 0.269 ms - -[0m
[0mGET /api/modifiers/table/2 [32m200[0m 1.195 ms - 454[0m
[0mGET /api/modifiers/table/3 [32m200[0m 1.205 ms - 758[0m
[0mGET /api/modifiers/table/1 [32m200[0m 1.183 ms - 422[0m
[0mGET /api/modifiers/table/2 [36m304[0m 0.717 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 0.712 ms - -[0m
[0mGET /api/modifiers/table/1 [36m304[0m 0.704 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 1.133 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 1.079 ms - -[0m
[0mGET /api/modifiers/table/1 [36m304[0m 1.280 ms - -[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 64.60000002
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
[0mGET /api/game/state [32m200[0m 5.030 ms - 7468[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 64.60000002
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
[0mGET /api/game/state [36m304[0m 2.574 ms - -[0m
[0mGET /api/game/events [36m304[0m 0.311 ms - -[0m
[0mGET /api/game/events [36m304[0m 0.247 ms - -[0m
[0mGET /api/modifiers/table/1 [32m200[0m 0.983 ms - 422[0m
[0mGET /api/modifiers/table/2 [32m200[0m 0.874 ms - 454[0m
[0mGET /api/modifiers/table/3 [32m200[0m 1.247 ms - 758[0m
[0mGET /api/modifiers/table/2 [36m304[0m 0.816 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 0.647 ms - -[0m
[0mGET /api/modifiers/table/1 [36m304[0m 0.906 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 1.478 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 1.192 ms - -[0m
[0mGET /api/modifiers/table/1 [36m304[0m 2.229 ms - -[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 64.60000002
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
[0mGET /api/game/state [32m200[0m 3.921 ms - 7468[0m
[0mGET /api/game/events [36m304[0m 0.190 ms - -[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 64.60000002
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
[0mGET /api/game/state [36m304[0m 2.258 ms - -[0m
[0mGET /api/game/events [36m304[0m 0.263 ms - -[0m
[0mGET /api/modifiers/table/2 [32m200[0m 0.931 ms - 454[0m
[0mGET /api/modifiers/table/1 [32m200[0m 1.346 ms - 422[0m
[0mGET /api/modifiers/table/3 [32m200[0m 1.194 ms - 758[0m
[0mGET /api/modifiers/table/1 [36m304[0m 0.734 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 0.961 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 0.662 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 0.572 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 0.783 ms - -[0m
[0mGET /api/modifiers/table/1 [36m304[0m 1.029 ms - -[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 64.60000002
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
[0mGET /api/game/state [32m200[0m 4.912 ms - 7468[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 64.60000002
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
[0mGET /api/game/state [36m304[0m 1.774 ms - -[0m
[0mGET /api/game/events [36m304[0m 0.381 ms - -[0m
[0mGET /api/game/events [36m304[0m 0.495 ms - -[0m
[0mGET /api/modifiers/table/3 [32m200[0m 0.888 ms - 758[0m
[0mGET /api/modifiers/table/1 [32m200[0m 1.377 ms - 422[0m
[0mGET /api/modifiers/table/2 [32m200[0m 1.166 ms - 454[0m
[0mGET /api/modifiers/table/3 [36m304[0m 1.146 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 0.965 ms - -[0m
[0mGET /api/modifiers/table/1 [36m304[0m 1.234 ms - -[0m
[0mGET /api/modifiers/table/1 [36m304[0m 1.025 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 1.072 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 0.976 ms - -[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 64.60000002
Food production calculated: 64.60000002
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
calculateNewReserves inputs: {
  currentFoodReserves: 488.9,
  foodProduction: 64.60000002,
  foodConsumption: 10
}
Perishable modifiers: {
  modifiers: [
    {
      id: 1,
      table_id: 3,
      title: 'Base',
      effect: 0.2,
      description: 'Péremption naturelle',
      start_date: null,
      created_at: '2025-04-23 22:53:21',
      updated_at: '2025-04-23 22:53:21'
    },
    {
      id: 6,
      table_id: 3,
      title: 'dingo',
      effect: 0.1,
      description: '',
      start_date: null,
      created_at: '2025-04-24 21:34:38',
      updated_at: '2025-04-24 22:01:57'
    },
    {
      id: 8,
      table_id: 3,
      title: 'Test',
      effect: -0.25,
      description: '',
      start_date: null,
      created_at: '2025-04-24 22:08:35',
      updated_at: '2025-04-24 22:08:35'
    }
  ],
  tableValue: 0.05000000000000002,
  calculatedSum: 0.050000000000000044
}
Food reserves calculation: {
  currentReserves: 488.9,
  perishableRate: 0.05000000000000002,
  foodLostToPerishable: 24.445000000000007,
  remainingAfterPerishable: 464.455,
  production: 64.60000002,
  consumption: 10,
  netProductionConsumption: 54.600000019999996,
  newReservesBeforeMax: 519.1,
  newReserves: 519.1
}
Checking newReserves: {
  probabilities: {
    sickness: 0.0004582347356232009,
    crime: 0.10664242539375059,
    research: 0.01
  },
  events: [],
  production: {
    foodProduction: 64.60000002,
    miningProduction: 0,
    materialsProduction: 0,
    tradingRevenue: 0,
    engineeringMultiplier: 0
  },
  consumption: {
    salaries: 280,
    charges: 0,
    foodConsumption: 10,
    craftsmanEffect: 1
  },
  newReserves: {
    newReserves: { food: 519.1, treasure: 0, materials: 0 },
    totalMineProduction: 70.57785180445435,
    mineDepth: 0,
    perishableRate: 0.05000000000000002,
    hasFamine: false
  }
}
Using calculated food reserves: 519.1
Updating food reserves to: 519.1
Verified food reserves after update: 519.1
[0mPOST /api/game/cycle [32m200[0m 7.358 ms - 3330[0m
[0mGET /api/modifiers/table/1 [32m200[0m 1.629 ms - 422[0m
[0mGET /api/modifiers/table/2 [32m200[0m 1.470 ms - 454[0m
[0mGET /api/modifiers/table/3 [32m200[0m 1.535 ms - 758[0m
[0mGET /api/modifiers/table/2 [36m304[0m 1.042 ms - -[0m
[0mGET /api/modifiers/table/1 [36m304[0m 1.481 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 1.314 ms - -[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 64.60000002
Food production calculated: 64.60000002
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
calculateNewReserves inputs: {
  currentFoodReserves: 519.1,
  foodProduction: 64.60000002,
  foodConsumption: 10
}
Perishable modifiers: {
  modifiers: [
    {
      id: 1,
      table_id: 3,
      title: 'Base',
      effect: 0.2,
      description: 'Péremption naturelle',
      start_date: null,
      created_at: '2025-04-23 22:53:21',
      updated_at: '2025-04-23 22:53:21'
    },
    {
      id: 6,
      table_id: 3,
      title: 'dingo',
      effect: 0.1,
      description: '',
      start_date: null,
      created_at: '2025-04-24 21:34:38',
      updated_at: '2025-04-24 22:01:57'
    },
    {
      id: 8,
      table_id: 3,
      title: 'Test',
      effect: -0.25,
      description: '',
      start_date: null,
      created_at: '2025-04-24 22:08:35',
      updated_at: '2025-04-24 22:08:35'
    }
  ],
  tableValue: 0.05000000000000002,
  calculatedSum: 0.050000000000000044
}
Food reserves calculation: {
  currentReserves: 519.1,
  perishableRate: 0.05000000000000002,
  foodLostToPerishable: 25.95500000000001,
  remainingAfterPerishable: 493.14500000000004,
  production: 64.60000002,
  consumption: 10,
  netProductionConsumption: 54.600000019999996,
  newReservesBeforeMax: 547.7,
  newReserves: 547.7
}
Checking newReserves: {
  probabilities: {
    sickness: 0.0004582347356232009,
    crime: 0.10664242539375059,
    research: 0.01
  },
  events: [],
  production: {
    foodProduction: 64.60000002,
    miningProduction: 0,
    materialsProduction: 0,
    tradingRevenue: 0,
    engineeringMultiplier: 0
  },
  consumption: {
    salaries: 280,
    charges: 0,
    foodConsumption: 10,
    craftsmanEffect: 1
  },
  newReserves: {
    newReserves: { food: 547.7, treasure: 0, materials: 0 },
    totalMineProduction: 70.57785180445435,
    mineDepth: 0,
    perishableRate: 0.05000000000000002,
    hasFamine: false
  }
}
Using calculated food reserves: 547.7
Updating food reserves to: 547.7
Verified food reserves after update: 547.7
[0mPOST /api/game/cycle [32m200[0m 7.178 ms - 3579[0m
[0mGET /api/modifiers/table/2 [32m200[0m 2.332 ms - 454[0m
[0mGET /api/modifiers/table/3 [32m200[0m 2.253 ms - 758[0m
[0mGET /api/modifiers/table/1 [32m200[0m 2.615 ms - 422[0m
[0mGET /api/modifiers/table/2 [36m304[0m 1.706 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 1.456 ms - -[0m
[0mGET /api/modifiers/table/1 [36m304[0m 1.327 ms - -[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 64.60000002
Food production calculated: 64.60000002
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
calculateNewReserves inputs: {
  currentFoodReserves: 547.7,
  foodProduction: 64.60000002,
  foodConsumption: 10
}
Perishable modifiers: {
  modifiers: [
    {
      id: 1,
      table_id: 3,
      title: 'Base',
      effect: 0.2,
      description: 'Péremption naturelle',
      start_date: null,
      created_at: '2025-04-23 22:53:21',
      updated_at: '2025-04-23 22:53:21'
    },
    {
      id: 6,
      table_id: 3,
      title: 'dingo',
      effect: 0.1,
      description: '',
      start_date: null,
      created_at: '2025-04-24 21:34:38',
      updated_at: '2025-04-24 22:01:57'
    },
    {
      id: 8,
      table_id: 3,
      title: 'Test',
      effect: -0.25,
      description: '',
      start_date: null,
      created_at: '2025-04-24 22:08:35',
      updated_at: '2025-04-24 22:08:35'
    }
  ],
  tableValue: 0.05000000000000002,
  calculatedSum: 0.050000000000000044
}
Food reserves calculation: {
  currentReserves: 547.7,
  perishableRate: 0.05000000000000002,
  foodLostToPerishable: 27.385000000000012,
  remainingAfterPerishable: 520.315,
  production: 64.60000002,
  consumption: 10,
  netProductionConsumption: 54.600000019999996,
  newReservesBeforeMax: 574.9,
  newReserves: 574.9
}
Checking newReserves: {
  probabilities: {
    sickness: 0.0004582347356232009,
    crime: 0.10664242539375059,
    research: 0.01
  },
  events: [
    {
      type: 'CRIME',
      description: "Un crime a été commis! Les protecteurs enquêtent sur l'affaire."
    }
  ],
  production: {
    foodProduction: 64.60000002,
    miningProduction: 0,
    materialsProduction: 0,
    tradingRevenue: 0,
    engineeringMultiplier: 0
  },
  consumption: {
    salaries: 280,
    charges: 0,
    foodConsumption: 10,
    craftsmanEffect: 1
  },
  newReserves: {
    newReserves: { food: 574.9, treasure: 0, materials: 0 },
    totalMineProduction: 70.57785180445435,
    mineDepth: 0,
    perishableRate: 0.05000000000000002,
    hasFamine: false
  }
}
Using calculated food reserves: 574.9
Updating food reserves to: 574.9
Verified food reserves after update: 574.9
[0mPOST /api/game/cycle [32m200[0m 7.112 ms - 3236[0m
[0mGET /api/modifiers/table/2 [32m200[0m 1.544 ms - 454[0m
[0mGET /api/modifiers/table/1 [32m200[0m 1.554 ms - 422[0m
[0mGET /api/modifiers/table/3 [32m200[0m 1.916 ms - 758[0m
[0mGET /api/modifiers/table/2 [36m304[0m 1.593 ms - -[0m
[0mGET /api/modifiers/table/1 [36m304[0m 1.232 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 1.048 ms - -[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 0.5,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 32.30000001
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
[0mGET /api/game/state [32m200[0m 4.199 ms - 7376[0m
[0mGET /api/game/events [32m200[0m 0.184 ms - 1688[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 0.5,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 32.30000001
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
[0mGET /api/game/state [36m304[0m 2.208 ms - -[0m
[0mGET /api/game/events [36m304[0m 0.276 ms - -[0m
[0mGET /api/modifiers/table/2 [32m200[0m 1.058 ms - 454[0m
[0mGET /api/modifiers/table/3 [32m200[0m 1.176 ms - 758[0m
[0mGET /api/modifiers/table/1 [32m200[0m 1.344 ms - 422[0m
[0mGET /api/modifiers/table/2 [36m304[0m 1.117 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 1.174 ms - -[0m
[0mGET /api/modifiers/table/1 [36m304[0m 1.212 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 0.624 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 0.712 ms - -[0m
[0mGET /api/modifiers/table/1 [36m304[0m 1.271 ms - -[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 0.5,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 32.30000001
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
[0mGET /api/game/state [32m200[0m 13.511 ms - 7376[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 0.5,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 32.30000001
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
[0mGET /api/game/state [36m304[0m 3.602 ms - -[0m
[0mGET /api/game/events [36m304[0m 0.387 ms - -[0m
[0mGET /api/game/events [36m304[0m 0.333 ms - -[0m
[0mGET /api/modifiers/table/1 [32m200[0m 1.953 ms - 422[0m
[0mGET /api/modifiers/table/3 [32m200[0m 1.468 ms - 758[0m
[0mGET /api/modifiers/table/2 [32m200[0m 2.120 ms - 454[0m
[0mGET /api/modifiers/table/1 [36m304[0m 0.998 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 1.425 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 1.497 ms - -[0m
[0mGET /api/modifiers/table/1 [36m304[0m 1.662 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 1.198 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 1.725 ms - -[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 0.5,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 32.30000001
Food production calculated: 32.30000001
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
calculateNewReserves inputs: {
  currentFoodReserves: 574.9,
  foodProduction: 32.30000001,
  foodConsumption: 10
}
Perishable modifiers: {
  modifiers: [
    {
      id: 1,
      table_id: 3,
      title: 'Base',
      effect: 0.2,
      description: 'Péremption naturelle',
      start_date: null,
      created_at: '2025-04-23 22:53:21',
      updated_at: '2025-04-23 22:53:21'
    },
    {
      id: 6,
      table_id: 3,
      title: 'dingo',
      effect: 0.1,
      description: '',
      start_date: null,
      created_at: '2025-04-24 21:34:38',
      updated_at: '2025-04-24 22:01:57'
    },
    {
      id: 8,
      table_id: 3,
      title: 'Test',
      effect: -0.25,
      description: '',
      start_date: null,
      created_at: '2025-04-24 22:08:35',
      updated_at: '2025-04-24 22:08:35'
    }
  ],
  tableValue: 0.05000000000000002,
  calculatedSum: 0.050000000000000044
}
Food reserves calculation: {
  currentReserves: 574.9,
  perishableRate: 0.05000000000000002,
  foodLostToPerishable: 28.745000000000008,
  remainingAfterPerishable: 546.155,
  production: 32.30000001,
  consumption: 10,
  netProductionConsumption: 22.300000009999998,
  newReservesBeforeMax: 568.5,
  newReserves: 568.5
}
Checking newReserves: {
  probabilities: {
    sickness: 0.0004582347356232009,
    crime: 0.10664242539375059,
    research: 0.01
  },
  events: [],
  production: {
    foodProduction: 32.30000001,
    miningProduction: 0,
    materialsProduction: 0,
    tradingRevenue: 0,
    engineeringMultiplier: 0
  },
  consumption: {
    salaries: 280,
    charges: 0,
    foodConsumption: 10,
    craftsmanEffect: 1
  },
  newReserves: {
    newReserves: { food: 568.5, treasure: 0, materials: 0 },
    totalMineProduction: 70.57785180445435,
    mineDepth: 0,
    perishableRate: 0.05000000000000002,
    hasFamine: false
  }
}
Using calculated food reserves: 568.5
Updating food reserves to: 568.5
Verified food reserves after update: 568.5
[0mPOST /api/game/cycle [32m200[0m 3.609 ms - 3265[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 0.5,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 32.30000001
Food production calculated: 32.30000001
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
calculateNewReserves inputs: {
  currentFoodReserves: 568.5,
  foodProduction: 32.30000001,
  foodConsumption: 10
}
Perishable modifiers: {
  modifiers: [
    {
      id: 1,
      table_id: 3,
      title: 'Base',
      effect: 0.2,
      description: 'Péremption naturelle',
      start_date: null,
      created_at: '2025-04-23 22:53:21',
      updated_at: '2025-04-23 22:53:21'
    },
    {
      id: 6,
      table_id: 3,
      title: 'dingo',
      effect: 0.1,
      description: '',
      start_date: null,
      created_at: '2025-04-24 21:34:38',
      updated_at: '2025-04-24 22:01:57'
    },
    {
      id: 8,
      table_id: 3,
      title: 'Test',
      effect: -0.25,
      description: '',
      start_date: null,
      created_at: '2025-04-24 22:08:35',
      updated_at: '2025-04-24 22:08:35'
    }
  ],
  tableValue: 0.05000000000000002,
  calculatedSum: 0.050000000000000044
}
Food reserves calculation: {
  currentReserves: 568.5,
  perishableRate: 0.05000000000000002,
  foodLostToPerishable: 28.425000000000008,
  remainingAfterPerishable: 540.075,
  production: 32.30000001,
  consumption: 10,
  netProductionConsumption: 22.300000009999998,
  newReservesBeforeMax: 562.4,
  newReserves: 562.4
}
Checking newReserves: {
  probabilities: {
    sickness: 0.0004582347356232009,
    crime: 0.10664242539375059,
    research: 0.01
  },
  events: [],
  production: {
    foodProduction: 32.30000001,
    miningProduction: 0,
    materialsProduction: 0,
    tradingRevenue: 0,
    engineeringMultiplier: 0
  },
  consumption: {
    salaries: 280,
    charges: 0,
    foodConsumption: 10,
    craftsmanEffect: 1
  },
  newReserves: {
    newReserves: { food: 562.4, treasure: 0, materials: 0 },
    totalMineProduction: 70.57785180445435,
    mineDepth: 0,
    perishableRate: 0.05000000000000002,
    hasFamine: false
  }
}
Using calculated food reserves: 562.4
Updating food reserves to: 562.4
Verified food reserves after update: 562.4
[0mPOST /api/game/cycle [32m200[0m 7.918 ms - 3258[0m
[0mGET /api/modifiers/table/1 [32m200[0m 2.565 ms - 422[0m
[0mGET /api/modifiers/table/2 [32m200[0m 2.565 ms - 454[0m
[0mGET /api/modifiers/table/3 [32m200[0m 2.355 ms - 758[0m
[0mGET /api/modifiers/table/2 [36m304[0m 1.062 ms - -[0m
[0mGET /api/modifiers/table/1 [36m304[0m 1.538 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 1.576 ms - -[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 0.5,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 32.30000001
Food production calculated: 32.30000001
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
calculateNewReserves inputs: {
  currentFoodReserves: 562.4,
  foodProduction: 32.30000001,
  foodConsumption: 10
}
Perishable modifiers: {
  modifiers: [
    {
      id: 1,
      table_id: 3,
      title: 'Base',
      effect: 0.2,
      description: 'Péremption naturelle',
      start_date: null,
      created_at: '2025-04-23 22:53:21',
      updated_at: '2025-04-23 22:53:21'
    },
    {
      id: 6,
      table_id: 3,
      title: 'dingo',
      effect: 0.1,
      description: '',
      start_date: null,
      created_at: '2025-04-24 21:34:38',
      updated_at: '2025-04-24 22:01:57'
    },
    {
      id: 8,
      table_id: 3,
      title: 'Test',
      effect: -0.25,
      description: '',
      start_date: null,
      created_at: '2025-04-24 22:08:35',
      updated_at: '2025-04-24 22:08:35'
    }
  ],
  tableValue: 0.05000000000000002,
  calculatedSum: 0.050000000000000044
}
Food reserves calculation: {
  currentReserves: 562.4,
  perishableRate: 0.05000000000000002,
  foodLostToPerishable: 28.120000000000008,
  remainingAfterPerishable: 534.28,
  production: 32.30000001,
  consumption: 10,
  netProductionConsumption: 22.300000009999998,
  newReservesBeforeMax: 556.6,
  newReserves: 556.6
}
Checking newReserves: {
  probabilities: {
    sickness: 0.0004582347356232009,
    crime: 0.10664242539375059,
    research: 0.01
  },
  events: [
    {
      type: 'CRIME',
      description: "Un crime a été commis! Les protecteurs enquêtent sur l'affaire."
    }
  ],
  production: {
    foodProduction: 32.30000001,
    miningProduction: 0,
    materialsProduction: 0,
    tradingRevenue: 0,
    engineeringMultiplier: 0
  },
  consumption: {
    salaries: 280,
    charges: 0,
    foodConsumption: 10,
    craftsmanEffect: 1
  },
  newReserves: {
    newReserves: { food: 556.6, treasure: 0, materials: 0 },
    totalMineProduction: 70.57785180445435,
    mineDepth: 0,
    perishableRate: 0.05000000000000002,
    hasFamine: false
  }
}
Using calculated food reserves: 556.6
Updating food reserves to: 556.6
Verified food reserves after update: 556.6
[0mPOST /api/game/cycle [32m200[0m 8.375 ms - 3337[0m
[0mGET /api/modifiers/table/1 [32m200[0m 0.885 ms - 422[0m
[0mGET /api/modifiers/table/2 [32m200[0m 1.151 ms - 454[0m
[0mGET /api/modifiers/table/3 [32m200[0m 1.270 ms - 758[0m
[0mGET /api/modifiers/table/1 [36m304[0m 0.988 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 1.043 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 1.092 ms - -[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 64.60000002
Food production calculated: 64.60000002
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
calculateNewReserves inputs: {
  currentFoodReserves: 556.6,
  foodProduction: 64.60000002,
  foodConsumption: 10
}
Perishable modifiers: {
  modifiers: [
    {
      id: 1,
      table_id: 3,
      title: 'Base',
      effect: 0.2,
      description: 'Péremption naturelle',
      start_date: null,
      created_at: '2025-04-23 22:53:21',
      updated_at: '2025-04-23 22:53:21'
    },
    {
      id: 6,
      table_id: 3,
      title: 'dingo',
      effect: 0.1,
      description: '',
      start_date: null,
      created_at: '2025-04-24 21:34:38',
      updated_at: '2025-04-24 22:01:57'
    },
    {
      id: 8,
      table_id: 3,
      title: 'Test',
      effect: -0.25,
      description: '',
      start_date: null,
      created_at: '2025-04-24 22:08:35',
      updated_at: '2025-04-24 22:08:35'
    }
  ],
  tableValue: 0.05000000000000002,
  calculatedSum: 0.050000000000000044
}
Food reserves calculation: {
  currentReserves: 556.6,
  perishableRate: 0.05000000000000002,
  foodLostToPerishable: 27.83000000000001,
  remainingAfterPerishable: 528.77,
  production: 64.60000002,
  consumption: 10,
  netProductionConsumption: 54.600000019999996,
  newReservesBeforeMax: 583.4,
  newReserves: 583.4
}
Checking newReserves: {
  probabilities: {
    sickness: 0.0004582347356232009,
    crime: 0.10664242539375059,
    research: 0.01
  },
  events: [],
  production: {
    foodProduction: 64.60000002,
    miningProduction: 0,
    materialsProduction: 0,
    tradingRevenue: 0,
    engineeringMultiplier: 0
  },
  consumption: {
    salaries: 280,
    charges: 0,
    foodConsumption: 10,
    craftsmanEffect: 1
  },
  newReserves: {
    newReserves: { food: 583.4, treasure: 0, materials: 0 },
    totalMineProduction: 70.57785180445435,
    mineDepth: 0,
    perishableRate: 0.05000000000000002,
    hasFamine: false
  }
}
Using calculated food reserves: 583.4
Updating food reserves to: 583.4
Verified food reserves after update: 583.4
[0mPOST /api/game/cycle [32m200[0m 8.674 ms - 3304[0m
[0mGET /api/modifiers/table/1 [32m200[0m 1.038 ms - 422[0m
[0mGET /api/modifiers/table/3 [32m200[0m 1.123 ms - 758[0m
[0mGET /api/modifiers/table/2 [32m200[0m 1.241 ms - 454[0m
[0mGET /api/modifiers/table/1 [36m304[0m 0.844 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 0.791 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 0.779 ms - -[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 64.60000002
Food production calculated: 64.60000002
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
calculateNewReserves inputs: {
  currentFoodReserves: 583.4,
  foodProduction: 64.60000002,
  foodConsumption: 10
}
Perishable modifiers: {
  modifiers: [
    {
      id: 1,
      table_id: 3,
      title: 'Base',
      effect: 0.2,
      description: 'Péremption naturelle',
      start_date: null,
      created_at: '2025-04-23 22:53:21',
      updated_at: '2025-04-23 22:53:21'
    },
    {
      id: 6,
      table_id: 3,
      title: 'dingo',
      effect: 0.1,
      description: '',
      start_date: null,
      created_at: '2025-04-24 21:34:38',
      updated_at: '2025-04-24 22:01:57'
    },
    {
      id: 8,
      table_id: 3,
      title: 'Test',
      effect: -0.25,
      description: '',
      start_date: null,
      created_at: '2025-04-24 22:08:35',
      updated_at: '2025-04-24 22:08:35'
    }
  ],
  tableValue: 0.05000000000000002,
  calculatedSum: 0.050000000000000044
}
Food reserves calculation: {
  currentReserves: 583.4,
  perishableRate: 0.05000000000000002,
  foodLostToPerishable: 29.17000000000001,
  remainingAfterPerishable: 554.23,
  production: 64.60000002,
  consumption: 10,
  netProductionConsumption: 54.600000019999996,
  newReservesBeforeMax: 608.8,
  newReserves: 608.8
}
Checking newReserves: {
  probabilities: {
    sickness: 0.0004582347356232009,
    crime: 0.10664242539375059,
    research: 0.01
  },
  events: [
    {
      type: 'CRIME',
      description: "Un crime a été commis! Les protecteurs enquêtent sur l'affaire."
    }
  ],
  production: {
    foodProduction: 64.60000002,
    miningProduction: 0,
    materialsProduction: 0,
    tradingRevenue: 0,
    engineeringMultiplier: 0
  },
  consumption: {
    salaries: 280,
    charges: 0,
    foodConsumption: 10,
    craftsmanEffect: 1
  },
  newReserves: {
    newReserves: { food: 608.8, treasure: 0, materials: 0 },
    totalMineProduction: 70.57785180445435,
    mineDepth: 0,
    perishableRate: 0.05000000000000002,
    hasFamine: false
  }
}
Using calculated food reserves: 608.8
Updating food reserves to: 608.8
Verified food reserves after update: 608.8
[0mPOST /api/game/cycle [32m200[0m 3.781 ms - 3293[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 64.60000002
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
[0mGET /api/game/state [32m200[0m 4.750 ms - 7448[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 64.60000002
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
[0mGET /api/game/state [36m304[0m 1.589 ms - -[0m
[0mGET /api/game/events [32m200[0m 0.272 ms - 1650[0m
[0mGET /api/game/events [36m304[0m 0.247 ms - -[0m
[0mGET /api/modifiers/table/1 [32m200[0m 1.248 ms - 422[0m
[0mGET /api/modifiers/table/2 [32m200[0m 1.297 ms - 454[0m
[0mGET /api/modifiers/table/3 [32m200[0m 1.259 ms - 758[0m
[0mGET /api/modifiers/table/1 [36m304[0m 0.577 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 0.590 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 0.705 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 0.488 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 0.504 ms - -[0m
[0mGET /api/modifiers/table/1 [36m304[0m 0.799 ms - -[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 64.60000002
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
[0mGET /api/game/state [32m200[0m 3.945 ms - 7448[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 64.60000002
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
[0mGET /api/game/state [36m304[0m 2.728 ms - -[0m
[0mGET /api/game/events [36m304[0m 0.311 ms - -[0m
[0mGET /api/game/events [36m304[0m 0.237 ms - -[0m
[0mGET /api/modifiers/table/2 [32m200[0m 0.819 ms - 454[0m
[0mGET /api/modifiers/table/1 [32m200[0m 1.266 ms - 422[0m
[0mGET /api/modifiers/table/3 [32m200[0m 1.242 ms - 758[0m
[0mGET /api/modifiers/table/1 [36m304[0m 0.871 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 1.027 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 1.365 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 0.883 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 1.039 ms - -[0m
[0mGET /api/modifiers/table/1 [36m304[0m 1.564 ms - -[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 64.60000002
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
[0mGET /api/game/state [32m200[0m 3.708 ms - 7448[0m
[0mGET /api/game/events [36m304[0m 0.200 ms - -[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 64.60000002
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
[0mGET /api/game/state [36m304[0m 2.116 ms - -[0m
[0mGET /api/game/events [36m304[0m 0.332 ms - -[0m
[0mGET /api/modifiers/table/2 [32m200[0m 0.618 ms - 454[0m
[0mGET /api/modifiers/table/1 [32m200[0m 0.940 ms - 422[0m
[0mGET /api/modifiers/table/3 [32m200[0m 0.855 ms - 758[0m
[0mGET /api/modifiers/table/1 [36m304[0m 0.560 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 0.620 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 1.103 ms - -[0m
[0mGET /api/modifiers/table/1 [36m304[0m 0.898 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 0.766 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 0.927 ms - -[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 64.60000002
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
[0mGET /api/game/state [32m200[0m 5.215 ms - 7448[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 64.60000002
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
[0mGET /api/game/state [36m304[0m 2.515 ms - -[0m
[0mGET /api/game/events [36m304[0m 0.352 ms - -[0m
[0mGET /api/game/events [36m304[0m 0.261 ms - -[0m
[0mGET /api/modifiers/table/1 [32m200[0m 0.993 ms - 422[0m
[0mGET /api/modifiers/table/2 [32m200[0m 1.082 ms - 454[0m
[0mGET /api/modifiers/table/3 [32m200[0m 1.197 ms - 758[0m
[0mGET /api/modifiers/table/1 [36m304[0m 1.026 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 1.063 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 1.045 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 0.759 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 1.018 ms - -[0m
[0mGET /api/modifiers/table/1 [36m304[0m 1.531 ms - -[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 64.60000002
Food production calculated: 64.60000002
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
calculateNewReserves inputs: {
  currentFoodReserves: 608.8,
  foodProduction: 64.60000002,
  foodConsumption: 10
}
Perishable modifiers: {
  modifiers: [
    {
      id: 1,
      table_id: 3,
      title: 'Base',
      effect: 0.2,
      description: 'Péremption naturelle',
      start_date: null,
      created_at: '2025-04-23 22:53:21',
      updated_at: '2025-04-23 22:53:21'
    },
    {
      id: 6,
      table_id: 3,
      title: 'dingo',
      effect: 0.1,
      description: '',
      start_date: null,
      created_at: '2025-04-24 21:34:38',
      updated_at: '2025-04-24 22:01:57'
    },
    {
      id: 8,
      table_id: 3,
      title: 'Test',
      effect: -0.25,
      description: '',
      start_date: null,
      created_at: '2025-04-24 22:08:35',
      updated_at: '2025-04-24 22:08:35'
    }
  ],
  tableValue: 0.05000000000000002,
  calculatedSum: 0.050000000000000044
}
Food reserves calculation: {
  currentReserves: 608.8,
  perishableRate: 0.05000000000000002,
  foodLostToPerishable: 30.44000000000001,
  remainingAfterPerishable: 578.3599999999999,
  production: 64.60000002,
  consumption: 10,
  netProductionConsumption: 54.600000019999996,
  newReservesBeforeMax: 633,
  newReserves: 633
}
Checking newReserves: {
  probabilities: {
    sickness: 0.0004582347356232009,
    crime: 0.10664242539375059,
    research: 0.01
  },
  events: [],
  production: {
    foodProduction: 64.60000002,
    miningProduction: 0,
    materialsProduction: 0,
    tradingRevenue: 0,
    engineeringMultiplier: 0
  },
  consumption: {
    salaries: 280,
    charges: 0,
    foodConsumption: 10,
    craftsmanEffect: 1
  },
  newReserves: {
    newReserves: { food: 633, treasure: 0, materials: 0 },
    totalMineProduction: 70.57785180445435,
    mineDepth: 0,
    perishableRate: 0.05000000000000002,
    hasFamine: false
  }
}
Using calculated food reserves: 633
Updating food reserves to: 633
Verified food reserves after update: 633
[0mPOST /api/game/cycle [32m200[0m 3.949 ms - 3289[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1.25,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 80.750000025
Food production calculated: 80.750000025
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
calculateNewReserves inputs: {
  currentFoodReserves: 633,
  foodProduction: 80.750000025,
  foodConsumption: 10
}
Perishable modifiers: {
  modifiers: [
    {
      id: 1,
      table_id: 3,
      title: 'Base',
      effect: 0.2,
      description: 'Péremption naturelle',
      start_date: null,
      created_at: '2025-04-23 22:53:21',
      updated_at: '2025-04-23 22:53:21'
    },
    {
      id: 6,
      table_id: 3,
      title: 'dingo',
      effect: 0.1,
      description: '',
      start_date: null,
      created_at: '2025-04-24 21:34:38',
      updated_at: '2025-04-24 22:01:57'
    },
    {
      id: 8,
      table_id: 3,
      title: 'Test',
      effect: -0.25,
      description: '',
      start_date: null,
      created_at: '2025-04-24 22:08:35',
      updated_at: '2025-04-24 22:08:35'
    }
  ],
  tableValue: 0.05000000000000002,
  calculatedSum: 0.050000000000000044
}
Food reserves calculation: {
  currentReserves: 633,
  perishableRate: 0.05000000000000002,
  foodLostToPerishable: 31.65000000000001,
  remainingAfterPerishable: 601.35,
  production: 80.750000025,
  consumption: 10,
  netProductionConsumption: 70.750000025,
  newReservesBeforeMax: 672.1,
  newReserves: 672.1
}
Checking newReserves: {
  probabilities: {
    sickness: 0.0004582347356232009,
    crime: 0.10664242539375059,
    research: 0.01
  },
  events: [],
  production: {
    foodProduction: 80.750000025,
    miningProduction: 0,
    materialsProduction: 0,
    tradingRevenue: 0,
    engineeringMultiplier: 0
  },
  consumption: {
    salaries: 280,
    charges: 0,
    foodConsumption: 10,
    craftsmanEffect: 1
  },
  newReserves: {
    newReserves: { food: 672.1, treasure: 0, materials: 0 },
    totalMineProduction: 70.57785180445435,
    mineDepth: 0,
    perishableRate: 0.05000000000000002,
    hasFamine: false
  }
}
Using calculated food reserves: 672.1
Updating food reserves to: 672.1
Verified food reserves after update: 672.1
[0mPOST /api/game/cycle [32m200[0m 8.673 ms - 3270[0m
[0mGET /api/modifiers/table/1 [32m200[0m 1.936 ms - 422[0m
[0mGET /api/modifiers/table/2 [32m200[0m 1.922 ms - 454[0m
[0mGET /api/modifiers/table/3 [32m200[0m 2.042 ms - 758[0m
[0mGET /api/modifiers/table/1 [36m304[0m 1.608 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 1.403 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 1.305 ms - -[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1.25,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 80.750000025
Food production calculated: 80.750000025
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
calculateNewReserves inputs: {
  currentFoodReserves: 672.1,
  foodProduction: 80.750000025,
  foodConsumption: 10
}
Perishable modifiers: {
  modifiers: [
    {
      id: 1,
      table_id: 3,
      title: 'Base',
      effect: 0.2,
      description: 'Péremption naturelle',
      start_date: null,
      created_at: '2025-04-23 22:53:21',
      updated_at: '2025-04-23 22:53:21'
    },
    {
      id: 6,
      table_id: 3,
      title: 'dingo',
      effect: 0.1,
      description: '',
      start_date: null,
      created_at: '2025-04-24 21:34:38',
      updated_at: '2025-04-24 22:01:57'
    },
    {
      id: 8,
      table_id: 3,
      title: 'Test',
      effect: -0.25,
      description: '',
      start_date: null,
      created_at: '2025-04-24 22:08:35',
      updated_at: '2025-04-24 22:08:35'
    }
  ],
  tableValue: 0.05000000000000002,
  calculatedSum: 0.050000000000000044
}
Food reserves calculation: {
  currentReserves: 672.1,
  perishableRate: 0.05000000000000002,
  foodLostToPerishable: 33.60500000000001,
  remainingAfterPerishable: 638.495,
  production: 80.750000025,
  consumption: 10,
  netProductionConsumption: 70.750000025,
  newReservesBeforeMax: 709.2,
  newReserves: 709.2
}
Checking newReserves: {
  probabilities: {
    sickness: 0.0004582347356232009,
    crime: 0.10664242539375059,
    research: 0.01
  },
  events: [],
  production: {
    foodProduction: 80.750000025,
    miningProduction: 0,
    materialsProduction: 0,
    tradingRevenue: 0,
    engineeringMultiplier: 0
  },
  consumption: {
    salaries: 280,
    charges: 0,
    foodConsumption: 10,
    craftsmanEffect: 1
  },
  newReserves: {
    newReserves: { food: 709.2, treasure: 0, materials: 0 },
    totalMineProduction: 70.57785180445435,
    mineDepth: 0,
    perishableRate: 0.05000000000000002,
    hasFamine: false
  }
}
Using calculated food reserves: 709.2
Updating food reserves to: 709.2
Verified food reserves after update: 709.2
[0mPOST /api/game/cycle [32m200[0m 8.244 ms - 3294[0m
[0mGET /api/modifiers/table/1 [32m200[0m 1.266 ms - 422[0m
[0mGET /api/modifiers/table/3 [32m200[0m 1.347 ms - 758[0m
[0mGET /api/modifiers/table/2 [32m200[0m 1.367 ms - 454[0m
[0mGET /api/modifiers/table/2 [36m304[0m 0.630 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 0.851 ms - -[0m
[0mGET /api/modifiers/table/1 [36m304[0m 1.283 ms - -[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1.25,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 80.750000025
Food production calculated: 80.750000025
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
calculateNewReserves inputs: {
  currentFoodReserves: 709.2,
  foodProduction: 80.750000025,
  foodConsumption: 10
}
Perishable modifiers: {
  modifiers: [
    {
      id: 1,
      table_id: 3,
      title: 'Base',
      effect: 0.2,
      description: 'Péremption naturelle',
      start_date: null,
      created_at: '2025-04-23 22:53:21',
      updated_at: '2025-04-23 22:53:21'
    },
    {
      id: 6,
      table_id: 3,
      title: 'dingo',
      effect: 0.1,
      description: '',
      start_date: null,
      created_at: '2025-04-24 21:34:38',
      updated_at: '2025-04-24 22:01:57'
    },
    {
      id: 8,
      table_id: 3,
      title: 'Test',
      effect: -0.25,
      description: '',
      start_date: null,
      created_at: '2025-04-24 22:08:35',
      updated_at: '2025-04-24 22:08:35'
    }
  ],
  tableValue: 0.05000000000000002,
  calculatedSum: 0.050000000000000044
}
Food reserves calculation: {
  currentReserves: 709.2,
  perishableRate: 0.05000000000000002,
  foodLostToPerishable: 35.460000000000015,
  remainingAfterPerishable: 673.74,
  production: 80.750000025,
  consumption: 10,
  netProductionConsumption: 70.750000025,
  newReservesBeforeMax: 744.5,
  newReserves: 744.5
}
Checking newReserves: {
  probabilities: {
    sickness: 0.0004582347356232009,
    crime: 0.10664242539375059,
    research: 0.01
  },
  events: [
    {
      type: 'CRIME',
      description: "Un crime a été commis! Les protecteurs enquêtent sur l'affaire."
    }
  ],
  production: {
    foodProduction: 80.750000025,
    miningProduction: 0,
    materialsProduction: 0,
    tradingRevenue: 0,
    engineeringMultiplier: 0
  },
  consumption: {
    salaries: 280,
    charges: 0,
    foodConsumption: 10,
    craftsmanEffect: 1
  },
  newReserves: {
    newReserves: { food: 744.5, treasure: 0, materials: 0 },
    totalMineProduction: 70.57785180445435,
    mineDepth: 0,
    perishableRate: 0.05000000000000002,
    hasFamine: false
  }
}
Using calculated food reserves: 744.5
Updating food reserves to: 744.5
Verified food reserves after update: 744.5
[0mPOST /api/game/cycle [32m200[0m 9.552 ms - 3315[0m
[0mGET /api/modifiers/table/2 [32m200[0m 1.509 ms - 454[0m
[0mGET /api/modifiers/table/3 [32m200[0m 2.190 ms - 758[0m
[0mGET /api/modifiers/table/1 [32m200[0m 2.724 ms - 422[0m
[0mGET /api/modifiers/table/2 [36m304[0m 1.047 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 1.034 ms - -[0m
[0mGET /api/modifiers/table/1 [36m304[0m 0.894 ms - -[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 64.60000002
Food production calculated: 64.60000002
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
calculateNewReserves inputs: {
  currentFoodReserves: 744.5,
  foodProduction: 64.60000002,
  foodConsumption: 10
}
Perishable modifiers: {
  modifiers: [
    {
      id: 1,
      table_id: 3,
      title: 'Base',
      effect: 0.2,
      description: 'Péremption naturelle',
      start_date: null,
      created_at: '2025-04-23 22:53:21',
      updated_at: '2025-04-23 22:53:21'
    },
    {
      id: 6,
      table_id: 3,
      title: 'dingo',
      effect: 0.1,
      description: '',
      start_date: null,
      created_at: '2025-04-24 21:34:38',
      updated_at: '2025-04-24 22:01:57'
    },
    {
      id: 8,
      table_id: 3,
      title: 'Test',
      effect: -0.25,
      description: '',
      start_date: null,
      created_at: '2025-04-24 22:08:35',
      updated_at: '2025-04-24 22:08:35'
    }
  ],
  tableValue: 0.05000000000000002,
  calculatedSum: 0.050000000000000044
}
Food reserves calculation: {
  currentReserves: 744.5,
  perishableRate: 0.05000000000000002,
  foodLostToPerishable: 37.225000000000016,
  remainingAfterPerishable: 707.275,
  production: 64.60000002,
  consumption: 10,
  netProductionConsumption: 54.600000019999996,
  newReservesBeforeMax: 761.9,
  newReserves: 761.9
}
Checking newReserves: {
  probabilities: {
    sickness: 0.0004582347356232009,
    crime: 0.10664242539375059,
    research: 0.01
  },
  events: [],
  production: {
    foodProduction: 64.60000002,
    miningProduction: 0,
    materialsProduction: 0,
    tradingRevenue: 0,
    engineeringMultiplier: 0
  },
  consumption: {
    salaries: 280,
    charges: 0,
    foodConsumption: 10,
    craftsmanEffect: 1
  },
  newReserves: {
    newReserves: { food: 761.9, treasure: 0, materials: 0 },
    totalMineProduction: 70.57785180445435,
    mineDepth: 0,
    perishableRate: 0.05000000000000002,
    hasFamine: false
  }
}
Using calculated food reserves: 761.9
Updating food reserves to: 761.9
Verified food reserves after update: 761.9
[0mPOST /api/game/cycle [32m200[0m 7.982 ms - 3303[0m
[0mGET /api/modifiers/table/1 [32m200[0m 1.020 ms - 422[0m
[0mGET /api/modifiers/table/2 [32m200[0m 1.090 ms - 454[0m
[0mGET /api/modifiers/table/3 [32m200[0m 1.210 ms - 758[0m
[0mGET /api/modifiers/table/1 [36m304[0m 0.767 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 0.935 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 0.999 ms - -[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 64.60000002
Food production calculated: 64.60000002
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
calculateNewReserves inputs: {
  currentFoodReserves: 761.9,
  foodProduction: 64.60000002,
  foodConsumption: 10
}
Perishable modifiers: {
  modifiers: [
    {
      id: 1,
      table_id: 3,
      title: 'Base',
      effect: 0.2,
      description: 'Péremption naturelle',
      start_date: null,
      created_at: '2025-04-23 22:53:21',
      updated_at: '2025-04-23 22:53:21'
    },
    {
      id: 6,
      table_id: 3,
      title: 'dingo',
      effect: 0.1,
      description: '',
      start_date: null,
      created_at: '2025-04-24 21:34:38',
      updated_at: '2025-04-24 22:01:57'
    },
    {
      id: 8,
      table_id: 3,
      title: 'Test',
      effect: -0.25,
      description: '',
      start_date: null,
      created_at: '2025-04-24 22:08:35',
      updated_at: '2025-04-24 22:08:35'
    }
  ],
  tableValue: 0.05000000000000002,
  calculatedSum: 0.050000000000000044
}
Food reserves calculation: {
  currentReserves: 761.9,
  perishableRate: 0.05000000000000002,
  foodLostToPerishable: 38.09500000000001,
  remainingAfterPerishable: 723.805,
  production: 64.60000002,
  consumption: 10,
  netProductionConsumption: 54.600000019999996,
  newReservesBeforeMax: 778.4,
  newReserves: 778.4
}
Checking newReserves: {
  probabilities: {
    sickness: 0.0004582347356232009,
    crime: 0.10664242539375059,
    research: 0.01
  },
  events: [],
  production: {
    foodProduction: 64.60000002,
    miningProduction: 0,
    materialsProduction: 0,
    tradingRevenue: 0,
    engineeringMultiplier: 0
  },
  consumption: {
    salaries: 280,
    charges: 0,
    foodConsumption: 10,
    craftsmanEffect: 1
  },
  newReserves: {
    newReserves: { food: 778.4, treasure: 0, materials: 0 },
    totalMineProduction: 70.57785180445435,
    mineDepth: 0,
    perishableRate: 0.05000000000000002,
    hasFamine: false
  }
}
Using calculated food reserves: 778.4
Updating food reserves to: 778.4
Verified food reserves after update: 778.4
[0mPOST /api/game/cycle [32m200[0m 7.208 ms - 3552[0m
[0mGET /api/modifiers/table/1 [32m200[0m 1.401 ms - 422[0m
[0mGET /api/modifiers/table/3 [32m200[0m 1.499 ms - 758[0m
[0mGET /api/modifiers/table/2 [32m200[0m 1.796 ms - 454[0m
[0mGET /api/modifiers/table/1 [36m304[0m 1.137 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 1.084 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 1.360 ms - -[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 64.60000002
Food production calculated: 64.60000002
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
calculateNewReserves inputs: {
  currentFoodReserves: 778.4,
  foodProduction: 64.60000002,
  foodConsumption: 10
}
Perishable modifiers: {
  modifiers: [
    {
      id: 1,
      table_id: 3,
      title: 'Base',
      effect: 0.2,
      description: 'Péremption naturelle',
      start_date: null,
      created_at: '2025-04-23 22:53:21',
      updated_at: '2025-04-23 22:53:21'
    },
    {
      id: 6,
      table_id: 3,
      title: 'dingo',
      effect: 0.1,
      description: '',
      start_date: null,
      created_at: '2025-04-24 21:34:38',
      updated_at: '2025-04-24 22:01:57'
    },
    {
      id: 8,
      table_id: 3,
      title: 'Test',
      effect: -0.25,
      description: '',
      start_date: null,
      created_at: '2025-04-24 22:08:35',
      updated_at: '2025-04-24 22:08:35'
    }
  ],
  tableValue: 0.05000000000000002,
  calculatedSum: 0.050000000000000044
}
Food reserves calculation: {
  currentReserves: 778.4,
  perishableRate: 0.05000000000000002,
  foodLostToPerishable: 38.92000000000001,
  remainingAfterPerishable: 739.48,
  production: 64.60000002,
  consumption: 10,
  netProductionConsumption: 54.600000019999996,
  newReservesBeforeMax: 794.1,
  newReserves: 794.1
}
Checking newReserves: {
  probabilities: {
    sickness: 0.0004582347356232009,
    crime: 0.10664242539375059,
    research: 0.01
  },
  events: [
    {
      type: 'CRIME',
      description: "Un crime a été commis! Les protecteurs enquêtent sur l'affaire."
    }
  ],
  production: {
    foodProduction: 64.60000002,
    miningProduction: 0,
    materialsProduction: 0,
    tradingRevenue: 0,
    engineeringMultiplier: 0
  },
  consumption: {
    salaries: 280,
    charges: 0,
    foodConsumption: 10,
    craftsmanEffect: 1
  },
  newReserves: {
    newReserves: { food: 794.1, treasure: 0, materials: 0 },
    totalMineProduction: 70.57785180445435,
    mineDepth: 0,
    perishableRate: 0.05000000000000002,
    hasFamine: false
  }
}
Using calculated food reserves: 794.1
Updating food reserves to: 794.1
Verified food reserves after update: 794.1
[0mPOST /api/game/cycle [32m200[0m 9.754 ms - 3223[0m
[0mGET /api/modifiers/table/1 [32m200[0m 0.861 ms - 422[0m
[0mGET /api/modifiers/table/2 [32m200[0m 0.810 ms - 454[0m
[0mGET /api/modifiers/table/3 [32m200[0m 0.984 ms - 758[0m
[0mGET /api/modifiers/table/1 [36m304[0m 0.633 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 0.666 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 0.831 ms - -[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 0.5,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 32.30000001
Food production calculated: 32.30000001
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
calculateNewReserves inputs: {
  currentFoodReserves: 794.1,
  foodProduction: 32.30000001,
  foodConsumption: 10
}
Perishable modifiers: {
  modifiers: [
    {
      id: 1,
      table_id: 3,
      title: 'Base',
      effect: 0.2,
      description: 'Péremption naturelle',
      start_date: null,
      created_at: '2025-04-23 22:53:21',
      updated_at: '2025-04-23 22:53:21'
    },
    {
      id: 6,
      table_id: 3,
      title: 'dingo',
      effect: 0.1,
      description: '',
      start_date: null,
      created_at: '2025-04-24 21:34:38',
      updated_at: '2025-04-24 22:01:57'
    },
    {
      id: 8,
      table_id: 3,
      title: 'Test',
      effect: -0.25,
      description: '',
      start_date: null,
      created_at: '2025-04-24 22:08:35',
      updated_at: '2025-04-24 22:08:35'
    }
  ],
  tableValue: 0.05000000000000002,
  calculatedSum: 0.050000000000000044
}
Food reserves calculation: {
  currentReserves: 794.1,
  perishableRate: 0.05000000000000002,
  foodLostToPerishable: 39.70500000000001,
  remainingAfterPerishable: 754.395,
  production: 32.30000001,
  consumption: 10,
  netProductionConsumption: 22.300000009999998,
  newReservesBeforeMax: 776.7,
  newReserves: 776.7
}
Checking newReserves: {
  probabilities: {
    sickness: 0.0004582347356232009,
    crime: 0.10664242539375059,
    research: 0.01
  },
  events: [],
  production: {
    foodProduction: 32.30000001,
    miningProduction: 0,
    materialsProduction: 0,
    tradingRevenue: 0,
    engineeringMultiplier: 0
  },
  consumption: {
    salaries: 280,
    charges: 0,
    foodConsumption: 10,
    craftsmanEffect: 1
  },
  newReserves: {
    newReserves: { food: 776.7, treasure: 0, materials: 0 },
    totalMineProduction: 70.57785180445435,
    mineDepth: 0,
    perishableRate: 0.05000000000000002,
    hasFamine: false
  }
}
Using calculated food reserves: 776.7
Updating food reserves to: 776.7
Verified food reserves after update: 776.7
[0mPOST /api/game/cycle [32m200[0m 8.628 ms - 3252[0m
[0mGET /api/modifiers/table/1 [32m200[0m 0.987 ms - 422[0m
[0mGET /api/modifiers/table/3 [32m200[0m 1.038 ms - 758[0m
[0mGET /api/modifiers/table/2 [32m200[0m 1.288 ms - 454[0m
[0mGET /api/modifiers/table/1 [36m304[0m 0.857 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 0.956 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 1.068 ms - -[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 0.5,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 32.30000001
Food production calculated: 32.30000001
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
calculateNewReserves inputs: {
  currentFoodReserves: 776.7,
  foodProduction: 32.30000001,
  foodConsumption: 10
}
Perishable modifiers: {
  modifiers: [
    {
      id: 1,
      table_id: 3,
      title: 'Base',
      effect: 0.2,
      description: 'Péremption naturelle',
      start_date: null,
      created_at: '2025-04-23 22:53:21',
      updated_at: '2025-04-23 22:53:21'
    },
    {
      id: 6,
      table_id: 3,
      title: 'dingo',
      effect: 0.1,
      description: '',
      start_date: null,
      created_at: '2025-04-24 21:34:38',
      updated_at: '2025-04-24 22:01:57'
    },
    {
      id: 8,
      table_id: 3,
      title: 'Test',
      effect: -0.25,
      description: '',
      start_date: null,
      created_at: '2025-04-24 22:08:35',
      updated_at: '2025-04-24 22:08:35'
    }
  ],
  tableValue: 0.05000000000000002,
  calculatedSum: 0.050000000000000044
}
Food reserves calculation: {
  currentReserves: 776.7,
  perishableRate: 0.05000000000000002,
  foodLostToPerishable: 38.835000000000015,
  remainingAfterPerishable: 737.865,
  production: 32.30000001,
  consumption: 10,
  netProductionConsumption: 22.300000009999998,
  newReservesBeforeMax: 760.2,
  newReserves: 760.2
}
Checking newReserves: {
  probabilities: {
    sickness: 0.0004582347356232009,
    crime: 0.10664242539375059,
    research: 0.01
  },
  events: [],
  production: {
    foodProduction: 32.30000001,
    miningProduction: 0,
    materialsProduction: 0,
    tradingRevenue: 0,
    engineeringMultiplier: 0
  },
  consumption: {
    salaries: 280,
    charges: 0,
    foodConsumption: 10,
    craftsmanEffect: 1
  },
  newReserves: {
    newReserves: { food: 760.2, treasure: 0, materials: 0 },
    totalMineProduction: 70.57785180445435,
    mineDepth: 0,
    perishableRate: 0.05000000000000002,
    hasFamine: false
  }
}
Using calculated food reserves: 760.2
Updating food reserves to: 760.2
Verified food reserves after update: 760.2
[0mPOST /api/game/cycle [32m200[0m 8.647 ms - 3245[0m
[0mGET /api/modifiers/table/2 [32m200[0m 0.825 ms - 454[0m
[0mGET /api/modifiers/table/1 [32m200[0m 1.286 ms - 422[0m
[0mGET /api/modifiers/table/3 [32m200[0m 1.250 ms - 758[0m
[0mGET /api/modifiers/table/1 [36m304[0m 0.709 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 1.155 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 0.925 ms - -[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 0.5,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 32.30000001
Food production calculated: 32.30000001
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
calculateNewReserves inputs: {
  currentFoodReserves: 760.2,
  foodProduction: 32.30000001,
  foodConsumption: 10
}
Perishable modifiers: {
  modifiers: [
    {
      id: 1,
      table_id: 3,
      title: 'Base',
      effect: 0.2,
      description: 'Péremption naturelle',
      start_date: null,
      created_at: '2025-04-23 22:53:21',
      updated_at: '2025-04-23 22:53:21'
    },
    {
      id: 6,
      table_id: 3,
      title: 'dingo',
      effect: 0.1,
      description: '',
      start_date: null,
      created_at: '2025-04-24 21:34:38',
      updated_at: '2025-04-24 22:01:57'
    },
    {
      id: 8,
      table_id: 3,
      title: 'Test',
      effect: -0.25,
      description: '',
      start_date: null,
      created_at: '2025-04-24 22:08:35',
      updated_at: '2025-04-24 22:08:35'
    }
  ],
  tableValue: 0.05000000000000002,
  calculatedSum: 0.050000000000000044
}
Food reserves calculation: {
  currentReserves: 760.2,
  perishableRate: 0.05000000000000002,
  foodLostToPerishable: 38.01000000000001,
  remainingAfterPerishable: 722.19,
  production: 32.30000001,
  consumption: 10,
  netProductionConsumption: 22.300000009999998,
  newReservesBeforeMax: 744.5,
  newReserves: 744.5
}
Checking newReserves: {
  probabilities: {
    sickness: 0.0004582347356232009,
    crime: 0.10664242539375059,
    research: 0.01
  },
  events: [],
  production: {
    foodProduction: 32.30000001,
    miningProduction: 0,
    materialsProduction: 0,
    tradingRevenue: 0,
    engineeringMultiplier: 0
  },
  consumption: {
    salaries: 280,
    charges: 0,
    foodConsumption: 10,
    craftsmanEffect: 1
  },
  newReserves: {
    newReserves: { food: 744.5, treasure: 0, materials: 0 },
    totalMineProduction: 70.57785180445435,
    mineDepth: 0,
    perishableRate: 0.05000000000000002,
    hasFamine: false
  }
}
Using calculated food reserves: 744.5
Updating food reserves to: 744.5
Verified food reserves after update: 744.5
[0mPOST /api/game/cycle [32m200[0m 8.139 ms - 3341[0m
[0mGET /api/modifiers/table/1 [32m200[0m 1.692 ms - 422[0m
[0mGET /api/modifiers/table/3 [32m200[0m 1.555 ms - 758[0m
[0mGET /api/modifiers/table/2 [32m200[0m 1.131 ms - 454[0m
[0mGET /api/modifiers/table/1 [36m304[0m 0.807 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 0.780 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 0.883 ms - -[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 64.60000002
Food production calculated: 64.60000002
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
calculateNewReserves inputs: {
  currentFoodReserves: 744.5,
  foodProduction: 64.60000002,
  foodConsumption: 10
}
Perishable modifiers: {
  modifiers: [
    {
      id: 1,
      table_id: 3,
      title: 'Base',
      effect: 0.2,
      description: 'Péremption naturelle',
      start_date: null,
      created_at: '2025-04-23 22:53:21',
      updated_at: '2025-04-23 22:53:21'
    },
    {
      id: 6,
      table_id: 3,
      title: 'dingo',
      effect: 0.1,
      description: '',
      start_date: null,
      created_at: '2025-04-24 21:34:38',
      updated_at: '2025-04-24 22:01:57'
    },
    {
      id: 8,
      table_id: 3,
      title: 'Test',
      effect: -0.25,
      description: '',
      start_date: null,
      created_at: '2025-04-24 22:08:35',
      updated_at: '2025-04-24 22:08:35'
    }
  ],
  tableValue: 0.05000000000000002,
  calculatedSum: 0.050000000000000044
}
Food reserves calculation: {
  currentReserves: 744.5,
  perishableRate: 0.05000000000000002,
  foodLostToPerishable: 37.225000000000016,
  remainingAfterPerishable: 707.275,
  production: 64.60000002,
  consumption: 10,
  netProductionConsumption: 54.600000019999996,
  newReservesBeforeMax: 761.9,
  newReserves: 761.9
}
Checking newReserves: {
  probabilities: {
    sickness: 0.0004582347356232009,
    crime: 0.10664242539375059,
    research: 0.01
  },
  events: [],
  production: {
    foodProduction: 64.60000002,
    miningProduction: 0,
    materialsProduction: 0,
    tradingRevenue: 0,
    engineeringMultiplier: 0
  },
  consumption: {
    salaries: 280,
    charges: 0,
    foodConsumption: 10,
    craftsmanEffect: 1
  },
  newReserves: {
    newReserves: { food: 761.9, treasure: 0, materials: 0 },
    totalMineProduction: 70.57785180445435,
    mineDepth: 0,
    perishableRate: 0.05000000000000002,
    hasFamine: false
  }
}
Using calculated food reserves: 761.9
Updating food reserves to: 761.9
Verified food reserves after update: 761.9
[0mPOST /api/game/cycle [32m200[0m 9.993 ms - 3308[0m
[0mGET /api/modifiers/table/1 [32m200[0m 0.745 ms - 422[0m
[0mGET /api/modifiers/table/2 [32m200[0m 0.693 ms - 454[0m
[0mGET /api/modifiers/table/3 [32m200[0m 1.190 ms - 758[0m
[0mGET /api/modifiers/table/2 [36m304[0m 0.659 ms - -[0m
[0mGET /api/modifiers/table/1 [36m304[0m 0.876 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 0.851 ms - -[0m
[0mGET /api/modifiers/moral [33m404[0m 0.617 ms - 158[0m
[0mGET /api/modifiers/moral [33m404[0m 0.135 ms - 158[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 64.60000002
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
[0mGET /api/game/state [32m200[0m 4.137 ms - 7461[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 64.60000002
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
[0mGET /api/game/state [36m304[0m 1.829 ms - -[0m
[0mGET /api/game/events [32m200[0m 0.342 ms - 1627[0m
[0mGET /api/game/events [36m304[0m 0.201 ms - -[0m
[0mGET /api/modifiers/moral [33m404[0m 0.193 ms - 158[0m
[0mGET /api/modifiers/moral [33m404[0m 0.514 ms - 158[0m
[0mGET /api/modifiers/table/2 [32m200[0m 1.671 ms - 454[0m
[0mGET /api/modifiers/table/3 [32m200[0m 1.704 ms - 758[0m
[0mGET /api/modifiers/table/1 [32m200[0m 1.775 ms - 422[0m
[0mGET /api/modifiers/table/3 [36m304[0m 0.522 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 0.822 ms - -[0m
[0mGET /api/modifiers/table/1 [36m304[0m 0.742 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 0.628 ms - -[0m
[0mGET /api/modifiers/table/1 [36m304[0m 1.224 ms - -[0m
[0mGET /api/modifiers/moral [33m404[0m 0.360 ms - 158[0m
[0mGET /api/modifiers/table/3 [36m304[0m 0.900 ms - -[0m
[0mGET /api/modifiers/moral [33m404[0m 0.221 ms - 158[0m
[0mGET /api/modifiers/moral [33m404[0m 0.149 ms - 158[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 64.60000002
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
[0mGET /api/game/state [32m200[0m 5.020 ms - 7461[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 64.60000002
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
[0mGET /api/game/state [36m304[0m 2.425 ms - -[0m
[0mGET /api/game/events [36m304[0m 0.305 ms - -[0m
[0mGET /api/game/events [36m304[0m 0.259 ms - -[0m
[0mGET /api/modifiers/table/1 [32m200[0m 2.387 ms - 422[0m
[0mGET /api/modifiers/table/2 [32m200[0m 2.819 ms - 454[0m
[0mGET /api/modifiers/table/3 [32m200[0m 2.572 ms - 758[0m
[0mGET /api/modifiers/table/1 [32m200[0m 0.731 ms - 422[0m
[0mGET /api/modifiers/table/3 [32m200[0m 0.627 ms - 758[0m
[0mGET /api/modifiers/table/2 [32m200[0m 1.295 ms - 454[0m
[0mGET /api/modifiers/table/1 [36m304[0m 1.476 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 0.990 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 1.299 ms - -[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 64.60000002
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
[0mGET /api/game/state [32m200[0m 6.829 ms - 7461[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 64.60000002
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
[0mGET /api/game/state [36m304[0m 1.983 ms - -[0m
[0mGET /api/game/events [36m304[0m 0.264 ms - -[0m
[0mGET /api/game/events [36m304[0m 0.222 ms - -[0m
[0mGET /api/modifiers/table/3 [32m200[0m 0.982 ms - 758[0m
[0mGET /api/modifiers/table/1 [32m200[0m 1.854 ms - 422[0m
[0mGET /api/modifiers/table/2 [32m200[0m 1.898 ms - 454[0m
[0mGET /api/modifiers/table/2 [36m304[0m 0.580 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 0.963 ms - -[0m
[0mGET /api/modifiers/table/1 [36m304[0m 1.097 ms - -[0m
[0mGET /api/modifiers/table/1 [36m304[0m 1.669 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 1.682 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 1.680 ms - -[0m
[0mGET /health [32m200[0m 0.130 ms - 15[0m
[0mGET /api/modifiers/table/10 [32m200[0m 0.316 ms - 218[0m
[0mGET /api/modifiers/moral [33m404[0m 0.134 ms - 158[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 64.60000002
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
[0mGET /api/game/state [32m200[0m 4.676 ms - 7464[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 64.60000002
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
[0mGET /api/game/state [36m304[0m 1.973 ms - -[0m
[0mGET /api/game/events [36m304[0m 0.408 ms - -[0m
[0mGET /api/game/events [36m304[0m 0.256 ms - -[0m
[0mGET /api/modifiers/table/2 [32m200[0m 0.950 ms - 454[0m
[0mGET /api/modifiers/table/3 [32m200[0m 0.977 ms - 758[0m
[0mGET /api/modifiers/table/1 [32m200[0m 1.323 ms - 422[0m
[0mGET /api/modifiers/table/2 [36m304[0m 0.855 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 0.876 ms - -[0m
[0mGET /api/modifiers/table/1 [36m304[0m 0.911 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 1.090 ms - -[0m
[0mGET /api/modifiers/table/1 [36m304[0m 1.432 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 1.967 ms - -[0m
[0mGET /api/modifiers/moral-stats [33m404[0m 0.313 ms - 164[0m
[0mGET /api/moral-stats [33m404[0m 0.207 ms - 154[0m
[0mGET /api/test-route [33m404[0m 0.113 ms - 153[0m
[0mGET /health [32m200[0m 0.090 ms - 15[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 64.60000002
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
[0mGET /api/game/state [32m200[0m 3.283 ms - 7464[0m
[0mGET /api/modifiers/table/10 [32m200[0m 0.317 ms - 224[0m
[0mGET /api/modifiers/food [32m200[0m 2.161 ms - 1621[0m
[0mGET /api/moral/stats [33m404[0m 0.113 ms - 154[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 64.60000002
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
[0mGET /api/game/state [32m200[0m 4.165 ms - 7464[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 64.60000002
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
[0mGET /api/game/state [36m304[0m 1.639 ms - -[0m
[0mGET /api/game/events [36m304[0m 0.363 ms - -[0m
[0mGET /api/game/events [36m304[0m 0.242 ms - -[0m
[0mGET /api/modifiers/table/1 [32m200[0m 0.705 ms - 422[0m
[0mGET /api/modifiers/table/2 [32m200[0m 1.311 ms - 454[0m
[0mGET /api/modifiers/table/3 [32m200[0m 1.034 ms - 758[0m
[0mGET /api/modifiers/table/1 [36m304[0m 0.724 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 0.794 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 0.988 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 0.897 ms - -[0m
[0mGET /api/modifiers/table/1 [36m304[0m 1.329 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 1.310 ms - -[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 64.60000002
Food production calculated: 64.60000002
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
calculateNewReserves inputs: {
  currentFoodReserves: 761.9,
  foodProduction: 64.60000002,
  foodConsumption: 10
}
Perishable modifiers: {
  modifiers: [
    {
      id: 1,
      table_id: 3,
      title: 'Base',
      effect: 0.2,
      description: 'Péremption naturelle',
      start_date: null,
      created_at: '2025-04-23 22:53:21',
      updated_at: '2025-04-23 22:53:21'
    },
    {
      id: 6,
      table_id: 3,
      title: 'dingo',
      effect: 0.1,
      description: '',
      start_date: null,
      created_at: '2025-04-24 21:34:38',
      updated_at: '2025-04-24 22:01:57'
    },
    {
      id: 8,
      table_id: 3,
      title: 'Test',
      effect: -0.25,
      description: '',
      start_date: null,
      created_at: '2025-04-24 22:08:35',
      updated_at: '2025-04-24 22:08:35'
    }
  ],
  tableValue: 0.05000000000000002,
  calculatedSum: 0.050000000000000044
}
Food reserves calculation: {
  currentReserves: 761.9,
  perishableRate: 0.05000000000000002,
  foodLostToPerishable: 38.09500000000001,
  remainingAfterPerishable: 723.805,
  production: 64.60000002,
  consumption: 10,
  netProductionConsumption: 54.600000019999996,
  newReservesBeforeMax: 778.4,
  newReserves: 778.4
}
Checking newReserves: {
  probabilities: {
    sickness: 0.0004582347356232009,
    crime: 0.10664242539375059,
    research: 0.01
  },
  events: [],
  production: {
    foodProduction: 64.60000002,
    miningProduction: 0,
    materialsProduction: 0,
    tradingRevenue: 0,
    engineeringMultiplier: 0
  },
  consumption: {
    salaries: 280,
    charges: 0,
    foodConsumption: 10,
    craftsmanEffect: 1
  },
  newReserves: {
    newReserves: { food: 778.4, treasure: 0, materials: 0 },
    totalMineProduction: 70.57785180445435,
    mineDepth: 0,
    perishableRate: 0.05000000000000002,
    hasFamine: false
  }
}
Using calculated food reserves: 778.4
Updating food reserves to: 778.4
Verified food reserves after update: 778.4
[0mPOST /api/game/cycle [32m200[0m 9.704 ms - 3296[0m
[0mGET /api/modifiers/table/1 [32m200[0m 1.583 ms - 422[0m
[0mGET /api/modifiers/table/2 [32m200[0m 1.658 ms - 454[0m
[0mGET /api/modifiers/table/3 [32m200[0m 1.749 ms - 758[0m
[0mGET /api/modifiers/table/2 [36m304[0m 1.551 ms - -[0m
[0mGET /api/modifiers/table/1 [36m304[0m 1.473 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 1.398 ms - -[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 64.60000002
Food production calculated: 64.60000002
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
calculateNewReserves inputs: {
  currentFoodReserves: 778.4,
  foodProduction: 64.60000002,
  foodConsumption: 10
}
Perishable modifiers: {
  modifiers: [
    {
      id: 1,
      table_id: 3,
      title: 'Base',
      effect: 0.2,
      description: 'Péremption naturelle',
      start_date: null,
      created_at: '2025-04-23 22:53:21',
      updated_at: '2025-04-23 22:53:21'
    },
    {
      id: 6,
      table_id: 3,
      title: 'dingo',
      effect: 0.1,
      description: '',
      start_date: null,
      created_at: '2025-04-24 21:34:38',
      updated_at: '2025-04-24 22:01:57'
    },
    {
      id: 8,
      table_id: 3,
      title: 'Test',
      effect: -0.25,
      description: '',
      start_date: null,
      created_at: '2025-04-24 22:08:35',
      updated_at: '2025-04-24 22:08:35'
    }
  ],
  tableValue: 0.05000000000000002,
  calculatedSum: 0.050000000000000044
}
Food reserves calculation: {
  currentReserves: 778.4,
  perishableRate: 0.05000000000000002,
  foodLostToPerishable: 38.92000000000001,
  remainingAfterPerishable: 739.48,
  production: 64.60000002,
  consumption: 10,
  netProductionConsumption: 54.600000019999996,
  newReservesBeforeMax: 794.1,
  newReserves: 794.1
}
Checking newReserves: {
  probabilities: {
    sickness: 0.0004582347356232009,
    crime: 0.10664242539375059,
    research: 0.01
  },
  events: [],
  production: {
    foodProduction: 64.60000002,
    miningProduction: 0,
    materialsProduction: 0,
    tradingRevenue: 0,
    engineeringMultiplier: 0
  },
  consumption: {
    salaries: 280,
    charges: 0,
    foodConsumption: 10,
    craftsmanEffect: 1
  },
  newReserves: {
    newReserves: { food: 794.1, treasure: 0, materials: 0 },
    totalMineProduction: 70.57785180445435,
    mineDepth: 0,
    perishableRate: 0.05000000000000002,
    hasFamine: false
  }
}
Using calculated food reserves: 794.1
Updating food reserves to: 794.1
Verified food reserves after update: 794.1
[0mPOST /api/game/cycle [32m200[0m 7.187 ms - 3294[0m
[0mGET /api/modifiers/table/1 [32m200[0m 2.337 ms - 422[0m
[0mGET /api/modifiers/table/3 [32m200[0m 2.080 ms - 758[0m
[0mGET /api/modifiers/table/2 [32m200[0m 2.573 ms - 454[0m
[0mGET /api/modifiers/table/3 [36m304[0m 1.512 ms - -[0m
[0mGET /api/modifiers/table/1 [36m304[0m 2.022 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 1.599 ms - -[0m
[0mGET /api/modifiers/test-moral [33m404[0m 0.154 ms - 163[0m
[0mGET /api/modifiers/test [32m200[0m 0.155 ms - 31[0m
[0mGET /api/modifiers/happiness [33m404[0m 0.132 ms - 162[0m
[0mGET /api/direct-test [33m404[0m 0.145 ms - 154[0m
[0mGET /api/moral-direct [33m404[0m 0.131 ms - 155[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1.25,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 80.750000025
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
[0mGET /api/game/state [32m200[0m 5.615 ms - 7445[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1.25,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 80.750000025
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
[0mGET /api/game/state [36m304[0m 1.816 ms - -[0m
[0mGET /api/game/events [36m304[0m 0.409 ms - -[0m
[0mGET /api/game/events [36m304[0m 0.263 ms - -[0m
[0mGET /api/modifiers/table/1 [32m200[0m 1.059 ms - 422[0m
[0mGET /api/modifiers/table/3 [32m200[0m 1.301 ms - 758[0m
[0mGET /api/modifiers/table/2 [32m200[0m 1.281 ms - 454[0m
[0mGET /api/modifiers/table/3 [36m304[0m 0.914 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 0.927 ms - -[0m
[0mGET /api/modifiers/table/1 [36m304[0m 1.491 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 0.691 ms - -[0m
[0mGET /api/modifiers/table/1 [36m304[0m 1.667 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 1.172 ms - -[0m
[0mGET /api/modifiers/moral [33m404[0m 0.223 ms - 158[0m
[0mGET /api/modifiers/moral [33m404[0m 0.119 ms - 158[0m
[0mGET /api/modifiers/food [32m200[0m 2.380 ms - 1621[0m
[0mGET /api/modifiers/test-simple [33m404[0m 0.126 ms - 164[0m
[0mGET /api/modifiers/test [32m200[0m 0.106 ms - 31[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1.25,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 80.750000025
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
[0mGET /api/game/state [32m200[0m 6.334 ms - 7445[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1.25,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 80.750000025
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
[0mGET /api/game/state [36m304[0m 2.243 ms - -[0m
[0mGET /api/game/events [36m304[0m 0.501 ms - -[0m
[0mGET /api/game/events [36m304[0m 0.347 ms - -[0m
[0mGET /api/modifiers/table/2 [32m200[0m 1.447 ms - 454[0m
[0mGET /api/modifiers/table/1 [32m200[0m 2.209 ms - 422[0m
[0mGET /api/modifiers/table/3 [32m200[0m 1.939 ms - 758[0m
[0mGET /api/modifiers/table/2 [36m304[0m 1.273 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 1.376 ms - -[0m
[0mGET /api/modifiers/table/1 [36m304[0m 1.591 ms - -[0m
[0mGET /api/modifiers/table/1 [36m304[0m 1.780 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 1.750 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 1.744 ms - -[0m
[0mGET /api/moral-direct [33m404[0m 0.105 ms - 155[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1.25,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 80.750000025
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
[0mGET /api/game/state [32m200[0m 4.834 ms - 7445[0m
Calculating moral modifier with moralValue: 1000
Normal distribution result: 0.**********
Final moral modifier: 1.000000082740371e-9
Food production inputs: {
  activeFarmers: 5,
  foodPerFarmer: 4,
  seasonFactor: 1.25,
  foodGeneralModifier: 2,
  foodTechModifier: 0.23,
  moralModifier: 1.000000082740371e-9
}
Food production calculated: 80.750000025
Food consumption calculation: { totalInhabitants: 10, consumption: 10 }
[0mGET /api/game/state [36m304[0m 2.605 ms - -[0m
[0mGET /api/game/events [36m304[0m 0.326 ms - -[0m
[0mGET /api/game/events [36m304[0m 0.227 ms - -[0m
[0mGET /api/modifiers/table/1 [32m200[0m 0.730 ms - 422[0m
[0mGET /api/modifiers/table/2 [32m200[0m 0.653 ms - 454[0m
[0mGET /api/modifiers/table/3 [32m200[0m 0.740 ms - 758[0m
[0mGET /api/modifiers/table/2 [36m304[0m 0.577 ms - -[0m
[0mGET /api/modifiers/table/1 [36m304[0m 1.111 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 1.510 ms - -[0m
[0mGET /api/modifiers/table/1 [36m304[0m 1.596 ms - -[0m
[0mGET /api/modifiers/table/2 [36m304[0m 1.158 ms - -[0m
[0mGET /api/modifiers/table/3 [36m304[0m 0.984 ms - -[0m
