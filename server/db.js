const sqlite3 = require('sqlite3').verbose();
const dbConfig = require('./config/database');

// Open the database
const db = new sqlite3.Database(dbConfig.dbPath, (err) => {
  if (err) {
    console.error(`Error opening database ${dbConfig.dbName}:`, err.message);
  } else {
    console.log(`Connected to the SQLite database: ${dbConfig.dbName}`);
  }
});

// Promisify database methods
const run = (sql, params = []) => {
  return new Promise((resolve, reject) => {
    db.run(sql, params, function (err) {
      if (err) {
        console.error('Error running SQL:', sql);
        console.error('Error details:', err);
        reject(err);
      } else {
        resolve({ lastID: this.lastID, changes: this.changes });
      }
    });
  });
};

const get = (sql, params = []) => {
  return new Promise((resolve, reject) => {
    db.get(sql, params, (err, row) => {
      if (err) {
        console.error('Error running SQL:', sql);
        console.error('Error details:', err);
        reject(err);
      } else {
        resolve(row);
      }
    });
  });
};

const all = (sql, params = []) => {
  return new Promise((resolve, reject) => {
    db.all(sql, params, (err, rows) => {
      if (err) {
        console.error('Error running SQL:', sql);
        console.error('Error details:', err);
        reject(err);
      } else {
        resolve(rows);
      }
    });
  });
};

// Close the database connection when the process is terminated
process.on('SIGINT', () => {
  db.close((err) => {
    if (err) {
      console.error('Error closing database:', err.message);
    } else {
      console.log('Database connection closed.');
    }
    process.exit(0);
  });
});

module.exports = {
  run,
  get,
  all
};
