// Ce fichier est exécuté avant chaque test
// Il peut être utilisé pour configurer l'environnement de test

// Augmenter le timeout pour les tests
jest.setTimeout(10000);

// Supprimer les logs pendant les tests
global.console = {
  ...console,
  // Uncomment to ignore specific console methods during tests
  // log: jest.fn(),
  // error: jest.fn(),
  // warn: jest.fn(),
};

// Mock de la base de données
jest.mock('./db', () => ({
  get: jest.fn(),
  all: jest.fn(),
  run: jest.fn()
}));
