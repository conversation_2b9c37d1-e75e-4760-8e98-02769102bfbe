/**
 * Utility functions for buildings
 */

/**
 * Calculate the cost of a building based on its plan and level
 * @param {Object} plan - Building plan
 * @param {number} level - Building level
 * @returns {Object} - Building cost
 */
const calculateBuildingCost = (plan, level = 1) => {
  if (!plan) {
    return {
      materials: 0,
      food: 0,
      gold: 0,
      maintenance: 0
    };
  }

  // Base costs from the plan
  const baseMaterialsCost = plan.materials_cost || 0;
  const baseFoodCost = plan.food_cost || 0;
  const baseGoldCost = plan.gold_cost || 0;
  const baseMaintenanceCost = plan.maintenance_cost || 0;

  // Cost scaling formula: base_cost * (1 + 0.5 * (level - 1))
  const levelMultiplier = 1 + 0.5 * (level - 1);

  return {
    materials: Math.round(baseMaterialsCost * levelMultiplier),
    food: Math.round(baseFoodCost * levelMultiplier),
    gold: Math.round(baseGoldCost * levelMultiplier),
    maintenance: Math.round(baseMaintenanceCost * levelMultiplier)
  };
};

module.exports = {
  calculateBuildingCost
};
