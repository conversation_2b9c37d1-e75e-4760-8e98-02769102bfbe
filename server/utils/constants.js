// Constantes pour l'application

// Mo<PERSON> de l'année
const MONTHS = [
  {
    mois: "Rubis",
    description: "<PERSON><PERSON><PERSON> (janvier) - Le rubis, pierre précieuse rouge vif, symbolise la passion, la force et la vitalité. En janvier, au cœur de l'hiver, on célèbre ces qualités en se préparant pour une nouvelle année pleine de défis."
  },
  {
    mois: "Saphir",
    description: "Sa<PERSON><PERSON> (février) - <PERSON>r, pierre précieuse bleue, est associé à la sagesse, à la clarté d'esprit et à la protection. En février, alors que l'hiver se poursuit, on se concentre sur la réflexion et la planification stratégique pour les mois à venir."
  },
  {
    mois: "Émeraude",
    description: "É<PERSON>aude (mars) - L'émeraude, pierre précieuse verte, représente la croissance, la fertilité et la vitalité. En mars, au début du printemps, on célèbre le renouveau de la nature et on se prépare à semer les graines des projets futurs."
  },
  {
    mois: "Diamant",
    description: "Diaman<PERSON> (avril) - <PERSON>, pierre précieuse transparente et étincelante, symbolise la pureté, la résilience et l'invincibilité. En avril, alors que le printemps s'installe, on célèbre sa force intérieure et sa capacité à surmonter les défis avec grâce et détermination. C'est aussi le mois durant lequel on déterre la hache de guerre."
  },
  {
    mois: "Améthyste",
    description: "Améthyste (mai) - L'améthyste, pierre précieuse violet profond, est associée à la spiritualité, à la paix intérieure et à la sagesse. En mai, à la fin du printemps, on se tourne vers l'introspection et la méditation, on cherche à se connecter avec son moi intérieur et à trouver l'harmonie."
  },
  {
    mois: "Topaze",
    description: "Topaze (juin) - La topaze, pierre précieuse jaune ou orange, symbolise la créativité, la passion et l'inspiration. En juin, au début de l'été, on met l'accent sur l'expression artistique et l'innovation, on cherche à donner vie à de nouvelles idées et à explorer de nouveaux horizons."
  },
  {
    mois: "Sardoine",
    description: "Sardoine (juillet) - La sardoine, pierre précieuse rouge ou brun rougeâtre, représente la force physique, le courage et la détermination. En juillet, au cœur de l'été, on se concentre sur l'entraînement et la préparation physique. C'est aussi une période de récoltes importantes."
  },
  {
    mois: "Perle",
    description: "Perle (août) - La perle, une gemme formée dans les coquillages, symbolise la pureté, la beauté et la perfection. En août, alors que l'été bat son plein, on célèbre la beauté de la pierre et la perfection de l'univers, se reconnectant avec les éléments qui nous entourent."
  },
  {
    mois: "Opale",
    description: "Opale (septembre) - L'opale, pierre précieuse aux reflets multicolores, est associée aux arcanes, au mystère et à la transformation. En septembre, au début de l'automne, on explore les mystères de l'univers et on cherche à comprendre les secrets cachés derrière les voiles de la réalité."
  },
  {
    mois: "Aventurine",
    description: "Aventurine (octobre) - L'aventurine, pierre précieuse verte avec des inclusions scintillantes, symbolise la chance, la prospérité et l'abondance. En octobre, au milieu de l'automne, on célèbre la chance et la fortune, se lançant dans de nouvelles aventures et cherchant à atteindre de nouvelles profondeurs."
  },
  {
    mois: "Grenat",
    description: "Grenat (novembre) - Le grenat, pierre précieuse rouge foncé à rouge orangé, représente la passion, l'énergie et la détermination. En novembre, à la fin de l'automne, on se concentre sur nos objectifs avec une énergie renouvelée, travaillant sans relâche pour atteindre nos rêves les plus chers."
  },
  {
    mois: "Tanzanite",
    description: "Tanzanite (décembre) - La tanzanite, pierre précieuse bleu-violet rare, symbolise la spiritualité, la paix intérieure et la connexion avec le divin. En décembre, au début de l'hiver, on se réunit pour célébrer notre spiritualité et notre connexion avec les forces supérieures, se préparant à accueillir une nouvelle année pleine de promesses et de possibilités. On en profite pour faire le point avec le reste du clan. Idéalement, on remet en questions nos rancunes et on enterre la hache de guerre, du moins jusqu'au mois de Diamant."
  }
];

// Saisons
const SEASONS = {
  WINTER: {
    name: "Hiver",
    factor: 0.5,
    months: [11, 0, 1] // Indices des mois d'hiver (Tanzanite, Rubis, Saphir)
  },
  SPRING: {
    name: "Printemps",
    factor: 1.0,
    months: [2, 3, 4] // Indices des mois de printemps (Émeraude, Diamant, Améthyste)
  },
  SUMMER: {
    name: "Été",
    factor: 1.25,
    months: [5, 6, 7] // Indices des mois d'été (Topaze, Sardoine, Perle)
  },
  AUTUMN: {
    name: "Automne",
    factor: 1.0,
    months: [8, 9, 10] // Indices des mois d'automne (Opale, Aventurine, Grenat)
  }
};

// Titres de moral
const MORAL_TITLES = [
  { min: 0.0, max: 0.2, title: "Rébellion" },
  { min: 0.2, max: 0.4, title: "En colère" },
  { min: 0.4, max: 0.65, title: "Triste" },
  { min: 0.65, max: 0.9, title: "Mécontent" },
  { min: 0.9, max: 1.1, title: "Neutre" },
  { min: 1.1, max: 1.35, title: "Content" },
  { min: 1.35, max: 1.6, title: "Epanoui" },
  { min: 1.6, max: 1.8, title: "Heureux" },
  { min: 1.8, max: 2.0, title: "Ecstatique" }
];

// Jobs
const JOBS = [
  { name: 'Scholar', salary: 60 },
  { name: 'Healer', salary: 40 },
  { name: 'Worker', salary: 20 },
  { name: 'Miner', salary: 20 },
  { name: 'Protector', salary: 50 },
  { name: 'Craftsman', salary: 20 },
  { name: 'Farmer', salary: 20 },
  { name: 'Engineer', salary: 50 },
  { name: 'Soldier', salary: 30 }
];

// Tables de modificateurs
const MODIFIER_TABLES = [
  { id: 1, name: 'Effets généraux sur la nourriture', description: 'Modificateurs généraux affectant la production de nourriture' },
  { id: 2, name: 'Technologies qui affectent la production de nourriture', description: 'Modificateurs technologiques affectant la production de nourriture' },
  { id: 3, name: 'Péremption', description: 'Facteurs affectant la péremption de la nourriture' },
  { id: 4, name: 'Effets généraux sur la mine', description: 'Modificateurs généraux affectant la production minière' },
  { id: 5, name: 'Ingénierie', description: 'Modificateurs d\'ingénierie affectant la production minière' },
  { id: 6, name: 'Technologies qui affectent la mine', description: 'Modificateurs technologiques affectant la production minière' },
  { id: 7, name: 'Coûts généraux', description: 'Modificateurs affectant les coûts généraux' },
  { id: 8, name: 'Modificateurs généraux des charges', description: 'Modificateurs généraux affectant les charges' },
  { id: 10, name: 'Effets moral', description: 'Modificateurs affectant le moral' },
  { id: 11, name: 'Effets généraux sur les matériaux', description: 'Modificateurs généraux affectant la production de matériaux' },
  { id: 12, name: 'Technologies qui affectent la génération de matériaux', description: 'Modificateurs technologiques affectant la production de matériaux' },
  { id: 13, name: 'Efficacité de la santé', description: 'Modificateurs affectant l\'efficacité des soins de santé' },
  { id: 14, name: 'Effets généraux sur la construction', description: 'Modificateurs généraux affectant la construction' },
  { id: 15, name: 'Technologie qui affecte la construction de bâtiments', description: 'Modificateurs technologiques affectant la construction' },
  { id: 16, name: 'Effets sur la recherche', description: 'Modificateurs affectant la recherche' },
  { id: 17, name: 'Effets sur la renommée', description: 'Modificateurs affectant la renommée' },
  { id: 18, name: 'Effets sur le commerce', description: 'Modificateurs affectant le commerce' },
  { id: 19, name: 'Efficacité de la justice', description: 'Modificateurs affectant l\'efficacité de la justice' },
  { id: 20, name: 'Armée', description: 'Modificateurs affectant l\'armée' }
];

// Valeurs par défaut
const DEFAULT_VALUES = {
  FOOD_PER_FARMER: 4,
  MATERIALS_PER_WORKER: 4,
  MATERIALS_PER_MINER: 2,
  MINING_VALUE: 30,
  TRADING_VALUE: 25,
  EFFORT_PER_WORKER: 10,
  PERISHABLE_RATE: 0.2,
  BASE_CHARGES: 50 // Added missing base charges value
};

module.exports = {
  MONTHS,
  SEASONS,
  MORAL_TITLES,
  JOBS,
  MODIFIER_TABLES,
  DEFAULT_VALUES
};
