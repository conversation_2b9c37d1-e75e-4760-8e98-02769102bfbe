require('dotenv').config();
const express = require('express');
const cors = require('cors');
const morgan = require('morgan');
const path = require('path');
const initializeDatabase = require('./db/init');
const runMigrations = require('./db/run-migrations');

const app = express();
const PORT = process.env.PORT || 3001;

app.use(cors());
app.use(express.json());
app.use(morgan('dev'));

// Import routes
const gameRoutes = require('./routes/game');
const modifiersRoutes = require('./routes/modifiers');
const inhabitantsRoutes = require('./routes/inhabitantsRoutes');
const jobTransferRoutes = require('./routes/jobTransfer');
const statsRoutes = require('./routes/stats');
const buildingsRoutes = require('./routes/buildings');

// Use routes
app.use('/api/game', gameRoutes);
app.use('/api/modifiers', modifiersRoutes);
app.use('/api/inhabitants', inhabitantsRoutes);
app.use('/api/job-transfer', jobTransferRoutes);
app.use('/api/stats', statsRoutes);
app.use('/api/buildings', buildingsRoutes);

// Basic health check
app.get('/health', (req, res) => {
    res.json({ status: 'ok' });
});

// Serve static files from the React app in production
if (process.env.NODE_ENV === 'production') {
    app.use(express.static(path.join(__dirname, '../client/build')));

    // Handle any requests that don't match the ones above
    app.get('*', (req, res) => {
        res.sendFile(path.join(__dirname, '../client/build/index.html'));
    });
} else {
    // In development, redirect root to API documentation or health check
    app.get('/', (req, res) => {
        res.json({
            message: 'Mine Simulator API',
            version: '1.0.0',
            endpoints: [
                '/api/game',
                '/api/modifiers',
                '/api/inhabitants',
                '/api/job-transfer',
                '/api/stats',
                '/health'
            ]
        });
    });
}

// Error handler
app.use((err, req, res, next) => {
    console.error(err.stack);
    res.status(500).json({ error: 'Something broke!' });
});

// Start the Express server
const server = app.listen(PORT, async () => {
    await initializeDatabase();
    await runMigrations();
    console.log(`Server running on port ${PORT}`);
});

// Start the WebSocket server
try {
    // Import the WebSocket server module and start it
    const WebSocket = require('ws');
    const http = require('http');

    // Create HTTP server for WebSocket
    const wsServer = http.createServer();

    // Create WebSocket server
    const wss = new WebSocket.Server({ server: wsServer });

    // Keep track of connected clients
    const clients = new Set();

    // Handle WebSocket connections
    wss.on('connection', (ws) => {
        console.log('Client connected');

        // Add client to the set
        clients.add(ws);

        // Send welcome message
        ws.send(JSON.stringify({
            type: 'WELCOME',
            payload: {
                message: 'Connected to Mine Simulator WebSocket Server',
                timestamp: Date.now()
            }
        }));

        // Handle client disconnection
        ws.on('close', () => {
            console.log('Client disconnected');
            clients.delete(ws);
        });
    });

    // Broadcast an update to all connected clients
    global.broadcastUpdate = (action) => {
        const update = {
            type: 'SERVER_UPDATE',
            payload: {
                action,
                timestamp: Date.now()
            }
        };

        for (const client of clients) {
            if (client.readyState === WebSocket.OPEN) {
                client.send(JSON.stringify(update));
            }
        }
    };

    // Start the WebSocket server
    const WS_PORT = process.env.WS_PORT || 3003;
    wsServer.listen(WS_PORT, () => {
        console.log(`WebSocket server running on port ${WS_PORT}`);
    });

} catch (error) {
    console.error('Failed to start WebSocket server:', error);
}
