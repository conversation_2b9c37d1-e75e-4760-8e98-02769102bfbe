/**
 * Migration to create buildings and building_plans tables
 */
const db = require('../db');

async function createBuildingsTables() {
  console.log('Creating buildings tables...');
  
  try {
    // Check if building_plans table exists
    const buildingPlansTableExists = await db.get(
      "SELECT name FROM sqlite_master WHERE type='table' AND name='building_plans'"
    );
    
    if (!buildingPlansTableExists) {
      console.log('Creating building_plans table...');
      await db.run(`
        CREATE TABLE building_plans (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          description TEXT,
          type TEXT NOT NULL,
          tech_level INTEGER DEFAULT 1,
          materials_cost INTEGER DEFAULT 0,
          food_cost INTEGER DEFAULT 0,
          gold_cost INTEGER DEFAULT 0,
          maintenance_cost INTEGER DEFAULT 0,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `);
      
      // Insert some sample building plans
      await db.run(`
        INSERT INTO building_plans (name, description, type, tech_level, materials_cost, food_cost, gold_cost, maintenance_cost)
        VALUES 
          ('Hutte', 'Une simple hutte en bois et paille', 'habitation', 1, 50, 20, 0, 5),
          ('Maison', 'Une maison en bois', 'habitation', 2, 100, 30, 10, 10),
          ('Ferme', 'Une ferme pour produire de la nourriture', 'production', 1, 80, 40, 5, 15),
          ('Atelier', 'Un atelier pour les artisans', 'production', 2, 120, 30, 20, 20),
          ('Tour de guet', 'Une tour pour surveiller les alentours', 'defense', 1, 70, 20, 15, 10),
          ('Palissade', 'Une palissade en bois pour protéger le village', 'defense', 1, 150, 30, 0, 15),
          ('Puits', 'Un puits pour l''eau potable', 'infrastructure', 1, 60, 20, 5, 5),
          ('Entrepôt', 'Un entrepôt pour stocker les ressources', 'infrastructure', 2, 100, 20, 10, 10)
      `);
      console.log('Building plans table created and populated with sample data');
    } else {
      console.log('Building plans table already exists');
    }
    
    // Check if buildings table exists
    const buildingsTableExists = await db.get(
      "SELECT name FROM sqlite_master WHERE type='table' AND name='buildings'"
    );
    
    if (!buildingsTableExists) {
      console.log('Creating buildings table...');
      await db.run(`
        CREATE TABLE buildings (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          type TEXT NOT NULL,
          level INTEGER DEFAULT 1,
          plan_id INTEGER,
          materials_cost INTEGER DEFAULT 0,
          food_cost INTEGER DEFAULT 0,
          gold_cost INTEGER DEFAULT 0,
          maintenance_cost INTEGER DEFAULT 0,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (plan_id) REFERENCES building_plans(id)
        )
      `);
      
      // Insert some sample buildings
      await db.run(`
        INSERT INTO buildings (name, type, level, plan_id, materials_cost, food_cost, gold_cost, maintenance_cost)
        VALUES 
          ('Hutte du chef', 'habitation', 1, 1, 50, 20, 0, 5),
          ('Ferme principale', 'production', 1, 3, 80, 40, 5, 15),
          ('Tour de guet nord', 'defense', 1, 5, 70, 20, 15, 10),
          ('Puits central', 'infrastructure', 1, 7, 60, 20, 5, 5)
      `);
      console.log('Buildings table created and populated with sample data');
    } else {
      console.log('Buildings table already exists');
    }
    
    console.log('Buildings tables migration completed successfully');
    return true;
  } catch (error) {
    console.error('Error creating buildings tables:', error);
    return false;
  }
}

module.exports = createBuildingsTables;
