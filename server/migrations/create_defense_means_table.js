const db = require('../db');

/**
 * Migration pour créer la table defense_means
 */
async function createDefenseMeansTable() {
  try {
    // Vérifier si la table existe déjà
    const tableExists = await db.get(
      "SELECT name FROM sqlite_master WHERE type='table' AND name='defense_means'"
    );

    if (tableExists) {
      console.log('defense_means table already exists');
      return;
    }

    // Créer la table defense_means
    await db.run(`
      CREATE TABLE defense_means (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT NOT NULL,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    console.log('defense_means table created successfully');
  } catch (error) {
    console.error('Error creating defense_means table:', error);
    throw error;
  }
}

module.exports = createDefenseMeansTable;
