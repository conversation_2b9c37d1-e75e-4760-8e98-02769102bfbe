const request = require('supertest');
const express = require('express');
const gameRoutes = require('../../routes/game');

// Mock de la base de données
jest.mock('../../db', () => ({
  get: jest.fn(),
  all: jest.fn(),
  run: jest.fn()
}));

// Importer le module db après le mock
const db = require('../../db');

// Créer une application Express pour les tests
const app = express();
app.use(express.json());
app.use('/api/game', gameRoutes);

describe('Game Routes', () => {
  beforeEach(() => {
    // Réinitialiser les mocks
    jest.clearAllMocks();
  });

  describe('POST /api/game/jobs/change', () => {
    test('should change job numbers successfully', async () => {
      // Configurer les mocks
      db.get
        .mockResolvedValueOnce({ id: 2, name: 'Miner', number: 5, free: 1 }) // job
        .mockResolvedValueOnce({ id: 1, name: 'Worker', number: 10, free: 0 }); // workerJob
      
      db.all.mockResolvedValue([
        { id: 1, name: 'Worker', number: 9, free: 0 },
        { id: 2, name: 'Miner', number: 6, free: 1 }
      ]);

      // Faire la requête
      const response = await request(app)
        .post('/jobs/change')
        .send({ jobId: 2, change: 1 });

      // Vérifier la réponse
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.jobs).toHaveLength(2);
      
      // Vérifier que les bonnes requêtes ont été faites
      expect(db.run).toHaveBeenCalledTimes(2);
      expect(db.run).toHaveBeenCalledWith(
        'UPDATE jobs SET number = number + ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [1, 2]
      );
      expect(db.run).toHaveBeenCalledWith(
        'UPDATE jobs SET number = number - ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [1, 1]
      );
    });

    test('should return error if trying to decrease below free workers', async () => {
      // Configurer les mocks
      db.get
        .mockResolvedValueOnce({ id: 2, name: 'Miner', number: 5, free: 4 }) // job
        .mockResolvedValueOnce({ id: 1, name: 'Worker', number: 10, free: 0 }); // workerJob
      
      // Faire la requête
      const response = await request(app)
        .post('/jobs/change')
        .send({ jobId: 2, change: -2 });

      // Vérifier la réponse
      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Cannot reduce');
    });

    test('should return error if not enough workers available', async () => {
      // Configurer les mocks
      db.get
        .mockResolvedValueOnce({ id: 2, name: 'Miner', number: 5, free: 1 }) // job
        .mockResolvedValueOnce({ id: 1, name: 'Worker', number: 2, free: 0 }); // workerJob
      
      // Faire la requête
      const response = await request(app)
        .post('/jobs/change')
        .send({ jobId: 2, change: 5 });

      // Vérifier la réponse
      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Not enough Workers available');
    });
  });

  describe('POST /api/game/jobs/free', () => {
    test('should change free workers successfully', async () => {
      // Configurer les mocks
      db.get.mockResolvedValueOnce({ id: 2, name: 'Miner', number: 5, free: 1 }); // job
      
      db.all.mockResolvedValue([
        { id: 1, name: 'Worker', number: 10, free: 0 },
        { id: 2, name: 'Miner', number: 5, free: 2 }
      ]);

      // Faire la requête
      const response = await request(app)
        .post('/jobs/free')
        .send({ jobId: 2, change: 1 });

      // Vérifier la réponse
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.jobs).toHaveLength(2);
      
      // Vérifier que les bonnes requêtes ont été faites
      expect(db.run).toHaveBeenCalledTimes(1);
      expect(db.run).toHaveBeenCalledWith(
        'UPDATE jobs SET free = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [2, 2]
      );
    });

    test('should return error if trying to set free workers above total', async () => {
      // Configurer les mocks
      db.get.mockResolvedValueOnce({ id: 2, name: 'Miner', number: 5, free: 4 }); // job
      
      // Faire la requête
      const response = await request(app)
        .post('/jobs/free')
        .send({ jobId: 2, change: 2 });

      // Vérifier la réponse
      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Cannot have more free workers than total workers');
    });
  });

  describe('POST /api/game/workers/change', () => {
    test('should change worker count successfully', async () => {
      // Configurer les mocks
      db.get.mockResolvedValueOnce({ id: 1, name: 'Worker', number: 10, free: 0 }); // workerJob
      
      db.all.mockResolvedValue([
        { id: 1, name: 'Worker', number: 12, free: 0 },
        { id: 2, name: 'Miner', number: 5, free: 1 }
      ]);

      // Faire la requête
      const response = await request(app)
        .post('/workers/change')
        .send({ change: 2 });

      // Vérifier la réponse
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.jobs).toHaveLength(2);
      
      // Vérifier que les bonnes requêtes ont été faites
      expect(db.run).toHaveBeenCalledTimes(2);
      expect(db.run).toHaveBeenCalledWith(
        'UPDATE jobs SET number = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [12, 1]
      );
    });

    test('should return error if trying to set negative workers', async () => {
      // Configurer les mocks
      db.get.mockResolvedValueOnce({ id: 1, name: 'Worker', number: 2, free: 0 }); // workerJob
      
      // Faire la requête
      const response = await request(app)
        .post('/workers/change')
        .send({ change: -3 });

      // Vérifier la réponse
      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Cannot have negative workers');
    });
  });
});
