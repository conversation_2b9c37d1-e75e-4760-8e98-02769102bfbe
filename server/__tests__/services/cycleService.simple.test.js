const cycleService = require('../../services/cycleService');
const { MONTHS, SEASONS } = require('../../utils/constants');

// Mock de la base de données
jest.mock('../../db', () => ({
  get: jest.fn(),
  all: jest.fn(),
  run: jest.fn()
}));

// Mock de FoodService
jest.mock('../../services/foodService', () => ({
  fixDuplicatePerishableEntries: jest.fn(),
  updateFoodModifiers: jest.fn(),
  calculateFoodProduction: jest.fn(),
  calculateFoodConsumption: jest.fn(),
  calculateNewFoodReserves: jest.fn()
}));

// Importer le module db après le mock
const db = require('../../db');
const FoodService = require('../../services/foodService');

describe('Cycle Service', () => {
  beforeEach(() => {
    // Réinitialiser les mocks
    jest.clearAllMocks();
  });

  describe('calculateMoralModifier', () => {
    test('should calculate moral modifier correctly for neutral value', () => {
      // Appeler la fonction avec une valeur neutre (1000)
      const result = cycleService.calculateMoralModifier(1000);
      
      // Pour une valeur de 1000, le modificateur devrait être proche de 0
      expect(result).toBeCloseTo(0, 5);
    });
    
    test('should calculate moral modifier correctly for high value', () => {
      // Appeler la fonction avec une valeur élevée (1500)
      const result = cycleService.calculateMoralModifier(1500);
      
      // Pour une valeur de 1500, le modificateur devrait être positif
      expect(result).toBeGreaterThan(0);
    });
    
    test('should calculate moral modifier correctly for low value', () => {
      // Appeler la fonction avec une valeur basse (500)
      const result = cycleService.calculateMoralModifier(500);
      
      // Pour une valeur de 500, le modificateur devrait être négatif
      expect(result).toBeLessThan(0);
    });
  });

  describe('normalDistribution', () => {
    test('should calculate normal distribution correctly', () => {
      // Appeler la fonction avec une valeur égale à la moyenne
      const result = cycleService.normalDistribution(1000, 1000, 1000);
      
      // Pour une valeur égale à la moyenne, le résultat devrait être 0.5
      expect(result).toBeCloseTo(0.5, 5);
    });
  });

  describe('erf', () => {
    test('should calculate error function correctly', () => {
      // Appeler la fonction avec 0
      const result = cycleService.erf(0);
      
      // Pour x = 0, erf(0) = 0
      expect(result).toBeCloseTo(0, 5);
    });
    
    test('should handle negative values correctly', () => {
      // Appeler la fonction avec -1
      const result = cycleService.erf(-1);
      
      // Pour x = -1, erf(-1) = -erf(1)
      expect(result).toBeLessThan(0);
    });
  });
});
