const miningService = require('../../services/miningService');

// Mock de la base de données
jest.mock('../../db', () => ({
  get: jest.fn(),
  all: jest.fn(),
  run: jest.fn()
}));

// Importer le module db après le mock
const db = require('../../db');

describe('Mining Service', () => {
  beforeEach(() => {
    // Réinitialiser les mocks
    jest.clearAllMocks();
  });

  describe('calculateMiningProduction', () => {
    test('should calculate mining production correctly', async () => {
      // Configurer les mocks
      db.all.mockResolvedValueOnce([
        { id: 2, name: 'Miner', number: 5, free: 1 },
        { id: 4, name: 'Engineer', number: 3, free: 0 }
      ]); // jobs
      
      db.all.mockResolvedValueOnce([
        { id: 1, table_id: 4, value: 0.1, title: 'Tech Level' },
        { id: 2, table_id: 4, value: 0.2, title: 'Equipment' }
      ]); // miningModifiers
      
      // Moral modifier mock
      const moralModifier = 0.3;
      
      // Appeler la fonction
      const result = await miningService.calculateMiningProduction(moralModifier);
      
      // Vérifier le résultat
      expect(result).toHaveProperty('activeMiners', 4); // 5 mineurs - 1 gratuit
      expect(result).toHaveProperty('engineers', 3);
      expect(result).toHaveProperty('baseProduction');
      expect(result).toHaveProperty('engineerBonus');
      expect(result).toHaveProperty('miningModifiers');
      expect(result).toHaveProperty('moralModifier', 0.3);
      
      // La production de base devrait être : activeMiners * 2
      expect(result.baseProduction).toBe(8);
      
      // Le bonus des ingénieurs devrait être : engineers * 0.5
      expect(result.engineerBonus).toBe(1.5);
      
      // Les modificateurs de minage devraient être la somme des valeurs
      expect(result.miningModifiers).toBe(0.3);
      
      // La production totale devrait être : baseProduction * (1 + engineerBonus/baseProduction + miningModifiers + moralModifier)
      // 8 * (1 + 1.5/8 + 0.3 + 0.3) = 8 * 1.7875 = 14.3
      expect(result).toHaveProperty('miningProduction');
      expect(result.miningProduction).toBeCloseTo(14.3);
    });
    
    test('should handle zero miners correctly', async () => {
      // Configurer les mocks
      db.all.mockResolvedValueOnce([
        { id: 2, name: 'Miner', number: 0, free: 0 },
        { id: 4, name: 'Engineer', number: 3, free: 0 }
      ]); // jobs
      
      db.all.mockResolvedValueOnce([
        { id: 1, table_id: 4, value: 0.1, title: 'Tech Level' },
        { id: 2, table_id: 4, value: 0.2, title: 'Equipment' }
      ]); // miningModifiers
      
      // Moral modifier mock
      const moralModifier = 0.3;
      
      // Appeler la fonction
      const result = await miningService.calculateMiningProduction(moralModifier);
      
      // Vérifier le résultat
      expect(result).toHaveProperty('activeMiners', 0);
      expect(result).toHaveProperty('miningProduction', 0);
    });
  });

  describe('calculateMiningExpenses', () => {
    test('should calculate mining expenses correctly', async () => {
      // Configurer les mocks
      db.all.mockResolvedValueOnce([
        { id: 1, name: 'Worker', number: 10, free: 2, salary: 1 },
        { id: 2, name: 'Miner', number: 5, free: 1, salary: 2 },
        { id: 4, name: 'Engineer', number: 3, free: 0, salary: 3 }
      ]); // jobs
      
      // Appeler la fonction
      const result = await miningService.calculateMiningExpenses();
      
      // Vérifier le résultat
      // Workers: (10-2)*1 = 8
      // Miners: (5-1)*2 = 8
      // Engineers: (3-0)*3 = 9
      // Total: 8 + 8 + 9 = 25
      expect(result).toBe(25);
    });
  });

  describe('calculateMiningRevenue', () => {
    test('should calculate mining revenue correctly', async () => {
      // Configurer les mocks pour la production
      const miningProduction = 14.3;
      
      // Appeler la fonction
      const result = miningService.calculateMiningRevenue(miningProduction);
      
      // Vérifier le résultat
      // Revenue should be equal to mining production
      expect(result).toBe(14.3);
    });
  });
});
