const moralService = require('../../services/moralService');

// Mock de la base de données
jest.mock('../../db', () => ({
  get: jest.fn(),
  all: jest.fn(),
  run: jest.fn()
}));

// Importer le module db après le mock
const db = require('../../db');

describe('Moral Service', () => {
  beforeEach(() => {
    // Réinitialiser les mocks
    jest.clearAllMocks();
  });

  describe('getMoralModifiers', () => {
    test('should get moral modifiers correctly', async () => {
      // Configurer les mocks
      db.all.mockResolvedValueOnce([
        { id: 1, table_id: 3, value: 50, title: 'Bonne nourriture', description: 'Les habitants sont bien nourris', start_date: '1349-01-01' },
        { id: 2, table_id: 3, value: -30, title: 'Logement insuffisant', description: 'Manque de logements', start_date: '1349-01-01' }
      ]);
      
      // Appeler la fonction
      const result = await moralService.getMoralModifiers();
      
      // Vérifier le résultat
      expect(result).toHaveLength(2);
      expect(result[0]).toHaveProperty('title', 'Bonne nourriture');
      expect(result[0]).toHaveProperty('value', 50);
      expect(result[1]).toHaveProperty('title', 'Logement insuffisant');
      expect(result[1]).toHaveProperty('value', -30);
    });
  });

  describe('addMoralModifier', () => {
    test('should add moral modifier correctly', async () => {
      // Configurer les mocks
      db.run.mockResolvedValueOnce({ lastID: 3 });
      
      // Appeler la fonction
      const result = await moralService.addMoralModifier({
        title: 'Nouveau modificateur',
        value: 20,
        description: 'Description du nouveau modificateur',
        start_date: '1349-01-01'
      });
      
      // Vérifier le résultat
      expect(result).toHaveProperty('success', true);
      expect(result).toHaveProperty('modifier');
      expect(result.modifier).toHaveProperty('id', 3);
      expect(result.modifier).toHaveProperty('title', 'Nouveau modificateur');
      expect(result.modifier).toHaveProperty('value', 20);
      
      // Vérifier que la requête a été exécutée avec les bons paramètres
      expect(db.run).toHaveBeenCalledWith(
        'INSERT INTO modifiers (table_id, title, value, description, start_date) VALUES (?, ?, ?, ?, ?)',
        [3, 'Nouveau modificateur', 20, 'Description du nouveau modificateur', '1349-01-01']
      );
    });
  });

  describe('updateMoralModifier', () => {
    test('should update moral modifier correctly', async () => {
      // Configurer les mocks
      db.run.mockResolvedValueOnce({ changes: 1 });
      
      // Appeler la fonction
      const result = await moralService.updateMoralModifier(1, {
        title: 'Modificateur mis à jour',
        value: 30,
        description: 'Description mise à jour',
        start_date: '1349-02-01'
      });
      
      // Vérifier le résultat
      expect(result).toHaveProperty('success', true);
      
      // Vérifier que la requête a été exécutée avec les bons paramètres
      expect(db.run).toHaveBeenCalledWith(
        'UPDATE modifiers SET title = ?, value = ?, description = ?, start_date = ? WHERE id = ?',
        ['Modificateur mis à jour', 30, 'Description mise à jour', '1349-02-01', 1]
      );
    });
  });

  describe('deleteMoralModifier', () => {
    test('should delete moral modifier correctly', async () => {
      // Configurer les mocks
      db.run.mockResolvedValueOnce({ changes: 1 });
      
      // Appeler la fonction
      const result = await moralService.deleteMoralModifier(1);
      
      // Vérifier le résultat
      expect(result).toHaveProperty('success', true);
      
      // Vérifier que la requête a été exécutée avec les bons paramètres
      expect(db.run).toHaveBeenCalledWith(
        'DELETE FROM modifiers WHERE id = ?',
        [1]
      );
    });
  });

  describe('calculateMoralValue', () => {
    test('should calculate moral value correctly', async () => {
      // Configurer les mocks
      db.all.mockResolvedValueOnce([
        { id: 1, table_id: 3, value: 50, title: 'Bonne nourriture', description: 'Les habitants sont bien nourris', start_date: '1349-01-01' },
        { id: 2, table_id: 3, value: -30, title: 'Logement insuffisant', description: 'Manque de logements', start_date: '1349-01-01' }
      ]);
      
      // Appeler la fonction
      const result = await moralService.calculateMoralValue();
      
      // Vérifier le résultat
      // La valeur de base est 1000, plus les modificateurs (50 - 30 = 20)
      expect(result).toBe(1020);
    });
  });

  describe('calculateMoralModifier', () => {
    test('should calculate moral modifier correctly for neutral value', () => {
      // Appeler la fonction avec une valeur neutre (1000)
      const result = moralService.calculateMoralModifier(1000);
      
      // Pour une valeur de 1000, le modificateur devrait être 0
      expect(result).toBeCloseTo(0);
    });
    
    test('should calculate moral modifier correctly for high value', () => {
      // Appeler la fonction avec une valeur élevée (1500)
      const result = moralService.calculateMoralModifier(1500);
      
      // Pour une valeur de 1500, le modificateur devrait être positif
      expect(result).toBeGreaterThan(0);
    });
    
    test('should calculate moral modifier correctly for low value', () => {
      // Appeler la fonction avec une valeur basse (500)
      const result = moralService.calculateMoralModifier(500);
      
      // Pour une valeur de 500, le modificateur devrait être négatif
      expect(result).toBeLessThan(0);
    });
  });

  describe('getMoralTitle', () => {
    test('should get moral title correctly for neutral value', () => {
      // Appeler la fonction avec une valeur neutre (1000)
      const result = moralService.getMoralTitle(1000);
      
      // Pour une valeur de 1000, le titre devrait être "Neutre"
      expect(result).toBe('Neutre');
    });
    
    test('should get moral title correctly for high value', () => {
      // Appeler la fonction avec une valeur très élevée (1800)
      const result = moralService.getMoralTitle(1800);
      
      // Pour une valeur de 1800, le titre devrait être "Extatique"
      expect(result).toBe('Extatique');
    });
    
    test('should get moral title correctly for low value', () => {
      // Appeler la fonction avec une valeur très basse (200)
      const result = moralService.getMoralTitle(200);
      
      // Pour une valeur de 200, le titre devrait être "Rébellion"
      expect(result).toBe('Rébellion');
    });
  });

  describe('getMoralEmoji', () => {
    test('should get moral emoji correctly for neutral value', () => {
      // Appeler la fonction avec une valeur neutre (1000)
      const result = moralService.getMoralEmoji(1000);
      
      // Pour une valeur de 1000, l'emoji devrait être "😐"
      expect(result).toBe('😐');
    });
    
    test('should get moral emoji correctly for high value', () => {
      // Appeler la fonction avec une valeur très élevée (1800)
      const result = moralService.getMoralEmoji(1800);
      
      // Pour une valeur de 1800, l'emoji devrait être "🤩"
      expect(result).toBe('🤩');
    });
    
    test('should get moral emoji correctly for low value', () => {
      // Appeler la fonction avec une valeur très basse (200)
      const result = moralService.getMoralEmoji(200);
      
      // Pour une valeur de 200, l'emoji devrait être "🤬"
      expect(result).toBe('🤬');
    });
  });
});
