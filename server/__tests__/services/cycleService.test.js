const cycleService = require('../../services/cycleService');

// Mock de la base de données
jest.mock('../../db', () => ({
  get: jest.fn(),
  all: jest.fn(),
  run: jest.fn()
}));

// Importer le module db après le mock
const db = require('../../db');

describe('Cycle Service', () => {
  beforeEach(() => {
    // Réinitialiser les mocks
    jest.clearAllMocks();
  });

  describe('calculateFoodProduction', () => {
    test('should calculate food production correctly', async () => {
      // Configurer les mocks
      db.all.mockResolvedValueOnce([
        { id: 3, name: 'Farmer', number: 5, free: 1 }
      ]);
      
      db.get.mockResolvedValueOnce({ season_factor: 0.5 }); // gameState
      
      db.all.mockResolvedValueOnce([
        { id: 1, table_id: 1, value: 0.2, title: 'Tech Level' },
        { id: 2, table_id: 1, value: 0.8, title: 'General' }
      ]); // foodModifiers
      
      // Moral modifier mock
      const moralModifier = 0.3;
      
      // Appeler la fonction
      const result = await cycleService.calculateFoodProduction(moralModifier);
      
      // Vérifier le résultat
      expect(result).toHaveProperty('activeFarmers', 4); // 5 fermiers - 1 gratuit
      expect(result).toHaveProperty('foodPerFarmer', 4);
      expect(result).toHaveProperty('seasonFactor', 0.5);
      expect(result).toHaveProperty('foodGeneralModifier', 0.8);
      expect(result).toHaveProperty('foodTechModifier', 0.2);
      expect(result).toHaveProperty('moralModifier', 0.3);
      
      // La production devrait être : activeFarmers * foodPerFarmer * seasonFactor * (1 + foodGeneralModifier + foodTechModifier + moralModifier)
      // 4 * 4 * 0.5 * (1 + 0.8 + 0.2 + 0.3) = 4 * 4 * 0.5 * 2.3 = 18.4
      expect(result).toHaveProperty('production');
      expect(result.production).toBeCloseTo(18.4);
    });
  });

  describe('calculateFoodConsumption', () => {
    test('should calculate food consumption correctly', async () => {
      // Configurer les mocks
      db.all.mockResolvedValueOnce([
        { id: 1, name: 'Worker', number: 10, free: 0 },
        { id: 2, name: 'Miner', number: 5, free: 1 },
        { id: 3, name: 'Farmer', number: 5, free: 1 }
      ]); // jobs
      
      // Appeler la fonction
      const result = await cycleService.calculateFoodConsumption();
      
      // Vérifier le résultat
      expect(result).toHaveProperty('totalInhabitants', 20); // 10 + 5 + 5
      expect(result).toHaveProperty('consumption', 20); // 1 par habitant
    });
  });

  describe('calculateFoodPerishable', () => {
    test('should calculate food perishable correctly', async () => {
      // Configurer les mocks
      db.all.mockResolvedValueOnce([
        { id: 1, table_id: 2, value: 0.2, title: 'Base Perishable' }
      ]); // perishableModifiers
      
      // Appeler la fonction
      const result = await cycleService.calculateFoodPerishable();
      
      // Vérifier le résultat
      expect(result).toBeCloseTo(0.2);
    });
  });

  describe('processCycle', () => {
    test('should process a cycle correctly', async () => {
      // Configurer les mocks pour gameState
      db.get.mockResolvedValueOnce({
        id: 1,
        cycle_number: 1,
        month: 'Tanzanite',
        month_number: 1,
        year: 1349,
        season: 'Hiver',
        season_factor: 0.5,
        treasure: 100,
        materials: 50,
        food_reserves: 100
      });
      
      // Mock pour les jobs
      db.all.mockResolvedValueOnce([
        { id: 1, name: 'Worker', number: 10, free: 0, salary: 1 },
        { id: 2, name: 'Miner', number: 5, free: 1, salary: 2 },
        { id: 3, name: 'Farmer', number: 5, free: 1, salary: 2 }
      ]);
      
      // Mock pour les modifiers
      db.all
        .mockResolvedValueOnce([]) // moralModifiers
        .mockResolvedValueOnce([{ id: 1, table_id: 1, value: 0.2, title: 'Tech Level' }]) // foodModifiers
        .mockResolvedValueOnce([{ id: 1, table_id: 2, value: 0.2, title: 'Base Perishable' }]); // perishableModifiers
      
      // Mock pour les mises à jour
      db.run.mockResolvedValue({ changes: 1 });
      
      // Appeler la fonction
      const result = await cycleService.processCycle();
      
      // Vérifier le résultat
      expect(result).toHaveProperty('success', true);
      expect(result).toHaveProperty('gameState');
      expect(result).toHaveProperty('jobs');
      expect(result).toHaveProperty('calculations');
      
      // Vérifier que les calculs ont été effectués
      expect(result.calculations).toHaveProperty('food');
      expect(result.calculations.food).toHaveProperty('production');
      expect(result.calculations.food).toHaveProperty('consumption');
      expect(result.calculations.food).toHaveProperty('perishable');
      
      // Vérifier que les mises à jour ont été effectuées
      expect(db.run).toHaveBeenCalled();
    });
  });
});
