const moralService = require('../../services/moralService');

// Mock de la base de données
jest.mock('../../db', () => ({
  get: jest.fn(),
  all: jest.fn(),
  run: jest.fn()
}));

// Importer le module db après le mock
const db = require('../../db');

describe('Moral Service', () => {
  beforeEach(() => {
    // Réinitialiser les mocks
    jest.clearAllMocks();
  });

  describe('calculateMoralModifier', () => {
    test('should calculate moral modifier correctly for neutral value', () => {
      // Appeler la fonction avec une valeur neutre (1000)
      const result = moralService.calculateMoralModifier(1000);
      
      // Pour une valeur de 1000, le modificateur devrait être proche de 0
      expect(result).toBeCloseTo(0, 5);
    });
    
    test('should calculate moral modifier correctly for high value', () => {
      // Appeler la fonction avec une valeur élevée (1500)
      const result = moralService.calculateMoralModifier(1500);
      
      // Pour une valeur de 1500, le modificateur devrait être positif
      expect(result).toBeGreaterThan(0);
    });
    
    test('should calculate moral modifier correctly for low value', () => {
      // Appeler la fonction avec une valeur basse (500)
      const result = moralService.calculateMoralModifier(500);
      
      // Pour une valeur de 500, le modificateur devrait être négatif
      expect(result).toBeLessThan(0);
    });
  });

  describe('getMoralTitle', () => {
    test('should get moral title correctly for neutral value', () => {
      // Appeler la fonction avec une valeur neutre (1000)
      const result = moralService.getMoralTitle(1000);
      
      // Pour une valeur de 1000, le titre devrait être "Neutre"
      expect(result).toBe('Neutre');
    });
  });
});
