const foodService = require('../../services/foodService');

// Mock de la base de données
jest.mock('../../db', () => ({
  get: jest.fn(),
  all: jest.fn(),
  run: jest.fn()
}));

// Importer le module db après le mock
const db = require('../../db');

describe('Food Service', () => {
  beforeEach(() => {
    // Réinitialiser les mocks
    jest.clearAllMocks();
  });

  describe('getFoodModifiers', () => {
    test('should get food modifiers correctly', async () => {
      // Configurer les mocks
      db.all.mockResolvedValueOnce([
        { id: 1, table_id: 1, value: 0.2, title: 'Tech Level', description: 'Bonus from technology' },
        { id: 2, table_id: 1, value: 0.8, title: 'General', description: 'General food production' }
      ]);
      
      // Appeler la fonction
      const result = await foodService.getFoodModifiers();
      
      // Vérifier le résultat
      expect(result).toHaveLength(2);
      expect(result[0]).toHaveProperty('title', 'Tech Level');
      expect(result[0]).toHaveProperty('value', 0.2);
      expect(result[1]).toHaveProperty('title', 'General');
      expect(result[1]).toHaveProperty('value', 0.8);
    });
  });

  describe('getPerishableModifiers', () => {
    test('should get perishable modifiers correctly', async () => {
      // Configurer les mocks
      db.all.mockResolvedValueOnce([
        { id: 1, table_id: 2, value: 0.2, title: 'Base Perishable', description: 'Base food perishable rate' }
      ]);
      
      // Appeler la fonction
      const result = await foodService.getPerishableModifiers();
      
      // Vérifier le résultat
      expect(result).toHaveLength(1);
      expect(result[0]).toHaveProperty('title', 'Base Perishable');
      expect(result[0]).toHaveProperty('value', 0.2);
    });
  });

  describe('addFoodModifier', () => {
    test('should add food modifier correctly', async () => {
      // Configurer les mocks
      db.run.mockResolvedValueOnce({ lastID: 3 });
      
      // Appeler la fonction
      const result = await foodService.addFoodModifier({
        title: 'New Modifier',
        value: 0.5,
        description: 'New food modifier'
      });
      
      // Vérifier le résultat
      expect(result).toHaveProperty('success', true);
      expect(result).toHaveProperty('modifier');
      expect(result.modifier).toHaveProperty('id', 3);
      expect(result.modifier).toHaveProperty('title', 'New Modifier');
      expect(result.modifier).toHaveProperty('value', 0.5);
      
      // Vérifier que la requête a été exécutée avec les bons paramètres
      expect(db.run).toHaveBeenCalledWith(
        'INSERT INTO modifiers (table_id, title, value, description) VALUES (?, ?, ?, ?)',
        [1, 'New Modifier', 0.5, 'New food modifier']
      );
    });
  });

  describe('addPerishableModifier', () => {
    test('should add perishable modifier correctly', async () => {
      // Configurer les mocks
      db.run.mockResolvedValueOnce({ lastID: 2 });
      
      // Appeler la fonction
      const result = await foodService.addPerishableModifier({
        title: 'New Perishable',
        value: -0.1,
        description: 'New perishable modifier'
      });
      
      // Vérifier le résultat
      expect(result).toHaveProperty('success', true);
      expect(result).toHaveProperty('modifier');
      expect(result.modifier).toHaveProperty('id', 2);
      expect(result.modifier).toHaveProperty('title', 'New Perishable');
      expect(result.modifier).toHaveProperty('value', -0.1);
      
      // Vérifier que la requête a été exécutée avec les bons paramètres
      expect(db.run).toHaveBeenCalledWith(
        'INSERT INTO modifiers (table_id, title, value, description) VALUES (?, ?, ?, ?)',
        [2, 'New Perishable', -0.1, 'New perishable modifier']
      );
    });
  });

  describe('updateModifier', () => {
    test('should update modifier correctly', async () => {
      // Configurer les mocks
      db.run.mockResolvedValueOnce({ changes: 1 });
      
      // Appeler la fonction
      const result = await foodService.updateModifier(1, {
        title: 'Updated Modifier',
        value: 0.3,
        description: 'Updated description'
      });
      
      // Vérifier le résultat
      expect(result).toHaveProperty('success', true);
      
      // Vérifier que la requête a été exécutée avec les bons paramètres
      expect(db.run).toHaveBeenCalledWith(
        'UPDATE modifiers SET title = ?, value = ?, description = ? WHERE id = ?',
        ['Updated Modifier', 0.3, 'Updated description', 1]
      );
    });
  });

  describe('deleteModifier', () => {
    test('should delete modifier correctly', async () => {
      // Configurer les mocks
      db.run.mockResolvedValueOnce({ changes: 1 });
      
      // Appeler la fonction
      const result = await foodService.deleteModifier(1);
      
      // Vérifier le résultat
      expect(result).toHaveProperty('success', true);
      
      // Vérifier que la requête a été exécutée avec les bons paramètres
      expect(db.run).toHaveBeenCalledWith(
        'DELETE FROM modifiers WHERE id = ?',
        [1]
      );
    });
  });

  describe('fixDuplicatePerishableEntries', () => {
    test('should fix duplicate perishable entries correctly', async () => {
      // Configurer les mocks
      db.all.mockResolvedValueOnce([
        { id: 1, table_id: 2, value: 0.2, title: 'Base Perishable', description: 'Base food perishable rate' },
        { id: 2, table_id: 2, value: 0.1, title: 'Base Perishable', description: 'Duplicate entry' }
      ]);
      
      db.run.mockResolvedValueOnce({ changes: 1 });
      
      // Appeler la fonction
      const result = await foodService.fixDuplicatePerishableEntries();
      
      // Vérifier le résultat
      expect(result).toHaveProperty('success', true);
      expect(result).toHaveProperty('deleted', 1);
      
      // Vérifier que la requête a été exécutée avec les bons paramètres
      expect(db.run).toHaveBeenCalledWith(
        'DELETE FROM modifiers WHERE id = ?',
        [2]
      );
    });
    
    test('should handle no duplicates correctly', async () => {
      // Configurer les mocks
      db.all.mockResolvedValueOnce([
        { id: 1, table_id: 2, value: 0.2, title: 'Base Perishable', description: 'Base food perishable rate' }
      ]);
      
      // Appeler la fonction
      const result = await foodService.fixDuplicatePerishableEntries();
      
      // Vérifier le résultat
      expect(result).toHaveProperty('success', true);
      expect(result).toHaveProperty('deleted', 0);
      
      // Vérifier que la requête de suppression n'a pas été exécutée
      expect(db.run).not.toHaveBeenCalled();
    });
  });
});
