const miningService = require('../../services/miningService');
const { DEFAULT_VALUES } = require('../../utils/constants');

// Mock de la base de données
jest.mock('../../db', () => ({
  get: jest.fn(),
  all: jest.fn(),
  run: jest.fn()
}));

// Importer le module db après le mock
const db = require('../../db');

describe('Mining Service', () => {
  beforeEach(() => {
    // Réinitialiser les mocks
    jest.clearAllMocks();
  });

  describe('calculateMiningProduction', () => {
    test('should calculate mining production correctly', () => {
      // Données de test
      const gameState = {};
      
      const jobs = [
        { name: 'Miner', number: 5, sick: 1 },
        { name: 'Engineer', number: 3, sick: 0 }
      ];
      
      const modifierTables = [
        { id: 4, current_value: 0.1 }, // General mining modifier
        { id: 5, current_value: 0.2 }, // Engineering modifier
        { id: 6, current_value: 0.3 }  // Tech mining modifier
      ];
      
      const moralModifier = 0.3;
      
      // Appeler la fonction
      const result = miningService.calculateMiningProduction(gameState, jobs, modifierTables, moralModifier);
      
      // Vérifier le résultat
      // Nous ne pouvons pas calculer la valeur exacte car elle dépend de DEFAULT_VALUES['MINING_VALUE']
      // Mais nous pouvons vérifier que le résultat est un nombre
      expect(typeof result).toBe('number');
      expect(result).toBeGreaterThan(0);
    });
    
    test('should handle zero miners correctly', () => {
      // Données de test
      const gameState = {};
      
      const jobs = [
        { name: 'Miner', number: 0, sick: 0 },
        { name: 'Engineer', number: 3, sick: 0 }
      ];
      
      const modifierTables = [
        { id: 4, current_value: 0.1 },
        { id: 5, current_value: 0.2 },
        { id: 6, current_value: 0.3 }
      ];
      
      const moralModifier = 0.3;
      
      // Appeler la fonction
      const result = miningService.calculateMiningProduction(gameState, jobs, modifierTables, moralModifier);
      
      // Vérifier le résultat
      expect(result).toBe(0);
    });
  });

  describe('getModifierValue', () => {
    test('should get modifier value correctly', () => {
      // Données de test
      const modifierTables = [
        { id: 4, current_value: 0.1 },
        { id: 5, current_value: 0.2 },
        { id: 6, current_value: 0.3 }
      ];
      
      // Appeler la fonction
      const result = miningService.getModifierValue(modifierTables, 5);
      
      // Vérifier le résultat
      expect(result).toBe(0.2);
    });
    
    test('should return 0 if table not found', () => {
      // Données de test
      const modifierTables = [
        { id: 4, current_value: 0.1 },
        { id: 5, current_value: 0.2 },
        { id: 6, current_value: 0.3 }
      ];
      
      // Appeler la fonction
      const result = miningService.getModifierValue(modifierTables, 999);
      
      // Vérifier le résultat
      expect(result).toBe(0);
    });
  });

  describe('getConfigValue', () => {
    test('should get config value correctly', () => {
      // Appeler la fonction
      const result = miningService.getConfigValue('MINING_VALUE', 0);
      
      // Vérifier le résultat
      expect(result).toBe(DEFAULT_VALUES['MINING_VALUE']);
    });
    
    test('should return default value if config not found', () => {
      // Appeler la fonction
      const result = miningService.getConfigValue('NON_EXISTENT_CONFIG', 42);
      
      // Vérifier le résultat
      expect(result).toBe(42);
    });
  });
});
