const foodService = require('../../services/foodService');

// Mock de la base de données
jest.mock('../../db', () => ({
  get: jest.fn(),
  all: jest.fn(),
  run: jest.fn()
}));

// Importer le module db après le mock
const db = require('../../db');

describe('Food Service', () => {
  beforeEach(() => {
    // Réinitialiser les mocks
    jest.clearAllMocks();
  });

  describe('calculateFoodProduction', () => {
    test('should calculate food production correctly', () => {
      // Données de test
      const gameState = {
        season_factor: 0.5
      };
      
      const jobs = [
        { name: 'Farmer', number: 5, sick: 1 }
      ];
      
      const modifierTables = [
        { id: 1, current_value: 0.2 }, // General food modifier
        { id: 2, current_value: 0.1 }  // Tech food modifier
      ];
      
      const moralModifier = 0.3;
      
      // Appeler la fonction
      const result = foodService.calculateFoodProduction(gameState, jobs, modifierTables, moralModifier);
      
      // Vérifier le résultat
      // Production = (5-1) * 4 * 0.5 * (1 + 0.2 + 0.1 + 0.3) = 4 * 4 * 0.5 * 1.6 = 12.8
      expect(result).toBeCloseTo(12.8);
    });
  });

  describe('calculateFoodConsumption', () => {
    test('should calculate food consumption correctly', () => {
      // Données de test
      const jobs = [
        { name: 'Worker', number: 10 },
        { name: 'Miner', number: 5 },
        { name: 'Farmer', number: 5 }
      ];
      
      // Appeler la fonction
      const result = foodService.calculateFoodConsumption(jobs);
      
      // Vérifier le résultat
      // Consommation = 10 + 5 + 5 = 20
      expect(result).toBe(20);
    });
  });

  describe('getConfigValue', () => {
    test('should get config value correctly', () => {
      // Appeler la fonction
      const result = foodService.getConfigValue('FOOD_PER_FARMER', 0);
      
      // Vérifier le résultat
      expect(result).toBe(4);
    });
    
    test('should return default value if config not found', () => {
      // Appeler la fonction
      const result = foodService.getConfigValue('NON_EXISTENT_CONFIG', 42);
      
      // Vérifier le résultat
      expect(result).toBe(42);
    });
  });
});
