const WebSocket = require('ws');
const http = require('http');
const db = require('./db');

// Create HTTP server
const server = http.createServer();

// Create WebSocket server
const wss = new WebSocket.Server({ server });

// Keep track of connected clients
const clients = new Set();

// Handle WebSocket connections
wss.on('connection', (ws) => {
  console.log('Client connected');

  // Add client to the set
  clients.add(ws);

  // Send welcome message
  ws.send(JSON.stringify({
    type: 'WELCOME',
    payload: {
      message: 'Connected to Mine Simulator WebSocket Server',
      timestamp: Date.now()
    }
  }));

  // Handle messages from client
  ws.on('message', async (message) => {
    try {
      const action = JSON.parse(message);
      console.log('Received action:', action);

      // Process different action types
      switch (action.type) {
        case 'population/updateInhabitant':
          await handleUpdateInhabitant(action);
          break;

        case 'population/createInhabitant':
          await handleCreateInhabitant(action);
          break;

        case 'population/deleteInhabitant':
          await handleDeleteInhabitant(action);
          break;

        case 'jobs/changeJobNumber':
          await handleChangeJobNumber(action);
          break;

        case 'jobs/changeFreeWorkers':
          await handleChangeFreeWorkers(action);
          break;

        default:
          console.log('Unhandled action type:', action.type);
          break;
      }

      // Broadcast a notification to all clients that they should refresh their state
      broadcastUpdate(action);

    } catch (error) {
      console.error('Error processing message:', error);
      ws.send(JSON.stringify({
        type: 'ERROR',
        payload: {
          message: error.message,
          originalAction: message,
          timestamp: Date.now()
        }
      }));
    }
  });

  // Handle client disconnection
  ws.on('close', () => {
    console.log('Client disconnected');
    clients.delete(ws);
  });
});

// Broadcast an update to all connected clients
function broadcastUpdate(action) {
  const update = {
    type: 'SERVER_UPDATE',
    payload: {
      action,
      timestamp: Date.now()
    }
  };

  for (const client of clients) {
    if (client.readyState === WebSocket.OPEN) {
      client.send(JSON.stringify(update));
    }
  }
}

// Handle inhabitant update
async function handleUpdateInhabitant(action) {
  const { id, inhabitant } = action.payload;

  try {
    // Update the inhabitant in the database
    await db.run(
      `UPDATE inhabitants
       SET first_name = ?, last_name = ?, race = ?, gender = ?, job_id = ?,
           is_pc = ?, is_sick = ?, short_description = ?, history = ?, age = ?,
           updated_at = CURRENT_TIMESTAMP
       WHERE id = ?`,
      [
        inhabitant.first_name,
        inhabitant.last_name,
        inhabitant.race,
        inhabitant.gender,
        inhabitant.job_id,
        inhabitant.is_pc,
        inhabitant.is_sick,
        inhabitant.short_description,
        inhabitant.history,
        inhabitant.age,
        id
      ]
    );

    // If job_id changed, we need to sync inhabitants with jobs
    await syncInhabitantsWithJobs();

    console.log(`Inhabitant ${id} updated successfully`);
  } catch (error) {
    console.error('Error updating inhabitant:', error);
    throw new Error(`Failed to update inhabitant: ${error.message}`);
  }
}

// Handle inhabitant creation
async function handleCreateInhabitant(action) {
  const inhabitant = action.payload;

  try {
    // Insert the inhabitant into the database
    const result = await db.run(
      `INSERT INTO inhabitants
       (first_name, last_name, race, gender, job_id, is_pc, is_sick,
        short_description, history, age, arrival_cycle, arrival_month, arrival_year)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        inhabitant.first_name,
        inhabitant.last_name,
        inhabitant.race,
        inhabitant.gender,
        inhabitant.job_id,
        inhabitant.is_pc,
        inhabitant.is_sick,
        inhabitant.short_description,
        inhabitant.history,
        inhabitant.age,
        inhabitant.arrival_cycle || 1,
        inhabitant.arrival_month || 'Rubis',
        inhabitant.arrival_year || 1326
      ]
    );

    // Sync inhabitants with jobs
    await syncInhabitantsWithJobs();

    console.log(`Inhabitant created with ID ${result.lastID}`);
  } catch (error) {
    console.error('Error creating inhabitant:', error);
    throw new Error(`Failed to create inhabitant: ${error.message}`);
  }
}

// Handle inhabitant deletion
async function handleDeleteInhabitant(action) {
  const id = action.payload;

  try {
    // Delete the inhabitant from the database
    await db.run('DELETE FROM inhabitants WHERE id = ?', [id]);

    // Sync inhabitants with jobs
    await syncInhabitantsWithJobs();

    console.log(`Inhabitant ${id} deleted successfully`);
  } catch (error) {
    console.error('Error deleting inhabitant:', error);
    throw new Error(`Failed to delete inhabitant: ${error.message}`);
  }
}

// Handle job number change
async function handleChangeJobNumber(action) {
  const { jobId, change } = action.payload;

  try {
    // Find the worker job
    const workerJob = await db.get('SELECT * FROM jobs WHERE name = ?', ['Worker']);

    if (!workerJob) {
      throw new Error('Worker job not found');
    }

    // Find the target job
    const targetJob = await db.get('SELECT * FROM jobs WHERE id = ?', [jobId]);

    if (!targetJob) {
      throw new Error(`Job with ID ${jobId} not found`);
    }

    // Calculate new values
    const newWorkerNumber = workerJob.number - change;
    const newTargetNumber = targetJob.number + change;

    // Update the worker job
    await db.run(
      'UPDATE jobs SET number = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [newWorkerNumber, workerJob.id]
    );

    // Update the target job
    await db.run(
      'UPDATE jobs SET number = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [newTargetNumber, jobId]
    );

    console.log(`Job numbers updated: Worker ${newWorkerNumber}, ${targetJob.name} ${newTargetNumber}`);
  } catch (error) {
    console.error('Error changing job number:', error);
    throw new Error(`Failed to change job number: ${error.message}`);
  }
}

// Handle free workers change
async function handleChangeFreeWorkers(action) {
  const { jobId, change } = action.payload;

  try {
    // Find the job
    const job = await db.get('SELECT * FROM jobs WHERE id = ?', [jobId]);

    if (!job) {
      throw new Error(`Job with ID ${jobId} not found`);
    }

    // Calculate new value
    const newFree = Math.max(0, Math.min(job.number, job.free + change));

    // Update the job
    await db.run(
      'UPDATE jobs SET free = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [newFree, jobId]
    );

    console.log(`Job ${job.name} free workers updated to ${newFree}`);
  } catch (error) {
    console.error('Error changing free workers:', error);
    throw new Error(`Failed to change free workers: ${error.message}`);
  }
}

// Sync inhabitants with jobs
async function syncInhabitantsWithJobs() {
  try {
    // Get all jobs
    const jobs = await db.all('SELECT * FROM jobs');

    // Reset job numbers
    for (const job of jobs) {
      await db.run('UPDATE jobs SET number = 0 WHERE id = ?', [job.id]);
    }

    // Count inhabitants by job
    const jobCounts = await db.all(`
      SELECT job_id, COUNT(*) as count
      FROM inhabitants
      GROUP BY job_id
    `);

    // Update job numbers
    for (const { job_id, count } of jobCounts) {
      await db.run('UPDATE jobs SET number = ? WHERE id = ?', [count, job_id]);
    }

    console.log('Inhabitants synced with jobs successfully');
  } catch (error) {
    console.error('Error syncing inhabitants with jobs:', error);
    throw new Error(`Failed to sync inhabitants with jobs: ${error.message}`);
  }
}

// Start the server
const PORT = process.env.WS_PORT || 3003;
server.listen(PORT, () => {
  console.log(`WebSocket server running on port ${PORT}`);
});

module.exports = server;
