/**
 * Routes for buildings
 */
const express = require('express');
const router = express.Router();
const db = require('../db');
const { calculateBuildingCost } = require('../utils/buildingUtils');

/**
 * Get all building plans
 * @route GET /api/buildings/plans
 * @returns {Array} - List of building plans
 */
router.get('/plans', async (req, res) => {
  try {
    const plans = await db.all('SELECT * FROM building_plans');
    res.json(plans);
  } catch (error) {
    console.error('Error fetching building plans:', error);
    res.status(500).json({ error: 'Failed to fetch building plans' });
  }
});

/**
 * Get all buildings
 * @route GET /api/buildings
 * @returns {Array} - List of buildings
 */
router.get('/', async (req, res) => {
  try {
    const buildings = await db.all('SELECT * FROM buildings');
    res.json(buildings);
  } catch (error) {
    console.error('Error fetching buildings:', error);
    res.status(500).json({ error: 'Failed to fetch buildings' });
  }
});

/**
 * Get building by ID
 * @route GET /api/buildings/:id
 * @param {number} id - Building ID
 * @returns {Object} - Building details
 */
router.get('/:id', async (req, res) => {
  try {
    const building = await db.get('SELECT * FROM buildings WHERE id = ?', [req.params.id]);
    if (!building) {
      return res.status(404).json({ error: 'Building not found' });
    }
    res.json(building);
  } catch (error) {
    console.error(`Error fetching building ${req.params.id}:`, error);
    res.status(500).json({ error: 'Failed to fetch building' });
  }
});

/**
 * Get building plan by ID
 * @route GET /api/buildings/plans/:id
 * @param {number} id - Building plan ID
 * @returns {Object} - Building plan details
 */
router.get('/plans/:id', async (req, res) => {
  try {
    const plan = await db.get('SELECT * FROM building_plans WHERE id = ?', [req.params.id]);
    if (!plan) {
      return res.status(404).json({ error: 'Building plan not found' });
    }
    res.json(plan);
  } catch (error) {
    console.error(`Error fetching building plan ${req.params.id}:`, error);
    res.status(500).json({ error: 'Failed to fetch building plan' });
  }
});

/**
 * Create a new building
 * @route POST /api/buildings
 * @param {Object} building - Building data
 * @returns {Object} - Created building
 */
router.post('/', async (req, res) => {
  try {
    const { name, type, level, plan_id, materials_cost, food_cost, gold_cost, maintenance_cost } = req.body;

    // Validate required fields
    if (!name || !type || !plan_id) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    const result = await db.run(
      'INSERT INTO buildings (name, type, level, plan_id, materials_cost, food_cost, gold_cost, maintenance_cost) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
      [name, type, level || 1, plan_id, materials_cost || 0, food_cost || 0, gold_cost || 0, maintenance_cost || 0]
    );

    const newBuilding = await db.get('SELECT * FROM buildings WHERE id = ?', [result.lastID]);
    res.status(201).json(newBuilding);
  } catch (error) {
    console.error('Error creating building:', error);
    res.status(500).json({ error: 'Failed to create building' });
  }
});

/**
 * Update a building
 * @route PUT /api/buildings/:id
 * @param {number} id - Building ID
 * @param {Object} building - Building data
 * @returns {Object} - Updated building
 */
router.put('/:id', async (req, res) => {
  try {
    const { name, type, level, plan_id, materials_cost, food_cost, gold_cost, maintenance_cost } = req.body;

    // Check if building exists
    const building = await db.get('SELECT * FROM buildings WHERE id = ?', [req.params.id]);
    if (!building) {
      return res.status(404).json({ error: 'Building not found' });
    }

    await db.run(
      'UPDATE buildings SET name = ?, type = ?, level = ?, plan_id = ?, materials_cost = ?, food_cost = ?, gold_cost = ?, maintenance_cost = ? WHERE id = ?',
      [
        name || building.name,
        type || building.type,
        level || building.level,
        plan_id || building.plan_id,
        materials_cost !== undefined ? materials_cost : building.materials_cost,
        food_cost !== undefined ? food_cost : building.food_cost,
        gold_cost !== undefined ? gold_cost : building.gold_cost,
        maintenance_cost !== undefined ? maintenance_cost : building.maintenance_cost,
        req.params.id
      ]
    );

    const updatedBuilding = await db.get('SELECT * FROM buildings WHERE id = ?', [req.params.id]);
    res.json(updatedBuilding);
  } catch (error) {
    console.error(`Error updating building ${req.params.id}:`, error);
    res.status(500).json({ error: 'Failed to update building' });
  }
});

/**
 * Delete a building
 * @route DELETE /api/buildings/:id
 * @param {number} id - Building ID
 * @returns {Object} - Success message
 */
router.delete('/:id', async (req, res) => {
  try {
    // Check if building exists
    const building = await db.get('SELECT * FROM buildings WHERE id = ?', [req.params.id]);
    if (!building) {
      return res.status(404).json({ error: 'Building not found' });
    }

    await db.run('DELETE FROM buildings WHERE id = ?', [req.params.id]);
    res.json({ success: true, message: 'Building deleted successfully' });
  } catch (error) {
    console.error(`Error deleting building ${req.params.id}:`, error);
    res.status(500).json({ error: 'Failed to delete building' });
  }
});

/**
 * Create a new building plan
 * @route POST /api/buildings/plans
 * @param {Object} plan - Building plan data
 * @returns {Object} - Created building plan
 */
router.post('/plans', async (req, res) => {
  try {
    const { name, description, type, tech_level, materials_cost, food_cost, gold_cost, maintenance_cost } = req.body;

    // Validate required fields
    if (!name || !type) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    const result = await db.run(
      'INSERT INTO building_plans (name, description, type, tech_level, materials_cost, food_cost, gold_cost, maintenance_cost) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
      [name, description || '', type, tech_level || 1, materials_cost || 0, food_cost || 0, gold_cost || 0, maintenance_cost || 0]
    );

    const newPlan = await db.get('SELECT * FROM building_plans WHERE id = ?', [result.lastID]);
    res.status(201).json(newPlan);
  } catch (error) {
    console.error('Error creating building plan:', error);
    res.status(500).json({ error: 'Failed to create building plan' });
  }
});

/**
 * Update a building plan
 * @route PUT /api/buildings/plans/:id
 * @param {number} id - Building plan ID
 * @param {Object} plan - Building plan data
 * @returns {Object} - Updated building plan
 */
router.put('/plans/:id', async (req, res) => {
  try {
    const { name, description, type, tech_level, materials_cost, food_cost, gold_cost, maintenance_cost } = req.body;

    // Check if plan exists
    const plan = await db.get('SELECT * FROM building_plans WHERE id = ?', [req.params.id]);
    if (!plan) {
      return res.status(404).json({ error: 'Building plan not found' });
    }

    await db.run(
      'UPDATE building_plans SET name = ?, description = ?, type = ?, tech_level = ?, materials_cost = ?, food_cost = ?, gold_cost = ?, maintenance_cost = ? WHERE id = ?',
      [
        name || plan.name,
        description !== undefined ? description : plan.description,
        type || plan.type,
        tech_level || plan.tech_level,
        materials_cost !== undefined ? materials_cost : plan.materials_cost,
        food_cost !== undefined ? food_cost : plan.food_cost,
        gold_cost !== undefined ? gold_cost : plan.gold_cost,
        maintenance_cost !== undefined ? maintenance_cost : plan.maintenance_cost,
        req.params.id
      ]
    );

    const updatedPlan = await db.get('SELECT * FROM building_plans WHERE id = ?', [req.params.id]);
    res.json(updatedPlan);
  } catch (error) {
    console.error(`Error updating building plan ${req.params.id}:`, error);
    res.status(500).json({ error: 'Failed to update building plan' });
  }
});

/**
 * Delete a building plan
 * @route DELETE /api/buildings/plans/:id
 * @param {number} id - Building plan ID
 * @returns {Object} - Success message
 */
router.delete('/plans/:id', async (req, res) => {
  try {
    // Check if plan exists
    const plan = await db.get('SELECT * FROM building_plans WHERE id = ?', [req.params.id]);
    if (!plan) {
      return res.status(404).json({ error: 'Building plan not found' });
    }

    // Check if plan is used by any buildings
    const buildings = await db.all('SELECT * FROM buildings WHERE plan_id = ?', [req.params.id]);
    if (buildings.length > 0) {
      return res.status(400).json({ error: 'Cannot delete plan that is used by buildings' });
    }

    await db.run('DELETE FROM building_plans WHERE id = ?', [req.params.id]);
    res.json({ success: true, message: 'Building plan deleted successfully' });
  } catch (error) {
    console.error(`Error deleting building plan ${req.params.id}:`, error);
    res.status(500).json({ error: 'Failed to delete building plan' });
  }
});

module.exports = router;
