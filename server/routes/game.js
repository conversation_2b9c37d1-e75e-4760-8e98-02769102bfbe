const express = require('express');
const router = express.Router();
const db = require('../db');
const CycleService = require('../services/cycleService');
const FoodService = require('../services/foodService');
const MaterialsService = require('../services/materialsService');

// Get current game state
router.get('/state', async (req, res) => {
    try {
        // Fix any duplicate entries in food modifiers
        await FoodService.fixDuplicatePerishableEntries();

        // Update all food-related modifier tables
        await FoodService.updateFoodModifiers();

        const gameState = await db.get('SELECT * FROM game_state WHERE id = 1');
        const jobs = await db.all('SELECT * FROM jobs');

        // Update all food-related modifier tables
        await FoodService.updateFoodModifiers();

        // Get all modifier tables
        const modifierTables = await db.all('SELECT * FROM modifier_tables');

        // For each table, get its modifiers
        for (const table of modifierTables) {
            const modifiers = await db.all('SELECT * FROM modifiers WHERE table_id = ? ORDER BY id', [table.id]);
            table.modifiers = modifiers;
        }

        // Calculate current food production
        // La valeur du moral est déjà au format multiplicatif (1.0 = neutre)
        // Pour l'utiliser comme modificateur, on soustrait 1.0
        const moralModifier = gameState.moral_value - 1.0;
        console.log('Using moral modifier for food production:', moralModifier);
        const foodProduction = FoodService.calculateFoodProduction(gameState, jobs, modifierTables, moralModifier);
        const foodConsumption = FoodService.calculateFoodConsumption(jobs);

        // Update all food-related modifier tables to ensure current_value is up to date
        await FoodService.updateFoodModifiers();

        // Get updated modifier tables
        const updatedModifierTables = await db.all('SELECT * FROM modifier_tables');

        // Get perishable rate from updated tables
        const perishableRate = updatedModifierTables.find(t => t.id === 3)?.current_value || 0.2;

        console.log('Perishable rate used for calculation in /state endpoint:', perishableRate);

        // Calculate perishable loss
        const perishableLoss = Math.round((gameState.food_reserves * perishableRate) * 10) / 10;

        // Calculate net after perishable
        const net = Math.round((foodProduction - foodConsumption) * 10) / 10;
        const netAfterPerishable = Math.round((net - perishableLoss) * 10) / 10;

        // Get food-related modifiers from updated tables
        const foodModifiers = {
            generalEffects: updatedModifierTables.find(t => t.id === 1)?.current_value || 0,
            techEffects: updatedModifierTables.find(t => t.id === 2)?.current_value || 0,
            perishableRate: perishableRate
        };

        console.log('Food modifiers for state endpoint:', foodModifiers);

        // Calculate next month's season factor
        const { MONTHS, SEASONS } = require('../utils/constants');
        let nextMonthIndex = gameState.month_index + 1;
        if (nextMonthIndex >= MONTHS.length) {
            nextMonthIndex = 0;
        }

        // Determine next season and season factor based on the next month index
        let nextSeasonFactor;
        if (nextMonthIndex === 11 || nextMonthIndex < 2) {
            nextSeasonFactor = SEASONS.WINTER.factor;
        } else if (nextMonthIndex >= 2 && nextMonthIndex < 5) {
            nextSeasonFactor = SEASONS.SPRING.factor;
        } else if (nextMonthIndex >= 5 && nextMonthIndex < 8) {
            nextSeasonFactor = SEASONS.SUMMER.factor;
        } else {
            nextSeasonFactor = SEASONS.AUTUMN.factor;
        }

        console.log('Next month index:', nextMonthIndex, 'Next season factor:', nextSeasonFactor);

        // Calculate materials production
        const materialsProduction = MaterialsService.calculateMaterialsProduction(gameState, jobs, modifierTables, moralModifier);

        res.json({
            gameState,
            jobs,
            modifierTables,
            foodData: {
                production: foodProduction,
                consumption: foodConsumption,
                net: net,
                perishableLoss: perishableLoss,
                netAfterPerishable: netAfterPerishable,
                modifiers: foodModifiers,
                nextSeasonFactor: nextSeasonFactor // Add next season factor to the response
            },
            materialsData: {
                production: materialsProduction
            }
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Update game configuration
router.put('/config/:category/:name', async (req, res) => {
    const { category, name } = req.params;
    const { value } = req.body;

    try {
        await db.run(
            'UPDATE game_config SET value = ?, updated_at = CURRENT_TIMESTAMP WHERE category = ? AND name = ?',
            [value, category, name]
        );
        res.json({ success: true });
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// Get game configuration
router.get('/config', async (req, res) => {
    try {
        const config = await db.all('SELECT * FROM game_config');
        res.json(config);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// Get recent events
router.get('/events', async (req, res) => {
    try {
        // Get the current simulation_id from game_state
        const gameState = await db.get('SELECT simulation_id FROM game_state WHERE id = 1');
        const simulationId = gameState?.simulation_id || 1;

        const events = await db.all(`
            SELECT * FROM events_log
            WHERE simulation_id = ?
            ORDER BY id DESC
            LIMIT 10
        `, [simulationId]);
        res.json(events);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// Process next cycle
router.post('/cycle', async (req, res) => {
    try {
        const results = await CycleService.processCycle();

        // Get updated game state and jobs after cycle processing
        const gameState = await db.get('SELECT * FROM game_state WHERE id = 1');
        const jobs = await db.all('SELECT * FROM jobs');

        // Get the most recent events from the current cycle only
        const simulationId = gameState?.simulation_id || 1;
        const recentEvents = await db.all(`
            SELECT * FROM events_log
            WHERE cycle = ? AND simulation_id = ?
            ORDER BY id DESC
        `, [gameState.cycle_number, simulationId]);

        // Calculate next month's season factor
        const { MONTHS, SEASONS } = require('../utils/constants');
        let nextMonthIndex = gameState.month_index + 1;
        if (nextMonthIndex >= MONTHS.length) {
            nextMonthIndex = 0;
        }

        // Determine next season and season factor based on the next month index
        let nextSeasonFactor;
        if (nextMonthIndex === 11 || nextMonthIndex < 2) {
            nextSeasonFactor = SEASONS.WINTER.factor;
        } else if (nextMonthIndex >= 2 && nextMonthIndex < 5) {
            nextSeasonFactor = SEASONS.SPRING.factor;
        } else if (nextMonthIndex >= 5 && nextMonthIndex < 8) {
            nextSeasonFactor = SEASONS.SUMMER.factor;
        } else {
            nextSeasonFactor = SEASONS.AUTUMN.factor;
        }

        console.log('After cycle - Next month index:', nextMonthIndex, 'Next season factor:', nextSeasonFactor);

        res.json({
            success: true,
            gameState,
            jobs,
            events: recentEvents,
            currentCycleEvents: results.events, // Include the events that were generated during this cycle
            calculations: {
                food: {
                    production: results.production.foodProduction,
                    consumption: results.consumption.foodConsumption,
                    perishable: results.newReserves.perishableRate,
                    perishableLoss: Math.round((gameState.food_reserves * results.newReserves.perishableRate) * 10) / 10,
                    netAfterPerishable: Math.round((results.production.foodProduction - results.consumption.foodConsumption - Math.round((gameState.food_reserves * results.newReserves.perishableRate) * 10) / 10) * 10) / 10,
                    nextSeasonFactor: nextSeasonFactor // Add next season factor to the response
                },
                mining: {
                    miningProduction: results.production.miningProduction,
                    totalProduction: results.newReserves.totalMineProduction,
                    mineDepth: results.newReserves.mineDepth
                },
                trading: {
                    revenue: results.production.tradingRevenue
                },
                finances: {
                    totalRevenue: results.production.miningProduction + results.production.tradingRevenue,
                    totalSalary: results.consumption.salaries,
                    nonSalaryCharges: results.consumption.charges,
                    totalCharges: results.consumption.salaries + results.consumption.charges,
                    craftsmanEffect: results.consumption.craftsmanEffect
                },
                materials: {
                    production: results.production.materialsProduction,
                    previousAmount: gameState.materials || 0,
                    newAmount: results.newReserves.newReserves.materials || 0,
                    netChange: Math.round((results.newReserves.newReserves.materials - (gameState.materials || 0)) * 10) / 10
                },
                probabilities: {
                    sick: results.probabilities.sickness,
                    crime: results.probabilities.crime,
                    research: results.probabilities.research
                }
            }
        });
    } catch (error) {
        console.error('Error processing cycle:', error);
        res.status(500).json({ error: error.message });
    }
});

// Change job numbers (increase/decrease)
router.post('/jobs/change', async (req, res) => {
    const { jobId, change } = req.body;

    try {
        // Get the job to change
        const job = await db.get('SELECT * FROM jobs WHERE id = ?', [jobId]);
        if (!job) {
            return res.status(404).json({ success: false, error: 'Job not found' });
        }

        // Get the worker job (reserve)
        const workerJob = await db.get('SELECT * FROM jobs WHERE name = "Worker"');
        if (!workerJob) {
            return res.status(404).json({ success: false, error: 'Worker job not found' });
        }

        // Check if we have enough workers to decrease from
        if (change < 0 && job.number < Math.abs(change)) {
            return res.status(400).json({
                success: false,
                error: `Not enough ${job.name}s to decrease`
            });
        }

        // Check if we're trying to decrease the job count below the number of free workers
        if (change < 0 && job.number + change < job.free) {
            return res.status(400).json({
                success: false,
                error: `Cannot reduce ${job.name}s below the number of free workers (${job.free})`
            });
        }

        // Check if we have enough workers to increase from
        if (change > 0 && workerJob.number < change) {
            return res.status(400).json({
                success: false,
                error: 'Not enough Workers available'
            });
        }

        // Update the job
        await db.run(
            'UPDATE jobs SET number = number + ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
            [change, jobId]
        );

        // Update the worker job (opposite change)
        await db.run(
            'UPDATE jobs SET number = number - ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
            [change, workerJob.id]
        );

        // Get updated jobs
        const jobs = await db.all('SELECT * FROM jobs');

        res.json({
            success: true,
            jobs
        });
    } catch (error) {
        console.error('Error changing job:', error);
        res.status(500).json({ success: false, error: error.message });
    }
});

// Change free workers (increase/decrease)
router.post('/jobs/free', async (req, res) => {
    const { jobId, change } = req.body;

    try {
        // Get the job to change
        const job = await db.get('SELECT * FROM jobs WHERE id = ?', [jobId]);
        if (!job) {
            return res.status(404).json({ success: false, error: 'Job not found' });
        }

        // Calculate new free value
        const newFreeValue = job.free + change;

        // Validate the change
        if (newFreeValue < 0) {
            return res.status(400).json({
                success: false,
                error: 'Cannot have negative free workers'
            });
        }

        if (newFreeValue > job.number) {
            return res.status(400).json({
                success: false,
                error: 'Cannot have more free workers than total workers'
            });
        }

        // Update the job's free workers
        await db.run(
            'UPDATE jobs SET free = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
            [newFreeValue, jobId]
        );

        // Get updated jobs
        const jobs = await db.all('SELECT * FROM jobs');

        res.json({
            success: true,
            jobs
        });
    } catch (error) {
        console.error('Error changing free workers:', error);
        res.status(500).json({ success: false, error: error.message });
    }
});

// Change worker count (add/remove workers to total population)
router.post('/workers/change', async (req, res) => {
    const { change } = req.body;

    try {
        // Get the worker job
        const workerJob = await db.get('SELECT * FROM jobs WHERE name = "Worker"');
        if (!workerJob) {
            return res.status(404).json({ success: false, error: 'Worker job not found' });
        }

        // Calculate new worker value
        const newWorkerValue = workerJob.number + change;

        // Validate the change
        if (newWorkerValue < 0) {
            return res.status(400).json({
                success: false,
                error: 'Cannot have negative workers'
            });
        }

        // Update the worker job
        await db.run(
            'UPDATE jobs SET number = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
            [newWorkerValue, workerJob.id]
        );

        // Get updated jobs
        const jobs = await db.all('SELECT * FROM jobs');

        // Update game state to reflect population change
        await db.run(
            'UPDATE game_state SET updated_at = CURRENT_TIMESTAMP'
        );

        res.json({
            success: true,
            jobs
        });
    } catch (error) {
        console.error('Error changing workers:', error);
        res.status(500).json({ success: false, error: error.message });
    }
});

// Get all dashboard data in a single request
router.get('/dashboard', async (req, res) => {
    try {
        // Get game state
        const gameState = await db.get('SELECT * FROM game_state WHERE id = 1');

        // Get jobs
        const jobs = await db.all('SELECT * FROM jobs');

        // Get modifier tables
        const modifierTables = await db.all('SELECT * FROM modifier_tables');

        // Get events
        const simulationId = gameState?.simulation_id || 1;
        const events = await db.all(`
            SELECT * FROM events_log
            WHERE simulation_id = ?
            ORDER BY id DESC
            LIMIT 20
        `, [simulationId]);

        // Get all modifiers for each table
        const allModifiers = {};
        for (const table of modifierTables) {
            const modifiers = await db.all('SELECT * FROM modifiers WHERE table_id = ? ORDER BY id', [table.id]);
            allModifiers[table.id] = modifiers;
        }

        // Calculate food data
        const moralModifier = gameState.moral_value - 1.0;
        const foodProduction = FoodService.calculateFoodProduction(gameState, jobs, modifierTables, moralModifier);
        const foodConsumption = FoodService.calculateFoodConsumption(jobs);

        // Update all food-related modifier tables to ensure current_value is up to date
        await FoodService.updateFoodModifiers();

        // Get updated modifier tables
        const updatedModifierTables = await db.all('SELECT * FROM modifier_tables');

        // Get perishable rate from updated tables
        const perishableRate = updatedModifierTables.find(t => t.id === 3)?.current_value || 0.2;

        console.log('Perishable rate used for calculation:', perishableRate);

        // Calculate perishable loss
        const perishableLoss = Math.round((gameState.food_reserves * perishableRate) * 10) / 10;

        // Calculate net after perishable
        const net = Math.round((foodProduction - foodConsumption) * 10) / 10;
        const netAfterPerishable = Math.round((net - perishableLoss) * 10) / 10;

        // Get food-related modifiers
        const foodModifiers = {
            generalEffects: modifierTables.find(t => t.id === 1)?.current_value || 0,
            techEffects: modifierTables.find(t => t.id === 2)?.current_value || 0,
            perishableRate: perishableRate
        };

        // Calculate materials production
        const materialsProduction = MaterialsService.calculateMaterialsProduction(gameState, jobs, modifierTables, moralModifier);

        // Return all data
        res.json({
            success: true,
            gameState,
            jobs,
            modifierTables,
            events,
            modifiers: allModifiers,
            foodData: {
                production: foodProduction,
                consumption: foodConsumption,
                net: net,
                perishableLoss: perishableLoss,
                netAfterPerishable: netAfterPerishable,
                modifiers: foodModifiers
            },
            materialsData: {
                production: materialsProduction
            }
        });
    } catch (error) {
        console.error('Error getting dashboard data:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

module.exports = router;