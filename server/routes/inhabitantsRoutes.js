const express = require('express');
const router = express.Router();
const InhabitantsService = require('../services/inhabitantsService');

/**
 * @route GET /api/inhabitants
 * @description Get all inhabitants with pagination and filters
 * @access Public
 */
router.get('/', async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 20;

        // Extract filter parameters
        const filters = {
            job: req.query.job,
            race: req.query.race,
            gender: req.query.gender,
            isPC: req.query.isPC,
            isSick: req.query.isSick,
            searchQuery: req.query.searchQuery,
            sortBy: req.query.sortBy,
            sortOrder: req.query.sortOrder
        };

        const result = await InhabitantsService.getInhabitants(page, limit, filters);

        res.json({
            success: true,
            inhabitants: result.inhabitants,
            total: result.total,
            page: result.page,
            limit: result.limit,
            totalPages: result.totalPages
        });
    } catch (error) {
        console.error('Error in GET /inhabitants:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to retrieve inhabitants',
            error: error.message
        });
    }
});

/**
 * @route GET /api/inhabitants/stats
 * @description Get statistics about inhabitants
 * @access Public
 */
router.get('/stats', async (req, res) => {
    try {
        const stats = await InhabitantsService.getStatistics();

        res.json({
            success: true,
            stats
        });
    } catch (error) {
        console.error('Error in GET /inhabitants/stats:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to retrieve inhabitant statistics',
            error: error.message
        });
    }
});

/**
 * @route PUT /api/inhabitants/sync-jobs
 * @description Synchronize inhabitants with jobs
 * @access Public
 */
router.put('/sync-jobs', async (req, res) => {
    try {
        // First, synchronize job counts with inhabitants
        await InhabitantsService.syncJobCountsWithInhabitants();

        // Then, synchronize inhabitants with jobs
        const results = await InhabitantsService.syncWithJobs();

        res.json({
            success: true,
            results,
            message: 'Inhabitants synchronized with jobs successfully'
        });
    } catch (error) {
        console.error('Error in PUT /inhabitants/sync-jobs:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to synchronize inhabitants with jobs',
            error: error.message
        });
    }
});

/**
 * @route GET /api/inhabitants/:id
 * @description Get a specific inhabitant by ID
 * @access Public
 */
router.get('/:id', async (req, res) => {
    try {
        const id = parseInt(req.params.id);
        const inhabitant = await InhabitantsService.getInhabitant(id);

        res.json({
            success: true,
            inhabitant
        });
    } catch (error) {
        console.error(`Error in GET /inhabitants/${req.params.id}:`, error);
        res.status(error.message === 'Inhabitant not found' ? 404 : 500).json({
            success: false,
            message: error.message,
            error: error.message
        });
    }
});

/**
 * @route POST /api/inhabitants
 * @description Create a new inhabitant
 * @access Public
 */
router.post('/', async (req, res) => {
    try {
        const inhabitant = await InhabitantsService.createInhabitant(req.body);

        res.status(201).json({
            success: true,
            inhabitant,
            message: 'Inhabitant created successfully'
        });
    } catch (error) {
        console.error('Error in POST /inhabitants:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to create inhabitant',
            error: error.message
        });
    }
});

/**
 * @route PUT /api/inhabitants/:id
 * @description Update an existing inhabitant
 * @access Public
 */
router.put('/:id', async (req, res) => {
    try {
        const id = parseInt(req.params.id);

        // Log the request body for debugging
        console.log('PUT /inhabitants/:id - Request body:', {
            id: id,
            body: req.body
        });

        const inhabitant = await InhabitantsService.updateInhabitant(id, req.body);

        // If the job_id was changed, recalculate the sickness probability
        // This is especially important when changing healers
        if (req.body.job_id !== undefined) {
            // Import HealthService to recalculate sickness probability
            const HealthService = require('../services/healthService');
            await HealthService.recalculateSickProbability();
            console.log('Recalculated sickness probability after inhabitant job change');
        }

        res.json({
            success: true,
            inhabitant,
            message: 'Inhabitant updated successfully'
        });
    } catch (error) {
        console.error(`Error in PUT /inhabitants/${req.params.id}:`, error);
        res.status(error.message === 'Inhabitant not found' ? 404 : 500).json({
            success: false,
            message: error.message,
            error: error.message
        });
    }
});

/**
 * @route DELETE /api/inhabitants/:id
 * @description Delete an inhabitant
 * @access Public
 */
router.delete('/:id', async (req, res) => {
    try {
        const id = parseInt(req.params.id);

        // Get the inhabitant before deleting to check if it's a healer
        const inhabitant = await InhabitantsService.getInhabitant(id);
        const isHealer = inhabitant && inhabitant.job_id ===
            (await InhabitantsService.getJobIdByName('Healer'));

        const success = await InhabitantsService.deleteInhabitant(id);

        if (success) {
            // If the deleted inhabitant was a healer, recalculate the sickness probability
            if (isHealer) {
                const HealthService = require('../services/healthService');
                await HealthService.recalculateSickProbability();
                console.log('Recalculated sickness probability after healer deletion');
            }

            res.json({
                success: true,
                message: 'Inhabitant deleted successfully'
            });
        } else {
            res.status(404).json({
                success: false,
                message: 'Inhabitant not found'
            });
        }
    } catch (error) {
        console.error(`Error in DELETE /inhabitants/${req.params.id}:`, error);
        res.status(500).json({
            success: false,
            message: 'Failed to delete inhabitant',
            error: error.message
        });
    }
});

module.exports = router;
