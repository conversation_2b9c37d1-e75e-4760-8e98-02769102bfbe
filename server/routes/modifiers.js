const express = require('express');
const router = express.Router();
const db = require('../db');
const FoodService = require('../services/foodService');
const MoralService = require('../services/moralService');
const MiningService = require('../services/miningService');
const MaterialsService = require('../services/materialsService');
const ChargesService = require('../services/chargesService');
const HealthService = require('../services/healthService');
const ResearchService = require('../services/researchService');
const TradingService = require('../services/tradingService');
const JusticeDefenseService = require('../services/justiceDefenseService');
const RenownService = require('../services/renownService');

// Get all modifiers for a specific table with table details
router.get('/table/:tableId', async (req, res) => {
    try {
        const tableId = req.params.tableId;

        // Get table information
        const table = await db.get('SELECT * FROM modifier_tables WHERE id = ?', [tableId]);

        if (!table) {
            return res.status(404).json({
                success: false,
                error: 'Modifier table not found'
            });
        }

        // Get modifiers for this table
        const modifiers = await db.all(
            'SELECT * FROM modifiers WHERE table_id = ? ORDER BY id',
            [tableId]
        );

        // If this is a food-related table, update its value first
        if (tableId >= 1 && tableId <= 3) {
            await FoodService.updateModifierTableSum(tableId);
            // Get the updated table
            const updatedTable = await db.get('SELECT * FROM modifier_tables WHERE id = ?', [tableId]);
            table.current_value = updatedTable.current_value;
        }

        res.json({
            success: true,
            table,
            modifiers,
            total: table.current_value
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Add new modifier
router.post('/table/:tableId', async (req, res) => {
    const { title, effect, description } = req.body;
    const tableId = parseInt(req.params.tableId);

    try {
        // Validate input
        if (!title || effect === undefined) {
            return res.status(400).json({
                success: false,
                error: 'Missing required fields'
            });
        }

        // Insert new modifier
        const result = await db.run(
            'INSERT INTO modifiers (table_id, title, effect, description) VALUES (?, ?, ?, ?)',
            [tableId, title, effect, description]
        );

        // If this is a food-related table, use FoodService to update
        if (tableId >= 1 && tableId <= 3) {
            await FoodService.updateModifierTableSum(tableId);
        } else if (tableId === 13) {
            // If this is the health modifiers table, use HealthService to update
            await HealthService.updateHealthModifiers();
        } else if (tableId === 16) {
            // If this is the research modifiers table, use ResearchService to update
            await ResearchService.updateResearchModifiers();
        } else if (tableId === 17) {
            // If this is the renown modifiers table, use RenownService to update
            await RenownService.updateRenownModifiers();
        } else if (tableId === 18) {
            // If this is the trading modifiers table, use TradingService to update
            await TradingService.updateTradingModifiers();
        } else {
            // For other tables, update directly
            await db.run(`
                UPDATE modifier_tables
                SET current_value = (
                    SELECT SUM(effect)
                    FROM modifiers
                    WHERE table_id = ?
                ),
                updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            `, [tableId, tableId]);
        }

        // Get updated table and modifiers
        const table = await db.get('SELECT * FROM modifier_tables WHERE id = ?', [tableId]);
        const modifiers = await db.all(
            'SELECT * FROM modifiers WHERE table_id = ? ORDER BY id',
            [tableId]
        );

        res.json({
            success: true,
            id: result.lastID,
            table,
            modifiers,
            total: table.current_value
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Update modifier
router.put('/:id', async (req, res) => {
    const { title, effect, description } = req.body;
    const modifierId = parseInt(req.params.id);

    try {
        // Validate input
        if (!title || effect === undefined) {
            return res.status(400).json({
                success: false,
                error: 'Missing required fields'
            });
        }

        // Get the modifier to check its table_id
        const modifier = await db.get('SELECT * FROM modifiers WHERE id = ?', [modifierId]);

        if (!modifier) {
            return res.status(404).json({
                success: false,
                error: 'Modifier not found'
            });
        }

        const tableId = modifier.table_id;

        // Update the modifier
        await db.run(
            'UPDATE modifiers SET title = ?, effect = ?, description = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
            [title, effect, description, modifierId]
        );

        // If this is a food-related table, use FoodService to update
        if (tableId >= 1 && tableId <= 3) {
            await FoodService.updateModifierTableSum(tableId);
        } else if (tableId === 13) {
            // If this is the health modifiers table, use HealthService to update
            await HealthService.updateHealthModifiers();
        } else if (tableId === 16) {
            // If this is the research modifiers table, use ResearchService to update
            await ResearchService.updateResearchModifiers();
        } else if (tableId === 17) {
            // If this is the renown modifiers table, use RenownService to update
            await RenownService.updateRenownModifiers();
        } else if (tableId === 18) {
            // If this is the trading modifiers table, use TradingService to update
            await TradingService.updateTradingModifiers();
        } else if (tableId === 19 || tableId === 20) {
            // If this is a justice or defense related table, use JusticeDefenseService to update
            await JusticeDefenseService.updateJusticeDefenseModifiers();
        } else {
            // For other tables, update directly
            await db.run(`
                UPDATE modifier_tables
                SET current_value = (
                    SELECT SUM(effect)
                    FROM modifiers
                    WHERE table_id = ?
                ),
                updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            `, [tableId, tableId]);
        }

        // Get updated table and modifiers
        const table = await db.get('SELECT * FROM modifier_tables WHERE id = ?', [tableId]);
        const modifiers = await db.all(
            'SELECT * FROM modifiers WHERE table_id = ? ORDER BY id',
            [tableId]
        );

        res.json({
            success: true,
            table,
            modifiers,
            total: table.current_value
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Delete modifier
router.delete('/:id', async (req, res) => {
    const modifierId = parseInt(req.params.id);

    try {
        // Get the modifier to check its table_id
        const modifier = await db.get('SELECT * FROM modifiers WHERE id = ?', [modifierId]);

        if (!modifier) {
            return res.status(404).json({
                success: false,
                error: 'Modifier not found'
            });
        }

        const tableId = modifier.table_id;

        // Don't allow deletion of the base perishable rate entry
        if (tableId === 3 && modifier.title === 'Base') {
            return res.status(400).json({
                success: false,
                error: 'Cannot delete the base perishable rate'
            });
        }

        // Delete the modifier
        await db.run('DELETE FROM modifiers WHERE id = ?', [modifierId]);

        // If this is a food-related table, use FoodService to update
        if (tableId >= 1 && tableId <= 3) {
            await FoodService.updateModifierTableSum(tableId);
        } else if (tableId === 13) {
            // If this is the health modifiers table, use HealthService to update
            await HealthService.updateHealthModifiers();
        } else if (tableId === 16) {
            // If this is the research modifiers table, use ResearchService to update
            await ResearchService.updateResearchModifiers();
        } else if (tableId === 17) {
            // If this is the renown modifiers table, use RenownService to update
            await RenownService.updateRenownModifiers();
        } else if (tableId === 18) {
            // If this is the trading modifiers table, use TradingService to update
            await TradingService.updateTradingModifiers();
        } else if (tableId === 19 || tableId === 20) {
            // If this is a justice or defense related table, use JusticeDefenseService to update
            await JusticeDefenseService.updateJusticeDefenseModifiers();
        } else {
            // For other tables, update directly
            await db.run(`
                UPDATE modifier_tables
                SET current_value = (
                    SELECT SUM(effect)
                    FROM modifiers
                    WHERE table_id = ?
                ),
                updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            `, [tableId, tableId]);
        }

        // Get updated table and modifiers
        const table = await db.get('SELECT * FROM modifier_tables WHERE id = ?', [tableId]);
        const modifiers = await db.all(
            'SELECT * FROM modifiers WHERE table_id = ? ORDER BY id',
            [tableId]
        );

        res.json({
            success: true,
            table,
            modifiers,
            total: table.current_value
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Test route
router.get('/test', (req, res) => {
    res.json({ message: 'Test route works!' });
});

// Get all food-related modifiers (tables 1-3)
router.get('/food', async (req, res) => {
    try {
        // Fix any duplicate entries first
        await FoodService.fixDuplicatePerishableEntries();

        // Update all food-related modifier tables
        await FoodService.updateFoodModifiers();

        // Get all food-related tables
        const tables = await db.all('SELECT * FROM modifier_tables WHERE id IN (1, 2, 3)');

        // Get modifiers for each table
        const foodData = await Promise.all(tables.map(async (table) => {
            const modifiers = await db.all(
                'SELECT * FROM modifiers WHERE table_id = ? ORDER BY id',
                [table.id]
            );

            return {
                table,
                modifiers,
                total: table.current_value
            };
        }));

        res.json({
            success: true,
            foodData
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Get all moral-related modifiers and stats
router.get('/moral', async (req, res) => {
    try {
        const moralData = await MoralService.getMoralData();

        res.json({
            success: true,
            moralData
        });
    } catch (error) {
        console.error('Error in /moral route:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Get all mining-related modifiers and stats
router.get('/mining', async (req, res) => {
    try {
        const miningData = await MiningService.getMiningData();

        res.json({
            success: true,
            miningData
        });
    } catch (error) {
        console.error('Error in /mining route:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Get all materials-related modifiers and stats
router.get('/materials', async (req, res) => {
    try {
        const materialsData = await MaterialsService.getMaterialsData();

        res.json({
            success: true,
            materialsData
        });
    } catch (error) {
        console.error('Error in /materials route:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Get all charges-related modifiers and stats
router.get('/charges', async (req, res) => {
    try {
        const chargesData = await ChargesService.getChargesData();

        res.json({
            success: true,
            chargesData
        });
    } catch (error) {
        console.error('Error in /charges route:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Get all health-related modifiers and stats
router.get('/health', async (req, res) => {
    try {
        const healthData = await HealthService.getHealthData();

        res.json({
            success: true,
            healthData
        });
    } catch (error) {
        console.error('Error in /health route:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Get all research-related modifiers and stats
router.get('/research', async (req, res) => {
    try {
        const researchData = await ResearchService.getResearchData();

        res.json({
            success: true,
            researchData
        });
    } catch (error) {
        console.error('Error in /research route:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Force recalculation of research probability
router.post('/research/recalculate', async (req, res) => {
    try {
        const probability = await ResearchService.recalculateResearchProbability();

        res.json({
            success: true,
            probability
        });
    } catch (error) {
        console.error('Error recalculating research probability:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Get all trading-related modifiers and stats
router.get('/trading', async (req, res) => {
    try {
        const tradingData = await TradingService.getTradingData();

        res.json({
            success: true,
            tradingData
        });
    } catch (error) {
        console.error('Error in /trading route:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Get all justice and defense related modifiers and stats
router.get('/justice-defense', async (req, res) => {
    try {
        const justiceDefenseData = await JusticeDefenseService.getJusticeDefenseData();

        res.json({
            success: true,
            justiceDefenseData
        });
    } catch (error) {
        console.error('Error in /justice-defense route:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Get all renown-related modifiers and stats
router.get('/renown', async (req, res) => {
    try {
        const renownData = await RenownService.getRenownData();

        res.json({
            success: true,
            renownData
        });
    } catch (error) {
        console.error('Error in /renown route:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Get all modifiers for all tables in a single request
router.get('/all', async (req, res) => {
    try {
        // Get all modifier tables
        const tables = await db.all('SELECT * FROM modifier_tables');

        // Get modifiers for each table
        const modifiers = {};

        for (const table of tables) {
            const tableModifiers = await db.all(
                'SELECT * FROM modifiers WHERE table_id = ? ORDER BY id',
                [table.id]
            );

            // Use table name as key for better readability
            modifiers[table.name.toLowerCase().replace(/\s+/g, '_')] = tableModifiers;
        }

        res.json({
            success: true,
            modifiers
        });
    } catch (error) {
        console.error('Error getting all modifiers:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Get all defense means
router.get('/defense-means', async (req, res) => {
    try {
        const defenseMeans = await JusticeDefenseService.getDefenseMeans();

        // Ensure defenseMeans is always an array
        const defenseMeansArray = Array.isArray(defenseMeans) ? defenseMeans : [];

        console.log('Sending defense means:', defenseMeansArray);

        res.json({
            success: true,
            defenseMeans: defenseMeansArray
        });
    } catch (error) {
        console.error('Error getting defense means:', error);
        res.status(500).json({
            success: false,
            error: error.message,
            defenseMeans: [] // Toujours renvoyer un tableau vide en cas d'erreur
        });
    }
});

// Get all renown-related modifiers and stats
router.get('/renown', async (req, res) => {
    try {
        const renownData = await RenownService.getRenownData();

        res.json({
            success: true,
            renownData
        });
    } catch (error) {
        console.error('Error in /renown route:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Add a new defense mean
router.post('/defense-means', async (req, res) => {
    const { title, description } = req.body;

    try {
        // Validate input
        if (!title) {
            return res.status(400).json({
                success: false,
                error: 'Title is required'
            });
        }

        // Add the defense mean
        const defenseMean = await JusticeDefenseService.addDefenseMean(title, description || '');

        res.status(201).json({
            success: true,
            defenseMean
        });
    } catch (error) {
        console.error('Error adding defense mean:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Update a defense mean
router.put('/defense-means/:id', async (req, res) => {
    const { title, description } = req.body;
    const id = parseInt(req.params.id);

    try {
        // Validate input
        if (!title) {
            return res.status(400).json({
                success: false,
                error: 'Title is required'
            });
        }

        // Update the defense mean
        const defenseMean = await JusticeDefenseService.updateDefenseMean(id, title, description || '');

        if (!defenseMean) {
            return res.status(404).json({
                success: false,
                error: 'Defense mean not found'
            });
        }

        res.json({
            success: true,
            defenseMean
        });
    } catch (error) {
        console.error('Error updating defense mean:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Delete a defense mean
router.delete('/defense-means/:id', async (req, res) => {
    const id = parseInt(req.params.id);

    try {
        // Delete the defense mean
        await JusticeDefenseService.deleteDefenseMean(id);

        res.json({
            success: true,
            message: 'Defense mean deleted successfully'
        });
    } catch (error) {
        console.error('Error deleting defense mean:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

module.exports = router;
