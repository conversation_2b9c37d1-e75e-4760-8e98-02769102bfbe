const express = require('express');
const router = express.Router();
const db = require('../db');
const InhabitantsService = require('../services/inhabitantsService');

// Liste des métiers spécialistes qui ne peuvent pas être modifiés via les boutons +/-
const SPECIALIST_JOBS = ['Scholar', 'Healer', 'Engineer', 'Protector', 'Craftsman'];

/**
 * Transfère un habitant d'un métier à un autre
 * POST /api/job-transfer
 * Body: { fromJobId, toJobId, count }
 */
router.post('/', async (req, res) => {
    const { fromJobId, toJobId, count = 1 } = req.body;
    
    try {
        // Vérifier que les métiers existent
        const fromJob = await db.get('SELECT * FROM jobs WHERE id = ?', [fromJobId]);
        const toJob = await db.get('SELECT * FROM jobs WHERE id = ?', [toJobId]);
        
        if (!fromJob || !toJob) {
            return res.status(404).json({
                success: false,
                message: 'Un ou plusieurs métiers non trouvés'
            });
        }
        
        // Vérifier si le métier source est un spécialiste
        if (SPECIALIST_JOBS.includes(fromJob.name) && fromJob.name !== 'Worker') {
            return res.status(400).json({
                success: false,
                message: `Les ${fromJob.name} sont des spécialistes et ne peuvent pas être réaffectés via cette méthode`
            });
        }
        
        // Vérifier si le métier de destination est un spécialiste
        if (SPECIALIST_JOBS.includes(toJob.name) && toJob.name !== 'Worker') {
            return res.status(400).json({
                success: false,
                message: `Les ${toJob.name} sont des spécialistes et ne peuvent pas être ajoutés via cette méthode`
            });
        }
        
        // Trouver les habitants à transférer
        const inhabitants = await db.all(`
            SELECT * FROM inhabitants 
            WHERE job_id = ? 
            ORDER BY id DESC
            LIMIT ?
        `, [fromJobId, count]);
        
        if (inhabitants.length < count) {
            return res.status(400).json({
                success: false,
                message: `Il n'y a pas assez de ${fromJob.name} disponibles pour le transfert`
            });
        }
        
        // Effectuer le transfert pour chaque habitant
        const transferredInhabitants = [];
        for (const inhabitant of inhabitants) {
            await db.run(`
                UPDATE inhabitants 
                SET job_id = ?, updated_at = CURRENT_TIMESTAMP 
                WHERE id = ?
            `, [toJobId, inhabitant.id]);
            
            // Récupérer l'habitant mis à jour
            const updatedInhabitant = await db.get('SELECT * FROM inhabitants WHERE id = ?', [inhabitant.id]);
            transferredInhabitants.push(updatedInhabitant);
        }
        
        // Mettre à jour les compteurs de métiers
        await InhabitantsService.syncJobCountsWithInhabitants();
        
        // Récupérer les métiers mis à jour
        const updatedJobs = await db.all('SELECT * FROM jobs');
        
        res.json({
            success: true,
            message: `${count} habitant(s) transféré(s) de ${fromJob.name} à ${toJob.name}`,
            transferredInhabitants,
            jobs: updatedJobs
        });
    } catch (error) {
        console.error('Erreur lors du transfert de métier:', error);
        res.status(500).json({
            success: false,
            message: 'Erreur lors du transfert de métier',
            error: error.message
        });
    }
});

module.exports = router;
