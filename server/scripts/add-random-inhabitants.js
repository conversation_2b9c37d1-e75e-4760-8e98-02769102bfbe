const db = require('../db');
const InhabitantsService = require('../services/inhabitantsService');

async function addRandomInhabitants(count = 20) {
  try {
    console.log(`Adding ${count} random inhabitants...`);
    
    // Get all jobs
    const jobs = await db.all('SELECT * FROM jobs');
    
    // Define races and genders
    const races = ['Dwarf', 'Human', 'Elf', 'Halfling', 'Gnome'];
    const genders = ['Male', 'Female'];
    
    // Create random inhabitants
    for (let i = 0; i < count; i++) {
      // Select random job, race, and gender
      const randomJob = jobs[Math.floor(Math.random() * jobs.length)];
      const randomRace = races[Math.floor(Math.random() * races.length)];
      const randomGender = genders[Math.floor(Math.random() * genders.length)];
      
      // Create inhabitant
      const inhabitant = await InhabitantsService.createInhabitant({
        race: randomRace,
        gender: randomGender,
        job_id: randomJob.id,
        is_pc: Math.random() < 0.1 ? 1 : 0, // 10% chance to be a PC
        is_sick: Math.random() < 0.05 ? 1 : 0 // 5% chance to be sick
      });
      
      console.log(`Created inhabitant: ${inhabitant.first_name} ${inhabitant.last_name} (${inhabitant.race}, ${inhabitant.gender}, ${randomJob.name})`);
    }
    
    console.log(`Successfully added ${count} random inhabitants.`);
    
    // Get statistics
    const stats = await InhabitantsService.getStatistics();
    console.log('Current population statistics:', stats);
    
  } catch (error) {
    console.error('Error adding random inhabitants:', error);
  } finally {
    process.exit(0);
  }
}

// Run the script with the specified count
const count = process.argv[2] ? parseInt(process.argv[2]) : 20;
addRandomInhabitants(count);
