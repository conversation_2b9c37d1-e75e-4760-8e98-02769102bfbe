const path = require('path');
const fs = require('fs');
const db = require('./index');
const { initializeGameData } = require('./init-data');

async function initializeDatabase() {
    try {
        // Read schema file
        const schemaPath = path.join(__dirname, 'schema.sql');
        const schema = fs.readFileSync(schemaPath, 'utf8');

        // Execute schema
        await db.exec(schema);

        console.log('Database schema initialized');

        // Create a view for modifiers_tables if it exists
        try {
            // Check if modifiers_tables exists
            const tableExists = await db.get("SELECT name FROM sqlite_master WHERE type='table' AND name='modifiers_tables'");

            if (tableExists) {
                // Create a view for modifiers_tables
                await db.exec("DROP VIEW IF EXISTS modifier_tables");
                await db.exec("CREATE VIEW modifier_tables AS SELECT * FROM modifiers_tables");
                console.log('Created view for modifiers_tables');
            }
        } catch (viewError) {
            console.error('Error creating view:', viewError);
        }

        // Initialize game data
        await initializeGameData(db);
        console.log('Game data initialized');

        return db;
    } catch (error) {
        console.error('Error initializing database:', error);
        throw error;
    }
}

module.exports = initializeDatabase;