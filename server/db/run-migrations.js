const addArmyStrengthColumn = require('./migrations/add_army_strength');
const addJusticeArmyModifiersColumns = require('./migrations/add_justice_army_modifiers');
const createInhabitantsTable = require('./migrations/create_inhabitants_table');
const createDefenseMeansTable = require('../migrations/create_defense_means_table');
const createBuildingsTables = require('../migrations/create_buildings_tables');

async function runMigrations() {
    try {
        console.log('Running database migrations...');

        // Run migrations in order
        await addArmyStrengthColumn();
        await addJusticeArmyModifiersColumns();
        await createInhabitantsTable();
        await createDefenseMeansTable();
        await createBuildingsTables();

        console.log('All migrations completed successfully');
    } catch (error) {
        console.error('Error running migrations:', error);
        process.exit(1);
    }
}

// Run migrations if this file is executed directly
if (require.main === module) {
    runMigrations();
}

module.exports = runMigrations;
