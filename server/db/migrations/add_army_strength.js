const db = require('../index');

async function addArmyStrengthColumn() {
    try {
        // Check if the column already exists
        const tableInfo = await db.all("PRAGMA table_info(game_state)");
        const columnExists = tableInfo.some(column => column.name === 'army_strength');
        
        if (!columnExists) {
            // Add the army_strength column if it doesn't exist
            await db.run("ALTER TABLE game_state ADD COLUMN army_strength REAL DEFAULT 0");
            console.log('Added army_strength column to game_state table');
        } else {
            console.log('army_strength column already exists in game_state table');
        }
    } catch (error) {
        console.error('Error adding army_strength column:', error);
        throw error;
    }
}

module.exports = addArmyStrengthColumn;
