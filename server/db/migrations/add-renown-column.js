const sqlite = require('sqlite');
const sqlite3 = require('sqlite3');
const path = require('path');

async function migrate() {
    try {
        // Open the database
        const db = await sqlite.open({
            filename: path.join(__dirname, '../mine.db'),
            driver: sqlite3.Database
        });

        console.log('Adding renown column to game_state table...');

        // Add the renown column to the game_state table
        await db.run(`
            ALTER TABLE game_state
            ADD COLUMN renown INTEGER DEFAULT 0
        `);

        console.log('Migration completed successfully!');
        await db.close();
    } catch (error) {
        console.error('Migration failed:', error);
    }
}

migrate();
