const db = require('../index');

async function addJusticeArmyModifiersColumns() {
    try {
        // Check if the justice_modifier column already exists
        const tableInfo = await db.all("PRAGMA table_info(game_state)");
        const justiceModifierExists = tableInfo.some(column => column.name === 'justice_modifier');
        const armyModifierExists = tableInfo.some(column => column.name === 'army_modifier');
        
        if (!justiceModifierExists) {
            // Add the justice_modifier column if it doesn't exist
            await db.run("ALTER TABLE game_state ADD COLUMN justice_modifier REAL DEFAULT 0");
            console.log('Added justice_modifier column to game_state table');
        } else {
            console.log('justice_modifier column already exists in game_state table');
        }

        if (!armyModifierExists) {
            // Add the army_modifier column if it doesn't exist
            await db.run("ALTER TABLE game_state ADD COLUMN army_modifier REAL DEFAULT 0");
            console.log('Added army_modifier column to game_state table');
        } else {
            console.log('army_modifier column already exists in game_state table');
        }
    } catch (error) {
        console.error('Error adding justice and army modifier columns:', error);
        throw error;
    }
}

module.exports = addJusticeArmyModifiersColumns;
