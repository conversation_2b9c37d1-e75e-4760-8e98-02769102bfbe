const db = require('../index');

async function createInhabitantsTable() {
    try {
        // Check if the inhabitants table already exists
        const tableExists = await db.get("SELECT name FROM sqlite_master WHERE type='table' AND name='inhabitants'");
        
        if (!tableExists) {
            // Create the inhabitants table
            await db.run(`
                CREATE TABLE IF NOT EXISTS inhabitants (
                    id INTEGER PRIMARY KEY,
                    first_name TEXT NOT NULL,
                    last_name TEXT NOT NULL,
                    race TEXT NOT NULL,
                    gender TEXT NOT NULL,
                    job_id INTEGER,
                    is_pc BOOLEAN DEFAULT 0,
                    is_sick BOOLEAN DEFAULT 0,
                    short_description TEXT,
                    history TEXT,
                    age INTEGER NOT NULL,
                    arrival_cycle INTEGER NOT NULL,
                    arrival_month TEXT NOT NULL,
                    arrival_year INTEGER NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    <PERSON>OR<PERSON>GN KEY (job_id) REFERENCES jobs(id)
                )
            `);
            
            // Create indexes for better performance
            await db.run("CREATE INDEX idx_inhabitants_job_id ON inhabitants(job_id)");
            await db.run("CREATE INDEX idx_inhabitants_race ON inhabitants(race)");
            await db.run("CREATE INDEX idx_inhabitants_is_sick ON inhabitants(is_sick)");
            
            console.log('Created inhabitants table and indexes');
        } else {
            console.log('Inhabitants table already exists');
        }
    } catch (error) {
        console.error('Error creating inhabitants table:', error);
        throw error;
    }
}

module.exports = createInhabitantsTable;
