-- Base tables for game mechanics
CREATE TABLE IF NOT EXISTS game_state (
    id INTEGER PRIMARY KEY,
    cycle_number INTEGER DEFAULT 1,
    month TEXT DEFAULT 'Rubis',
    month_index INTEGER DEFAULT 0,
    year INTEGER DEFAULT 1326,
    season TEXT DEFAULT 'Hiver',
    season_factor REAL DEFAULT 0.5,
    month_description TEXT,
    treasure REAL DEFAULT 1000,
    food_reserves REAL DEFAULT 100,
    materials REAL DEFAULT 100,
    total_mine_production REAL DEFAULT 0,
    mine_depth INTEGER DEFAULT 0,
    dwellings INTEGER DEFAULT 10,
    moral_value REAL DEFAULT 1.0,
    moral_title TEXT DEFAULT 'Neutre',
    total_renown INTEGER DEFAULT 0,
    tech_level INTEGER DEFAULT 0,
    sick_probability REAL DEFAULT 0,
    crime_probability REAL DEFAULT 0,
    research_probability REAL DEFAULT 0,
    army_strength REAL DEFAULT 0,
    justice_modifier REAL DEFAULT 0,
    army_modifier REAL DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Jobs management
CREATE TABLE IF NOT EXISTS jobs (
    id INTEGER PRIMARY KEY,
    name TEXT NOT NULL,
    number INTEGER DEFAULT 0,
    salary REAL NOT NULL,
    free INTEGER DEFAULT 0,
    sick INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Modifier tables (categories)
CREATE TABLE IF NOT EXISTS modifier_tables (
    id INTEGER PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    current_value REAL DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Individual modifiers
CREATE TABLE IF NOT EXISTS modifiers (
    id INTEGER PRIMARY KEY,
    table_id INTEGER NOT NULL,
    title TEXT NOT NULL,
    effect REAL DEFAULT 0,
    description TEXT,
    start_date TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (table_id) REFERENCES modifier_tables(id)
);

-- Game events log
CREATE TABLE IF NOT EXISTS events_log (
    id INTEGER PRIMARY KEY,
    cycle INTEGER NOT NULL,
    event_type TEXT NOT NULL,
    description TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Game configuration (for editable constants)
CREATE TABLE IF NOT EXISTS game_config (
    id INTEGER PRIMARY KEY,
    category TEXT NOT NULL,
    name TEXT NOT NULL,
    value TEXT NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Defense means (for justice and defense tab)
CREATE TABLE IF NOT EXISTS defense_means (
    id INTEGER PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);