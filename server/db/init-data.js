const { MONTHS, SEASONS, MODIFIER_TABLES, JOBS, DEFAULT_VALUES } = require('../utils/constants');

async function initializeGameData(db) {
    try {
        // Initialize game state if not exists
        const gameState = await db.get('SELECT * FROM game_state WHERE id = 1');
        if (!gameState) {
            await db.run('INSERT INTO game_state (id) VALUES (1)');
        }

        // Initialize jobs
        const existingJobs = await db.all('SELECT * FROM jobs');
        if (existingJobs.length === 0) {
            for (const job of JOBS) {
                await db.run(
                    'INSERT INTO jobs (name, salary) VALUES (?, ?)',
                    [job.name, job.salary]
                );
            }
        }

        // Initialize modifier tables
        const existingTables = await db.all('SELECT * FROM modifier_tables');
        if (existingTables.length === 0) {
            for (const table of MODIFIER_TABLES) {
                await db.run(
                    'INSERT INTO modifier_tables (id, name, description) VALUES (?, ?, ?)',
                    [table.id, table.name, table.description]
                );
            }

            // Add base perishable modifier
            await db.run(
                'INSERT INTO modifiers (table_id, title, effect, description) VALUES (?, ?, ?, ?)',
                [3, 'Base', DEFAULT_VALUES.PERISHABLE_RATE, 'Péremption naturelle']
            );

            // Update the table's current_value
            await db.run(
                'UPDATE modifier_tables SET current_value = ? WHERE id = ?',
                [DEFAULT_VALUES.PERISHABLE_RATE, 3]
            );

            // Add base materials modifiers
            await db.run(
                'INSERT INTO modifiers (table_id, title, effect, description) VALUES (?, ?, ?, ?)',
                [11, 'Base', 0, 'Effet de base sur les matériaux']
            );

            await db.run(
                'INSERT INTO modifiers (table_id, title, effect, description) VALUES (?, ?, ?, ?)',
                [12, 'Base', 0, 'Effet technologique de base sur les matériaux']
            );

            // Add base charges modifiers
            await db.run(
                'INSERT INTO modifiers (table_id, title, effect, description) VALUES (?, ?, ?, ?)',
                [7, 'Entretien de la mine', 50, 'Coûts généraux de base pour l\'entretien de la mine']
            );

            await db.run(
                'INSERT INTO modifiers (table_id, title, effect, description) VALUES (?, ?, ?, ?)',
                [8, 'Base', 0, 'Modificateur général de base des charges']
            );

            // Nous n'utilisons plus les modificateurs technologiques pour les charges

            // Update the materials tables' current_value
            await db.run(
                'UPDATE modifier_tables SET current_value = ? WHERE id = ?',
                [0, 11]
            );

            await db.run(
                'UPDATE modifier_tables SET current_value = ? WHERE id = ?',
                [0, 12]
            );

            // Update the charges tables' current_value
            await db.run(
                'UPDATE modifier_tables SET current_value = ? WHERE id = ?',
                [50, 7]
            );

            await db.run(
                'UPDATE modifier_tables SET current_value = ? WHERE id = ?',
                [0, 8]
            );

            // Nous n'utilisons plus la table des modificateurs technologiques pour les charges

            // Add base renown modifier
            await db.run(
                'INSERT INTO modifiers (table_id, title, effect, description) VALUES (?, ?, ?, ?)',
                [17, 'Base', 0, 'Effet de base sur la renommée']
            );

            // Update the renown table's current_value
            await db.run(
                'UPDATE modifier_tables SET current_value = ? WHERE id = ?',
                [0, 17]
            );

            // Add base research modifier
            await db.run(
                'INSERT INTO modifiers (table_id, title, effect, description) VALUES (?, ?, ?, ?)',
                [16, 'Base', 0, 'Effet de base sur la recherche']
            );

            // Update the research table's current_value
            await db.run(
                'UPDATE modifier_tables SET current_value = ? WHERE id = ?',
                [0, 16]
            );

            // Add base trading modifier
            await db.run(
                'INSERT INTO modifiers (table_id, title, effect, description) VALUES (?, ?, ?, ?)',
                [18, 'Base', 1, 'Effet de base sur le commerce']
            );

            // Update the trading table's current_value
            await db.run(
                'UPDATE modifier_tables SET current_value = ? WHERE id = ?',
                [1, 18]
            );

            // Add base justice modifier
            await db.run(
                'INSERT INTO modifiers (table_id, title, effect, description) VALUES (?, ?, ?, ?)',
                [19, 'Base', 0, 'Effet de base sur la justice']
            );

            // Update the justice table's current_value
            await db.run(
                'UPDATE modifier_tables SET current_value = ? WHERE id = ?',
                [0, 19]
            );

            // Add base army modifier
            await db.run(
                'INSERT INTO modifiers (table_id, title, effect, description) VALUES (?, ?, ?, ?)',
                [20, 'Base', 0, 'Effet de base sur l\'armée']
            );

            // Update the army table's current_value
            await db.run(
                'UPDATE modifier_tables SET current_value = ? WHERE id = ?',
                [0, 20]
            );

            // Add initial defense means
            await db.run(
                'INSERT INTO defense_means (title, description) VALUES (?, ?)',
                ['Murailles', 'Fortifications de base autour de la colonie']
            );
        }

        // Initialize game configuration
        const existingConfig = await db.all('SELECT * FROM game_config');
        if (existingConfig.length === 0) {
            const defaultConfigs = [
                { category: 'PRODUCTION', name: 'FOOD_PER_FARMER', value: DEFAULT_VALUES.FOOD_PER_FARMER.toString(), description: 'Base food production per farmer' },
                { category: 'PRODUCTION', name: 'MATERIALS_PER_WORKER', value: DEFAULT_VALUES.MATERIALS_PER_WORKER.toString(), description: 'Base materials production per worker' },
                { category: 'PRODUCTION', name: 'MATERIALS_PER_MINER', value: DEFAULT_VALUES.MATERIALS_PER_MINER.toString(), description: 'Base materials production per miner' },
                { category: 'ECONOMY', name: 'MINING_VALUE', value: DEFAULT_VALUES.MINING_VALUE.toString(), description: 'Base value of mining production' },
                { category: 'ECONOMY', name: 'TRADING_VALUE', value: DEFAULT_VALUES.TRADING_VALUE.toString(), description: 'Base value of trading' },
                { category: 'ECONOMY', name: 'BASE_CHARGES', value: DEFAULT_VALUES.BASE_CHARGES.toString(), description: 'Base charges per cycle' },
                { category: 'PRODUCTION', name: 'EFFORT_PER_WORKER', value: DEFAULT_VALUES.EFFORT_PER_WORKER.toString(), description: 'Base effort per worker' },
                { category: 'FOOD', name: 'PERISHABLE_RATE', value: DEFAULT_VALUES.PERISHABLE_RATE.toString(), description: 'Base food perish rate' }
            ];

            for (const config of defaultConfigs) {
                await db.run(
                    'INSERT INTO game_config (category, name, value, description) VALUES (?, ?, ?, ?)',
                    [config.category, config.name, config.value, config.description]
                );
            }
        }

    } catch (error) {
        console.error('Error initializing game data:', error);
        throw error;
    }
}

module.exports = { initializeGameData };