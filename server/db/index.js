const sqlite3 = require('sqlite3').verbose();
const { open } = require('sqlite');
const dbConfig = require('../config/database');

let db;

async function getDatabase() {
    if (db) return db;

    db = await open({
        filename: dbConfig.dbPath,
        driver: sqlite3.Database
    });

    // Create a view for modifiers_tables if it exists
    try {
        // Check if modifiers_tables exists
        const tableExists = await db.get("SELECT name FROM sqlite_master WHERE type='table' AND name='modifiers_tables'");

        if (tableExists) {
            // Create a view for modifiers_tables
            await db.exec("DROP VIEW IF EXISTS modifier_tables");
            await db.exec("CREATE VIEW modifier_tables AS SELECT * FROM modifiers_tables");
            console.log('Created view for modifiers_tables');
        }
    } catch (viewError) {
        console.error('Error creating view:', viewError);
    }

    return db;
}

module.exports = {
    get: async (sql, params = []) => {
        const database = await getDatabase();
        return database.get(sql, params);
    },
    all: async (sql, params = []) => {
        const database = await getDatabase();
        return database.all(sql, params);
    },
    run: async (sql, params = []) => {
        const database = await getDatabase();
        return database.run(sql, params);
    },
    exec: async (sql) => {
        const database = await getDatabase();
        return database.exec(sql);
    },
    close: async () => {
        if (db) {
            await db.close();
            db = null;
        }
    }
};
